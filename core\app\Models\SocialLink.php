<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SocialLink extends Model
{
    use HasFactory;

    protected $table = 'social_links';

    protected $fillable = [
        'token_chain_id',
        'token_address',
        'website',
        'telegram',
        'twitter',
        'discord',
        'facebook',
        'reddit',
        'linktree',
        'presale_url',
        'whitepaper',
    ];

    /**
     * Find a social link record by chain ID and token address
     *
     * @param string $chainId
     * @param string $tokenAddress
     * @return SocialLink|null
     */
    public static function findByToken($chainId, $tokenAddress)
    {
        return self::where('token_chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->first();
    }
}