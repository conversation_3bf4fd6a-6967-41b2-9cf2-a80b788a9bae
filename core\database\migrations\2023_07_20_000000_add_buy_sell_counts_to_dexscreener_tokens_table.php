<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            $table->integer('buy_count')->nullable()->after('txn_24h');
            $table->integer('sell_count')->nullable()->after('buy_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            $table->dropColumn([
                'buy_count',
                'sell_count'
            ]);
        });
    }
}; 