/*!
 * jQuery Migrate - v3.0.0 - 2016-06-09
 * Copyright jQuery Foundation and other contributors
 */
!function(e,t){"use strict";var r;e.migrateVersion="3.0.0",(r=t.console&&t.console.log&&function(){t.console.log.apply(t.console,arguments)})&&(e&&!/^[12]\./.test(e.fn.jquery)||r("JQMIGRATE: jQuery 3.0.0+ REQUIRED"),e.migrateWarnings&&r("JQMIGRATE: Migrate plugin loaded multiple times"),r("JQMIGRATE: Migrate is installed"+(e.migrateMute?"":" with logging active")+", version "+e.migrateVersion));var n={};function i(r){var i=t.console;n[r]||(n[r]=!0,e.migrateWarnings.push(r),i&&i.warn&&!e.migrateMute&&(i.warn("JQMIGRATE: "+r),e.migrateTrace&&i.trace&&i.trace()))}function a(e,t,r,n){Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){return i(n),r}})}e.migrateWarnings=[],void 0===e.migrateTrace&&(e.migrateTrace=!0),e.migrateReset=function(){n={},e.migrateWarnings.length=0},"BackCompat"===document.compatMode&&i("jQuery is not compatible with Quirks Mode");var s,o=e.fn.init,u=e.isNumeric,c=e.find,l=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/,p=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/g;for(s in e.fn.init=function(e){var t=Array.prototype.slice.call(arguments);return"string"==typeof e&&"#"===e&&(i("jQuery( '#' ) is not a valid selector"),t[0]=[]),o.apply(this,t)},e.fn.init.prototype=e.fn,e.find=function(e){var t=Array.prototype.slice.call(arguments);if("string"==typeof e&&l.test(e))try{document.querySelector(e)}catch(r){e=e.replace(p,(function(e,t,r,n){return"["+t+r+'"'+n+'"]'}));try{document.querySelector(e),i("Attribute selector with '#' must be quoted: "+t[0]),t[0]=e}catch(e){i("Attribute selector with '#' was not fixed: "+t[0])}}return c.apply(this,t)},c)Object.prototype.hasOwnProperty.call(c,s)&&(e.find[s]=c[s]);e.fn.size=function(){return i("jQuery.fn.size() is deprecated; use the .length property"),this.length},e.parseJSON=function(){return i("jQuery.parseJSON is deprecated; use JSON.parse"),JSON.parse.apply(null,arguments)},e.isNumeric=function(t){var r,n,a=u(t),s=(n=(r=t)&&r.toString(),!e.isArray(r)&&n-parseFloat(n)+1>=0);return a!==s&&i("jQuery.isNumeric() should not be called on constructed objects"),s},a(e,"unique",e.uniqueSort,"jQuery.unique is deprecated, use jQuery.uniqueSort"),a(e.expr,"filters",e.expr.pseudos,"jQuery.expr.filters is now jQuery.expr.pseudos"),a(e.expr,":",e.expr.pseudos,'jQuery.expr[":"] is now jQuery.expr.pseudos');var d=e.ajax;e.ajax=function(){var e=d.apply(this,arguments);return e.promise&&(a(e,"success",e.done,"jQXHR.success is deprecated and removed"),a(e,"error",e.fail,"jQXHR.error is deprecated and removed"),a(e,"complete",e.always,"jQXHR.complete is deprecated and removed")),e};var f=e.fn.removeAttr,y=e.fn.toggleClass,g=/\S+/g;e.fn.removeAttr=function(t){var r=this;return e.each(t.match(g),(function(t,n){e.expr.match.bool.test(n)&&(i("jQuery.fn.removeAttr no longer sets boolean properties: "+n),r.prop(n,!1))})),f.apply(this,arguments)},e.fn.toggleClass=function(t){return void 0!==t&&"boolean"!=typeof t?y.apply(this,arguments):(i("jQuery.fn.toggleClass( boolean ) is deprecated"),this.each((function(){var r=this.getAttribute&&this.getAttribute("class")||"";r&&e.data(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===t?"":e.data(this,"__className__")||"")})))};var h=!1;e.swap&&e.each(["height","width","reliableMarginRight"],(function(t,r){var n=e.cssHooks[r]&&e.cssHooks[r].get;n&&(e.cssHooks[r].get=function(){var e;return h=!0,e=n.apply(this,arguments),h=!1,e})})),e.swap=function(e,t,r,n){var a,s,o={};for(s in h||i("jQuery.swap() is undocumented and deprecated"),t)o[s]=e.style[s],e.style[s]=t[s];for(s in a=r.apply(e,n||[]),t)e.style[s]=o[s];return a};var m=e.data;e.data=function(t,r,n){var a;return r&&r!==e.camelCase(r)&&(a=e.hasData(t)&&m.call(this,t))&&r in a?(i("jQuery.data() always sets/gets camelCased names: "+r),arguments.length>2&&(a[r]=n),a[r]):m.apply(this,arguments)};var v=e.Tween.prototype.run;e.Tween.prototype.run=function(t){e.easing[this.easing].length>1&&(i('easing function "jQuery.easing.'+this.easing.toString()+'" should use only first argument'),e.easing[this.easing]=e.easing[this.easing].bind(e.easing,t,this.options.duration*t,0,1,this.options.duration)),v.apply(this,arguments)};var j=e.fn.load,Q=e.event.fix;e.event.props=[],e.event.fixHooks={},e.event.fix=function(t){var r,n=t.type,a=this.fixHooks[n],s=e.event.props;if(s.length)for(i("jQuery.event.props are deprecated and removed: "+s.join());s.length;)e.event.addProp(s.pop());if(a&&!a._migrated_&&(a._migrated_=!0,i("jQuery.event.fixHooks are deprecated and removed: "+n),(s=a.props)&&s.length))for(;s.length;)e.event.addProp(s.pop());return r=Q.call(this,t),a&&a.filter?a.filter(r,t):r},e.each(["load","unload","error"],(function(t,r){e.fn[r]=function(){var e=Array.prototype.slice.call(arguments,0);return"load"===r&&"string"==typeof e[0]?j.apply(this,e):(i("jQuery.fn."+r+"() is deprecated"),e.splice(0,0,r),arguments.length?this.on.apply(this,e):(this.triggerHandler.apply(this,e),this))}})),e((function(){e(document).triggerHandler("ready")})),e.event.special.ready={setup:function(){this===document&&i("'ready' event is deprecated")}},e.fn.extend({bind:function(e,t,r){return i("jQuery.fn.bind() is deprecated"),this.on(e,null,t,r)},unbind:function(e,t){return i("jQuery.fn.unbind() is deprecated"),this.off(e,null,t)},delegate:function(e,t,r,n){return i("jQuery.fn.delegate() is deprecated"),this.on(t,e,r,n)},undelegate:function(e,t,r){return i("jQuery.fn.undelegate() is deprecated"),1===arguments.length?this.off(e,"**"):this.off(t,e||"**",r)}});var b=e.fn.offset;e.fn.offset=function(){var t,r=this[0],n={top:0,left:0};return r&&r.nodeType?(t=(r.ownerDocument||document).documentElement,e.contains(t,r)?b.apply(this,arguments):(i("jQuery.fn.offset() requires an element connected to a document"),n)):(i("jQuery.fn.offset() requires a valid DOM element"),n)};var w=e.param;e.param=function(t,r){var n=e.ajaxSettings&&e.ajaxSettings.traditional;return void 0===r&&n&&(i("jQuery.param() no longer uses jQuery.ajaxSettings.traditional"),r=n),w.call(this,t,r)};var x=e.fn.andSelf||e.fn.addBack;e.fn.andSelf=function(){return i("jQuery.fn.andSelf() replaced by jQuery.fn.addBack()"),x.apply(this,arguments)};var A=e.Deferred,S=[["resolve","done",e.Callbacks("once memory"),e.Callbacks("once memory"),"resolved"],["reject","fail",e.Callbacks("once memory"),e.Callbacks("once memory"),"rejected"],["notify","progress",e.Callbacks("memory"),e.Callbacks("memory")]];e.Deferred=function(t){var r=A(),n=r.promise();return r.pipe=n.pipe=function(){var t=arguments;return i("deferred.pipe() is deprecated"),e.Deferred((function(i){e.each(S,(function(a,s){var o=e.isFunction(t[a])&&t[a];r[s[1]]((function(){var t=o&&o.apply(this,arguments);t&&e.isFunction(t.promise)?t.promise().done(i.resolve).fail(i.reject).progress(i.notify):i[s[0]+"With"](this===n?i.promise():this,o?[t]:arguments)}))})),t=null})).promise()},t&&t.call(r,r),r}}(jQuery,window);