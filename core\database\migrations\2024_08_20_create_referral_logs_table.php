<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referral_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('The user who made the purchase (referred user)');
            $table->unsignedBigInteger('referee_id')->comment('The user who receives the commission (referrer)');
            $table->decimal('amount', 28, 8)->default(0);
            $table->integer('level')->default(1);
            $table->decimal('percent', 5, 2)->default(0);
            $table->string('trx')->nullable()->comment('Transaction reference');
            $table->timestamps();

            // Add indexes for faster queries
            $table->index('user_id');
            $table->index('referee_id');
            $table->index('trx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referral_logs');
    }
};
