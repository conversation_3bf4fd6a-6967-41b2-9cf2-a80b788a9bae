<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('presales', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('token_name');
            $table->string('token_symbol');
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('quantity')->nullable();
            $table->decimal('price', 18, 8)->nullable();
            $table->decimal('sold', 18, 8)->default(0);
            $table->boolean('is_active')->default(true);
            $table->integer('position')->default(0); // For ordering presales
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('presales');
    }
};
