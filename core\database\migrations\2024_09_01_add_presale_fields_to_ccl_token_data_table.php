<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ccl_token_data', function (Blueprint $table) {
            $table->date('presale_start_date')->nullable()->after('buy_token_url');
            $table->date('presale_end_date')->nullable()->after('presale_start_date');
            $table->string('presale_quantity')->nullable()->after('presale_end_date');
            $table->decimal('presale_price', 18, 8)->nullable()->after('presale_quantity');
            $table->decimal('presale_sold', 18, 8)->default(0)->after('presale_price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ccl_token_data', function (Blueprint $table) {
            $table->dropColumn('presale_start_date');
            $table->dropColumn('presale_end_date');
            $table->dropColumn('presale_quantity');
            $table->dropColumn('presale_price');
            $table->dropColumn('presale_sold');
        });
    }
};
