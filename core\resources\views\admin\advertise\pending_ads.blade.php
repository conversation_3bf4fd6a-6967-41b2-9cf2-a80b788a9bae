@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('User')</th>
                                <th>@lang('Position')</th>
                                <th>@lang('Banner')</th>
                                <th>@lang('Redirect URL')</th>
                                <th>@lang('Submitted')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($pendingAds as $ad)
                                <tr>
                                    <td>
                                        <span class="fw-bold">{{ $ad->user->username }}</span>
                                        <br>
                                        <span class="small">
                                            <a href="{{ route('admin.users.detail', $ad->user_id) }}">
                                                <span>@</span>{{ $ad->user->username }}
                                            </a>
                                        </span>
                                    </td>
                                    <td>{{ $ad->adPosition->name }}</td>
                                    <td>
                                        <a href="{{ getImage(getFilePath('ads_images').'/'.$ad->image) }}" target="_blank">
                                            <img src="{{ getImage(getFilePath('ads_images').'/'.$ad->image) }}" alt="@lang('Banner')" class="w-100" style="max-width: 100px;">
                                        </a>
                                    </td>
                                    <td>
                                        @if($ad->redirect_url)
                                            <a href="{{ $ad->redirect_url }}" target="_blank" class="text--primary">{{ Str::limit($ad->redirect_url, 30) }}</a>
                                        @else
                                            <span class="text-muted">@lang('No URL')</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ showDateTime($ad->created_at) }}
                                        <br>
                                        {{ diffForHumans($ad->created_at) }}
                                    </td>
                                    <td>
                                        <div class="button--group">
                                            <button type="button" class="btn btn-sm btn-outline--success approveBtn" data-id="{{ $ad->id }}">
                                                <i class="las la-check-circle"></i> @lang('Approve')
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline--danger rejectBtn" data-id="{{ $ad->id }}">
                                                <i class="las la-times-circle"></i> @lang('Reject')
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline--danger deleteBtn" data-id="{{ $ad->id }}">
                                                <i class="las la-trash"></i> @lang('Delete')
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage ?? 'No pending ads found') }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($pendingAds->hasPages())
                <div class="card-footer py-4">
                    {{ paginateLinks($pendingAds) }}
                </div>
            @endif
        </div>
    </div>
</div>

{{-- APPROVE MODAL --}}
<div id="approveModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Approve Banner Ad')</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <form action="" method="POST">
                @csrf
                <div class="modal-body">
                    <p>@lang('Are you sure to approve this banner ad?')</p>
                    <p>@lang('The banner will be displayed on the site immediately after approval.')</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('No')</button>
                    <button type="submit" class="btn btn--primary">@lang('Yes')</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- REJECT MODAL --}}
<div id="rejectModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Reject Banner Ad')</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <form action="" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Reason for Rejection')</label>
                        <textarea name="reason" class="form-control" rows="3" required></textarea>
                        <small class="text-muted">@lang('This will be shown to the user')</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <button type="submit" class="btn btn--danger">@lang('Reject')</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- DELETE MODAL --}}
<div id="deleteModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Delete Banner Ad')</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <form action="" method="POST">
                @csrf
                <div class="modal-body">
                    <p>@lang('Are you sure to delete this banner ad?')</p>
                    <p class="text-danger">@lang('This action cannot be undone and will permanently remove the banner.')</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('No')</button>
                    <button type="submit" class="btn btn--danger">@lang('Yes')</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    (function($) {
        "use strict";

        $('.approveBtn').on('click', function() {
            var modal = $('#approveModal');
            modal.find('form').attr('action', `{{ route('admin.advertise.pending.approve', '') }}/${$(this).data('id')}`);
            modal.modal('show');
        });

        $('.rejectBtn').on('click', function() {
            var modal = $('#rejectModal');
            modal.find('form').attr('action', `{{ route('admin.advertise.pending.reject') }}`);
            modal.find('input[name=id]').remove();
            modal.find('form').append(`<input type="hidden" name="id" value="${$(this).data('id')}">`);
            modal.modal('show');
        });

        $('.deleteBtn').on('click', function() {
            var modal = $('#deleteModal');
            modal.find('form').attr('action', `{{ route('admin.advertise.pending.delete', '') }}/${$(this).data('id')}`);
            modal.modal('show');
        });

    })(jQuery);
</script>
@endpush
