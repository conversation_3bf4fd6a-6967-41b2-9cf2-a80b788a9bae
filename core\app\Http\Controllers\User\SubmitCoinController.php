<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\SubmitCoin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubmitCoinController extends Controller
{
    /**
     * View user's submitted coins that are currently listed
     */
    public function mySubmittedCoins()
    {
        $pageTitle = 'My Submitted Coins';

        // Get all submitted coins by the user
        $userSubmittedCoins = SubmitCoin::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->get();

        // Filter to include only:
        // 1. Tokens that exist in DexscreenerToken table (active tokens)
        // 2. Tokens that were explicitly rejected (status = 2)
        $listedCoins = collect();

        foreach ($userSubmittedCoins as $coin) {
            // Check if this coin has a corresponding token in DexscreenerToken
            $token = null;
            if ($coin->contract_address) {
                $token = \App\Models\DexscreenerToken::where('token_address', $coin->contract_address)
                    ->first();
            }

            // Include the coin if:
            // 1. It has a corresponding token in DexscreenerToken (still active in the system)
            // 2. OR it was explicitly rejected (status = 2)
            if ($token || $coin->status == 2) {
                $coin->token = $token; // Attach the token data (may be null for rejected tokens)
                $listedCoins->push($coin);
            }
        }

        // Paginate the filtered collection
        $perPage = getPaginate(10);
        $page = request()->get('page', 1);
        $offset = ($page - 1) * $perPage;

        $submittedCoins = new \Illuminate\Pagination\LengthAwarePaginator(
            $listedCoins->slice($offset, $perPage),
            $listedCoins->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return view('Template::user.token.submitted_coins', compact('pageTitle', 'submittedCoins'));
    }
}
