<?php

namespace Database\Seeders;

use App\Models\Gateway;
use App\Models\GatewayCurrency;
use Illuminate\Database\Seeder;

class PaypalExpressSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if PayPal Express gateway exists
        $gateway = Gateway::where('code', 101)->first();
        
        if (!$gateway) {
            // Create new gateway
            $gateway = new Gateway();
            $gateway->code = 101;
            $gateway->name = 'PayPal Express';
            $gateway->alias = 'paypalexpress';
            $gateway->image = '663a34d5d1dfc1715090645.png'; // Using an existing image from your system
            $gateway->status = 1;
            $gateway->gateway_parameters = json_encode([
                'paypal_email' => [
                    'title' => 'PayPal Email',
                    'global' => true,
                    'value' => '<EMAIL>'
                ],
                'client_id' => [
                    'title' => 'Client ID',
                    'global' => true,
                    'value' => 'YOUR-CLIENT-ID'
                ],
                'client_secret' => [
                    'title' => 'Client Secret',
                    'global' => true,
                    'value' => 'YOUR-CLIENT-SECRET'
                ],
                'sandbox_mode' => [
                    'title' => 'Sandbox Mode',
                    'global' => true,
                    'value' => 'yes'
                ]
            ]);
            $gateway->supported_currencies = json_encode([
                'USD' => 'USD',
                'EUR' => 'EUR',
                'GBP' => 'GBP',
                'AUD' => 'AUD',
                'CAD' => 'CAD'
            ]);
            $gateway->crypto = 0; // Not a crypto payment
            $gateway->description = 'PayPal Express Checkout Gateway';
            $gateway->save();
        } else {
            // Update existing gateway
            $gateway->name = 'PayPal Express';
            $gateway->alias = 'paypalexpress';
            $gateway->image = '663a34d5d1dfc1715090645.png';
            $gateway->gateway_parameters = json_encode([
                'paypal_email' => [
                    'title' => 'PayPal Email',
                    'global' => true,
                    'value' => '<EMAIL>'
                ],
                'client_id' => [
                    'title' => 'Client ID',
                    'global' => true,
                    'value' => 'YOUR-CLIENT-ID'
                ],
                'client_secret' => [
                    'title' => 'Client Secret',
                    'global' => true,
                    'value' => 'YOUR-CLIENT-SECRET'
                ],
                'sandbox_mode' => [
                    'title' => 'Sandbox Mode',
                    'global' => true,
                    'value' => 'yes'
                ]
            ]);
            $gateway->save();
        }
        
        // Check if USD currency exists for this gateway
        $currency = GatewayCurrency::where('method_code', 101)
            ->where('currency', 'USD')
            ->first();
            
        if (!$currency) {
            // Create the currency
            $currency = new GatewayCurrency();
            $currency->name = 'PayPal Express - USD';
            $currency->gateway_alias = 'paypalexpress';
            $currency->currency = 'USD';
            $currency->symbol = '$';
            $currency->min_amount = 1.00;
            $currency->max_amount = 10000.00;
            $currency->percent_charge = 1.00;
            $currency->fixed_charge = 1.00;
            $currency->rate = 1.00;
            $currency->method_code = 101;
            $currency->gateway_parameter = json_encode([
                'paypal_email' => '<EMAIL>',
                'client_id' => 'YOUR-CLIENT-ID',
                'client_secret' => 'YOUR-CLIENT-SECRET',
                'sandbox_mode' => 'yes'
            ]);
            $currency->save();
        }
    }
} 