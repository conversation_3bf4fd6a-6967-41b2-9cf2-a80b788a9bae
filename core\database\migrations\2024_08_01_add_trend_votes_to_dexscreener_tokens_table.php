<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            if (!Schema::hasColumn('dexscreener_tokens', 'trend_votes')) {
                $table->integer('trend_votes')->default(0)->after('is_verified');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            if (Schema::hasColumn('dexscreener_tokens', 'trend_votes')) {
                $table->dropColumn('trend_votes');
            }
        });
    }
};
