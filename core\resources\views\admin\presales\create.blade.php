@extends('admin.layouts.app')
@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.presales.store') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Title')</label>
                                <input type="text" class="form-control" name="title" required value="{{ old('title') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Position')</label>
                                <input type="number" class="form-control" name="position" required value="{{ old('position', 0) }}" min="0">
                                <small class="text-muted">@lang('Lower numbers appear first')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Token Name')</label>
                                <input type="text" class="form-control" name="token_name" required value="{{ old('token_name') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Token Symbol')</label>
                                <input type="text" class="form-control" name="token_symbol" required value="{{ old('token_symbol') }}">
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Start Date')</label>
                                <input type="date" class="form-control" name="start_date" value="{{ old('start_date') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('End Date')</label>
                                <input type="date" class="form-control" name="end_date" value="{{ old('end_date') }}">
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Quantity')</label>
                                <input type="text" class="form-control" name="quantity" value="{{ old('quantity') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Price')</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="price" value="{{ old('price') }}">
                                    <span class="input-group-text">USD</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Next Price')</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="next_price" value="{{ old('next_price') }}">
                                    <span class="input-group-text">USD</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Status')</label>
                                <select class="form-control" name="is_active" required>
                                    <option value="1" {{ old('is_active') == '1' ? 'selected' : '' }}>@lang('Active')</option>
                                    <option value="0" {{ old('is_active') == '0' ? 'selected' : '' }}>@lang('Inactive')</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="form-group">
                                <button type="submit" class="btn btn--primary w-100 h-45">@lang('Submit')</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<a href="{{ route('admin.presales.index') }}" class="btn btn-sm btn--primary">
    <i class="las la-list"></i> @lang('All Presales')
</a>
@endpush
