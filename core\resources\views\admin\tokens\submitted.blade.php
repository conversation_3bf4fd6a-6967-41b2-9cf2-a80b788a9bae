@extends('admin.layouts.app')
@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('Token')</th>
                                <th>@lang('Blockchain')</th>
                                <th>@lang('Contract Address')</th>
                                <th>@lang('Submitted By')</th>
                                <th>@lang('Submitted At')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($submittedTokens as $token)
                                <tr>
                                    <td>
                                        <div class="user">
                                            <div class="thumb">
                                                @if($token->image_url)
                                                    <img src="{{ getImage(getFilePath('coinLogos').'/'.$token->image_url, getFileSize('coinLogos')) }}" alt="@lang('image')">
                                                @else
                                                    <img src="{{ asset('assets/images/default.png') }}" alt="@lang('image')">
                                                @endif
                                            </div>
                                            <span class="name">{{ $token->token_name }} ({{ $token->token_symbol }})</span>
                                        </div>
                                    </td>
                                    <td>{{ $token->chain_id }}</td>
                                    <td>{{ Str::limit($token->token_address, 15) }}</td>
                                    <td>
                                        <a href="{{ route('admin.users.detail', $token->submitted_by_user_id) }}">
                                            {{ @\App\Models\User::find($token->submitted_by_user_id)->username }}
                                        </a>
                                    </td>
                                    <td>{{ showDateTime($token->created_at) }}</td>
                                    <td>
                                        <span class="badge badge--warning">@lang('Pending')</span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.tokens.view', $token->id) }}" class="btn btn-sm btn-outline--primary">
                                            <i class="las la-eye"></i> @lang('View')
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline--success verifyBtn" data-id="{{ $token->id }}" data-token="{{ $token->token_name }} ({{ $token->token_symbol }})">
                                            <i class="las la-check-circle"></i> @lang('Verify')
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline--danger rejectBtn" data-id="{{ $token->id }}" data-token="{{ $token->token_name }} ({{ $token->token_symbol }})">
                                            <i class="las la-times-circle"></i> @lang('Reject')
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage ?? 'No pending tokens to approve') }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($submittedTokens->hasPages())
                <div class="card-footer py-4">
                    {{ paginateLinks($submittedTokens) }}
                </div>
            @endif
        </div>
    </div>
</div>

{{-- VERIFY MODAL --}}
<div id="verifyModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Verify Token')</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="" method="POST">
                @csrf
                <div class="modal-body">
                    <p>@lang('Are you sure to verify') <span class="font-weight-bold token-name"></span>?</p>
                    <p>@lang('This token will be displayed on the homepage as a promoted token.')</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-dismiss="modal">@lang('No')</button>
                    <button type="submit" class="btn btn--success">@lang('Yes')</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- REJECT MODAL --}}
<div id="rejectModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Reject Token')</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="" method="POST">
                @csrf
                <div class="modal-body">
                    <p>@lang('Are you sure to reject') <span class="font-weight-bold token-name"></span>?</p>
                    <p>@lang('This token will be removed from the system.')</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-dismiss="modal">@lang('No')</button>
                    <button type="submit" class="btn btn--danger">@lang('Yes')</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    (function($) {
        "use strict";

        $('.verifyBtn').on('click', function() {
            var modal = $('#verifyModal');
            modal.find('.token-name').text($(this).data('token'));
            modal.find('form').attr('action', `{{ route('admin.tokens.verify', '') }}/${$(this).data('id')}`);
            modal.modal('show');
        });

        $('.rejectBtn').on('click', function() {
            var modal = $('#rejectModal');
            modal.find('.token-name').text($(this).data('token'));
            modal.find('form').attr('action', `{{ route('admin.tokens.reject', '') }}/${$(this).data('id')}`);
            modal.modal('show');
        });

    })(jQuery);
</script>
@endpush