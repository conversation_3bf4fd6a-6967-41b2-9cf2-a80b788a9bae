<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('submit_coins', function (Blueprint $table) {
            $table->string('presale_start_date')->nullable()->after('is_presale');
            $table->string('presale_end_date')->nullable()->after('presale_start_date');
            $table->string('presale_url')->nullable()->after('presale_end_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('submit_coins', function (Blueprint $table) {
            $table->dropColumn('presale_start_date');
            $table->dropColumn('presale_end_date');
            $table->dropColumn('presale_url');
        });
    }
};
