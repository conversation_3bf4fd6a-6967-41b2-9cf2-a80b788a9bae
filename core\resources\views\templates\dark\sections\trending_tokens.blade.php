@php
    use App\Models\DexscreenerToken;
    use App\Models\TokenVote;
    use Illuminate\Support\Facades\DB;

    // Get the token count setting from general settings
    $general = \App\Models\GeneralSetting::first();
    $tokenCount = $general->dexscreener_tokens_count ?? 10;

    // Set fixed items per page for pagination (100 items)
    $itemsPerPage = 100;

    // Get current page for trending tokens
    $currentPage = request()->query('trending_page', 1);

    // Optimized approach: Use database-level sorting with vote count calculation
    // This avoids loading all tokens into memory while maintaining exact ordering
    
    // Step 1: Get tokens with vote counts calculated at database level
    $tokensQuery = DexscreenerToken::select([
            'dexscreener_tokens.*',
            // Calculate vote count at database level for performance
            DB::raw('(
                SELECT COALESCE(SUM(CASE WHEN is_negative = 0 THEN 1 ELSE 0 END), 0) 
                FROM token_votes 
                WHERE token_votes.chain_id COLLATE utf8mb4_unicode_ci = dexscreener_tokens.chain_id COLLATE utf8mb4_unicode_ci
                AND token_votes.token_address COLLATE utf8mb4_unicode_ci = dexscreener_tokens.token_address COLLATE utf8mb4_unicode_ci
            ) + COALESCE(dexscreener_tokens.trend_votes, 0) as total_vote_count')
        ])
        ->where(function($query) {
            $query->where('is_verified', true)
                  ->orWhereNull('submitted_by_user_id'); // Admin added tokens have null user_id
        });

    // Step 2: Apply the original ordering logic at database level
    // First by vote count (desc), then by created_at (desc) for tokens with same vote count
    $tokensQuery->orderByRaw('total_vote_count DESC, created_at DESC');

    // Step 3: Get total count for pagination (efficient count query)
    $totalTokens = $tokensQuery->count();
    $totalPages = ceil($totalTokens / $itemsPerPage);

    // Step 4: Get only the tokens for current page
    $paginatedTokensRaw = $tokensQuery
        ->skip(($currentPage - 1) * $itemsPerPage)
        ->take($itemsPerPage)
        ->get();

    // Step 5: Process only the paginated tokens (much smaller dataset)
    $paginatedTokens = collect();
    $startRank = ($currentPage - 1) * $itemsPerPage + 1;

    foreach ($paginatedTokensRaw as $index => $token) {
        // Check if this is a presale or fair launch token (only for displayed tokens)
        $isPresale = strpos($token->token_address, 'presale_') === 0;
        $isFairLaunch = strpos($token->token_address, 'fairlaunch_') === 0;

        // Check metadata only for displayed tokens
        if (!$isPresale && !$isFairLaunch) {
            if (is_object($token->metadata)) {
                $metadata = $token->metadata;
            } elseif (is_string($token->metadata)) {
                $metadata = json_decode($token->metadata ?? '{}');
            } else {
                $metadata = new \stdClass();
            }

            if (isset($metadata->force_presale_display) && $metadata->force_presale_display === true) {
                $isPresale = true;
            }
        }

        // Check SubmitCoin record only for displayed tokens if needed
        $submitCoin = null;
        if (!$isPresale && !$isFairLaunch) {
            $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token->token_address)
                ->where(function($query) {
                    $query->where('is_presale', 1)
                          ->orWhere('is_fair_launch', 1);
                })
                ->orderBy('created_at', 'desc')
                ->first();

            if ($submitCoin) {
                $isPresale = $submitCoin->is_presale == 1;
                $isFairLaunch = $submitCoin->is_fair_launch == 1;
            }
        } elseif (strpos($token->token_address, 'presale_') === 0 || strpos($token->token_address, 'fairlaunch_') === 0) {
            $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token->token_address)
                ->orderBy('created_at', 'desc')
                ->first();

            if ($submitCoin) {
                $isPresale = $submitCoin->is_presale == 1;
                $isFairLaunch = $submitCoin->is_fair_launch == 1;
            }
        }

        // Use the pre-calculated vote count from database
        $totalVotes = $token->total_vote_count;
        $userVotes = TokenVote::getVoteCountByAddress($token->chain_id, $token->token_address);
        $adminTrendVotes = $token->trend_votes ?? 0;

        $paginatedTokens->push([
            'chain_id' => $token->chain_id,
            'token_address' => $token->token_address,
            'token_name' => $token->token_name,
            'token_symbol' => $token->token_symbol,
            'price_usd' => $token->price_usd,
            'price_change_24h' => $token->price_change_24h,
            'price_change_5m' => $token->price_change_5m ?? null,
            'price_change_1h' => $token->price_change_1h ?? null,
            'price_change_6h' => $token->price_change_6h ?? null,
            'volume_24h' => $token->volume_24h,
            'market_cap' => $token->market_cap,
            'liquidity_usd' => $token->liquidity_usd,
            'txn_24h' => $token->txn_24h,
            'token_age' => $token->token_age,
            'image_url' => $token->image_url,
            'source' => 'dexscreener',
            'is_presale' => $isPresale,
            'is_fair_launch' => $isFairLaunch,
            'vote_count' => $totalVotes,
            'user_votes' => $userVotes,
            'admin_votes' => $adminTrendVotes,
            'created_at' => $token->created_at,
            'global_rank' => $startRank + $index, // Calculate rank based on page position
            'token_details_url' => route('token.details', ['chainId' => $token->chain_id, 'tokenAddress' => $token->token_address])
        ]);
    }
@endphp

<section class="trending-tokens py-3">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <x-ad position="trending_tokens_top" />

                <div class="trending-tokens-header d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <h4 class="mb-0 d-inline-flex align-items-center justify-content-center section-title"><span class="fire-emoji me-2">🔥</span>Trending Tokens</h4>
                        <div class="search-menu-items ms-3">
                            <div class="search-menu-item-wrapper blockchain-dropdown-container">
                                <a href="javascript:void(0);" class="search-menu-item blockchain-toggle" id="trendingBlockchainToggle">Blockchain ▼</a>
                                <div class="blockchain-menu">
                                    <a href="{{ route('search', ['query' => 'ethereum']) }}" class="blockchain-item">ETH</a>
                                    <a href="{{ route('search', ['query' => 'bsc']) }}" class="blockchain-item">BSC</a>
                                    <a href="{{ route('search', ['query' => 'polygon']) }}" class="blockchain-item">MATIC</a>
                                    <a href="{{ route('search', ['query' => 'solana']) }}" class="blockchain-item">SOL</a>
                                    <a href="{{ route('search', ['query' => 'avalanche']) }}" class="blockchain-item">AVAX</a>
                                    <a href="{{ route('search', ['query' => 'tron']) }}" class="blockchain-item">TRX</a>
                                    <a href="{{ route('search', ['query' => 'cardano']) }}" class="blockchain-item">ADA</a>
                                    <a href="{{ route('search', ['query' => 'polkadot']) }}" class="blockchain-item">DOT</a>
                                    <a href="{{ route('search', ['query' => 'arbitrum']) }}" class="blockchain-item">ARB</a>
                                    <a href="{{ route('search', ['query' => 'optimism']) }}" class="blockchain-item">OP</a>
                                    <a href="{{ route('search', ['query' => 'base']) }}" class="blockchain-item">BASE</a>
                                    <a href="{{ route('search', ['query' => 'fantom']) }}" class="blockchain-item">FTM</a>
                                    <a href="{{ route('search', ['query' => 'cronos']) }}" class="blockchain-item">CRO</a>
                                    <a href="{{ route('search', ['query' => 'algorand']) }}" class="blockchain-item">ALGO</a>
                                    <a href="{{ route('search', ['query' => 'cosmos']) }}" class="blockchain-item">ATOM</a>
                                    <a href="{{ route('search', ['query' => 'near']) }}" class="blockchain-item">NEAR</a>
                                    <a href="{{ route('search', ['query' => 'aptos']) }}" class="blockchain-item">APT</a>
                                    <a href="{{ route('search', ['query' => 'sui']) }}" class="blockchain-item">SUI</a>
                                    <a href="{{ route('search', ['query' => 'hedera']) }}" class="blockchain-item">HBAR</a>
                                    <a href="{{ route('search', ['query' => 'xrp']) }}" class="blockchain-item">XRP</a>
                                    <a href="{{ route('search', ['query' => 'stellar']) }}" class="blockchain-item">XLM</a>
                                    <a href="{{ route('search', ['query' => 'harmony']) }}" class="blockchain-item">ONE</a>
                                    <a href="{{ route('search', ['query' => 'moonbeam']) }}" class="blockchain-item">GLMR</a>
                                    <a href="{{ route('search', ['query' => 'gnosis']) }}" class="blockchain-item">GNO</a>
                                    <a href="{{ route('search', ['query' => 'zilliqa']) }}" class="blockchain-item">ZIL</a>
                                    <a href="{{ route('search', ['query' => 'zksync']) }}" class="blockchain-item">ZKS</a>
                                    <a href="{{ route('search', ['query' => 'linea']) }}" class="blockchain-item">LINEA</a>
                                    <a href="{{ route('search', ['query' => 'scroll']) }}" class="blockchain-item">SCROLL</a>
                                    <a href="{{ route('search', ['query' => 'heco']) }}" class="blockchain-item">HT</a>
                                    <a href="{{ route('search', ['query' => 'ethw']) }}" class="blockchain-item">ETHW</a>
                                    <a href="{{ route('search', ['query' => 'kcc']) }}" class="blockchain-item">KCS</a>
                                    <a href="{{ route('search', ['query' => 'fon']) }}" class="blockchain-item">FON</a>
                                    <a href="{{ route('search', ['query' => 'mantle']) }}" class="blockchain-item">MNT</a>
                                    <a href="{{ route('search', ['query' => 'opbnb']) }}" class="blockchain-item">OBNB</a>
                                    <a href="{{ route('search', ['query' => 'zkfair']) }}" class="blockchain-item">ZKF</a>
                                    <a href="{{ route('search', ['query' => 'blast']) }}" class="blockchain-item">BLAST</a>
                                    <a href="{{ route('search', ['query' => 'manta']) }}" class="blockchain-item">MANTA</a>
                                    <a href="{{ route('search', ['query' => 'berachain']) }}" class="blockchain-item">BERA</a>
                                    <a href="{{ route('search', ['query' => 'abstract']) }}" class="blockchain-item">ABSTRACT</a>
                                    <a href="{{ route('search', ['query' => 'hashkey']) }}" class="blockchain-item">HSK</a>
                                    <a href="{{ route('search', ['query' => 'sonic']) }}" class="blockchain-item">SONIC</a>
                                    <a href="{{ route('search', ['query' => 'story']) }}" class="blockchain-item">STORY</a>
                                    <a href="{{ route('search', ['query' => 'own']) }}" class="blockchain-item">OWN</a>
                                    <a href="{{ route('search', ['query' => 'other']) }}" class="blockchain-item">OTHER</a>
                                </div>
                            </div>
                            <div class="search-menu-item-wrapper">
                                <a href="{{ route('search', ['query' => 'presale']) }}" class="search-menu-item">Presale</a>
                            </div>
                            <div class="search-menu-item-wrapper">
                                <a href="{{ route('search', ['query' => 'fair launch']) }}" class="search-menu-item">Fair Launch</a>
                            </div>
                            <div class="search-menu-item-wrapper">
                                <a href="{{ route('search', ['query' => 'newest']) }}" class="search-menu-item">Newest</a>
                            </div>
                        </div>
                    </div>
                    <div class="tokens-pagination-info small">

                    </div>
                </div>

                @if($paginatedTokens->count() > 0)
                <div class="table-responsive mt-1">
                    <table class="table trending-tokens-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Coin</th>
                                <th>Chain</th>
                                <th>Price</th>
                                <th>Age</th>
                                <th>TXN</th>
                                <th>Volume</th>
                                <th>1h</th>
                                <th>24h</th>
                                <th>LP</th>
                                <th>MCap</th>
                                <th>Votes</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($paginatedTokens as $index => $token)
                                @php
                                    $priceChange24h = $token['price_change_24h'];
                                    $priceChange5m = $token['price_change_5m'];
                                    $priceChange1h = $token['price_change_1h'];
                                    $priceChange6h = $token['price_change_6h'];
                                    $change24hClass = floatval($priceChange24h) >= 0 ? 'text-success' : 'text-danger';
                                    $change5mClass = floatval($priceChange5m) >= 0 ? 'text-success' : 'text-danger';
                                    $change1hClass = floatval($priceChange1h) >= 0 ? 'text-success' : 'text-danger';
                                    $change6hClass = floatval($priceChange6h) >= 0 ? 'text-success' : 'text-danger';
                                    $changePrefix24h = floatval($priceChange24h) >= 0 ? '+' : '';
                                    $changePrefix5m = floatval($priceChange5m) >= 0 ? '+' : '';
                                    $changePrefix1h = floatval($priceChange1h) >= 0 ? '+' : '';
                                    $changePrefix6h = floatval($priceChange6h) >= 0 ? '+' : '';
                                    $dexUrl = $token['token_details_url'];
                                @endphp
                                <tr>
                                    <td>
                                        <div class="rank-number">{{ $token['global_rank'] }}</div>
                                    </td>
                                    <td>
                                        <div class="position-relative">
                                            <div class="token-actions-wrapper mb-1">
                                                <span class="token-action-icon promote-icon" data-bs-toggle="tooltip" data-bs-placement="top" title="Promote" data-token-address="{{ $token['token_address'] }}" data-chain-id="{{ $token['chain_id'] }}" data-token-symbol="{{ $token['token_symbol'] }}">⚡</span>
                                                <span class="token-action-icon vote-icon" data-bs-toggle="tooltip" data-bs-placement="top" title="Vote" data-token-address="{{ $token['token_address'] }}" data-chain-id="{{ $token['chain_id'] }}" data-token-symbol="{{ $token['token_symbol'] }}">🔥</span>
                                                <span class="token-action-icon watchlist-icon" data-bs-toggle="tooltip" data-bs-placement="top" title="Add to watchlist" data-token-address="{{ $token['token_address'] }}" data-chain-id="{{ $token['chain_id'] }}" data-token-symbol="{{ $token['token_symbol'] }}">⭐</span>
                                            </div>
                                            <a href="{{ $dexUrl }}" class="d-flex align-items-center text-decoration-none">
                                                <img src="@if(isset($token['image_url']) && !filter_var($token['image_url'], FILTER_VALIDATE_URL) && strpos($token['image_url'], 'http') !== 0){{ asset('assets/images/coin_logos/'.$token['image_url']) }}@else{{ $token['image_url'] ?? asset('assets/images/default.png') }}@endif" alt="{{ $token['token_symbol'] }}" class="coin-icon me-2" width="32" height="32">
                                                <div class="token-info-container">
                                                    <div class="fw-bold text-white">{{ $token['token_symbol'] }}</div>
                                                    <div class="text-muted small">{{ Str::limit($token['token_name'], 20) }}</div>
                                                </div>
                                            </a>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge chain-badge">{{ formatBlockchainName($token['chain_id']) }}</span>
                                    </td>
                                    <td>
                                        @if(isset($token['is_presale']) && $token['is_presale'])
                                            <span class="presale-badge-list">Presale</span>
                                        @elseif(isset($token['is_fair_launch']) && $token['is_fair_launch'])
                                            <span class="presale-badge-list">Fair Launch</span>
                                        @elseif($token['price_usd'])
                                            ${{ $token['price_usd'] }}
                                        @else
                                            <span class="softcap">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if((isset($token['is_presale']) && $token['is_presale']) || (isset($token['is_fair_launch']) && $token['is_fair_launch']))
                                            @php
                                                // Get the corresponding SubmitCoin record for presale/fair launch tokens
                                                $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token['token_address'])
                                                    ->orderBy('created_at', 'desc')
                                                    ->first();

                                                if ($submitCoin && $submitCoin->presale_start_date) {
                                                    try {
                                                        $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', $submitCoin->presale_start_date);
                                                        $now = \Carbon\Carbon::now();
                                                        $daysUntil = (int)$now->diffInDays($startDate, false);

                                                        if ($daysUntil > 0) {
                                                            // Future date
                                                            $dayText = $daysUntil == 1 ? 'Day' : 'Days';
                                                            echo 'Starts in ' . $daysUntil . ' ' . $dayText;
                                                        } elseif ($daysUntil == 0) {
                                                            // Today
                                                            echo 'Started Today';
                                                        } else {
                                                            // Past date
                                                            $dayText = abs($daysUntil) == 1 ? 'Day' : 'Days';
                                                            echo 'Started ' . abs($daysUntil) . ' ' . $dayText . ' ago';
                                                        }
                                                    } catch (\Exception $e) {
                                                        // If date format is invalid, fall back to created_at
                                                        if(isset($token['created_at'])) {
                                                            $createdAt = \Carbon\Carbon::parse($token['created_at']);
                                                            $diff = $createdAt->diffInSeconds();

                                                            if ($diff < 60) {
                                                                echo $diff . 's';
                                                            } elseif ($diff < 3600) {
                                                                echo floor($diff / 60) . 'm';
                                                            } elseif ($diff < 86400) {
                                                                echo floor($diff / 3600) . 'h';
                                                            } else {
                                                                echo floor($diff / 86400) . 'd';
                                                            }
                                                        } else {
                                                            echo '<span class="softcap">-</span>';
                                                        }
                                                    }
                                                } else {
                                                    // No presale date, fall back to created_at
                                                    if(isset($token['created_at'])) {
                                                        $createdAt = \Carbon\Carbon::parse($token['created_at']);
                                                        $diff = $createdAt->diffInSeconds();

                                                        if ($diff < 60) {
                                                            echo $diff . 's';
                                                        } elseif ($diff < 3600) {
                                                            echo floor($diff / 60) . 'm';
                                                        } elseif ($diff < 86400) {
                                                            echo floor($diff / 3600) . 'h';
                                                        } else {
                                                            echo floor($diff / 86400) . 'd';
                                                        }
                                                    } else {
                                                        echo '<span class="softcap">-</span>';
                                                    }
                                                }
                                            @endphp
                                        @elseif($token['token_age'])
                                            {{ $token['token_age'] }} days
                                        @else
                                            <span class="softcap">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if((isset($token['is_presale']) && $token['is_presale']) || (isset($token['is_fair_launch']) && $token['is_fair_launch']))
                                            <span class="presale-label">Softcap</span><br>
                                            @php
                                                // Get the corresponding SubmitCoin record for presale/fair launch tokens
                                                $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token['token_address'])
                                                    ->orderBy('created_at', 'desc')
                                                    ->first();

                                                $softcap = $submitCoin && $submitCoin->softcap ? formatCapValue($submitCoin->softcap) : '-';
                                            @endphp
                                            <span class="presale-value-list">{{ $softcap }}</span>
                                        @elseif($token['txn_24h'])
                                            {{ number_format($token['txn_24h']) }}
                                        @else
                                            <span class="softcap">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if((isset($token['is_presale']) && $token['is_presale']) || (isset($token['is_fair_launch']) && $token['is_fair_launch']))
                                            <span class="presale-label">Hardcap</span><br>
                                            @php
                                                // Get the corresponding SubmitCoin record for presale/fair launch tokens
                                                $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token['token_address'])
                                                    ->orderBy('created_at', 'desc')
                                                    ->first();

                                                $hardcap = $submitCoin && $submitCoin->hardcap ? formatCapValue($submitCoin->hardcap) : '-';
                                            @endphp
                                            <span class="presale-value-list">{{ $hardcap }}</span>
                                        @elseif($token['volume_24h'])
                                            ${{ formatAmount($token['volume_24h']) }}
                                        @else
                                            <span class="softcap">-</span>
                                        @endif
                                    </td>

                                    <td>
                                        @if((isset($token['is_presale']) && $token['is_presale']) || (isset($token['is_fair_launch']) && $token['is_fair_launch']))
                                            <span class="presale-na-list">N/A</span>
                                        @elseif($token['price_change_1h'] !== null)
                                            <span class="{{ $change1hClass }}">{{ $changePrefix1h }}{{ number_format(floatval($token['price_change_1h']), 2) }}%</span>
                                        @else
                                            <span class="softcap">-</span>
                                        @endif
                                    </td>

                                    <td>
                                        @if((isset($token['is_presale']) && $token['is_presale']) || (isset($token['is_fair_launch']) && $token['is_fair_launch']))
                                            <span class="presale-na-list">N/A</span>
                                        @elseif($token['price_change_24h'] !== null)
                                            <span class="{{ $change24hClass }}">{{ $changePrefix24h }}{{ number_format(floatval($token['price_change_24h']), 2) }}%</span>
                                        @else
                                            <span class="softcap">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if((isset($token['is_presale']) && $token['is_presale']) || (isset($token['is_fair_launch']) && $token['is_fair_launch']))
                                            <span class="presale-na-list">N/A</span>
                                        @elseif($token['liquidity_usd'])
                                            ${{ formatAmount($token['liquidity_usd']) }}
                                        @else
                                            <span class="softcap">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if((isset($token['is_presale']) && $token['is_presale']) || (isset($token['is_fair_launch']) && $token['is_fair_launch']))
                                            <span class="presale-na-list">N/A</span>
                                        @elseif($token['market_cap'])
                                            ${{ formatAmount($token['market_cap']) }}
                                        @else
                                            <span class="softcap">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="vote-count">
                                            <span class="fire-emoji-small">🔥</span> {{ $token['vote_count'] }}
                                        </div>
                                    </td>
                                </tr>

                                {{-- Check if this is token #50 and we have at least 50 tokens --}}
                                @if($paginatedTokens->count() >= 50 && $token['global_rank'] == 50)
                                    <tr>
                                        <td>
                                            <div class="rank-number">AD</div>
                                        </td>
                                        <td colspan="11">
                                            <x-ad position="trending_tokens_middle" />
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                        </tbody>
                    </table>
                </div>

                @if($totalPages > 1)
                <div class="pagination-wrapper d-flex justify-content-center mt-3">
                    <ul class="pagination">
                        @if($currentPage > 1)
                        <li class="page-item">
                            <a class="page-link trending-pagination-link" href="?trending_page={{ $currentPage - 1 }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        @endif

                        @php
                            // Show at most 5 page numbers with current page in the middle when possible
                            $startPage = max(1, $currentPage - 2);
                            $endPage = min($totalPages, $startPage + 4);

                            // Adjust start page if we're near the end
                            if ($endPage - $startPage < 4 && $startPage > 1) {
                                $startPage = max(1, $endPage - 4);
                            }
                        @endphp

                        @if($startPage > 1)
                            <li class="page-item">
                                <a class="page-link trending-pagination-link" href="?trending_page=1">1</a>
                            </li>
                            @if($startPage > 2)
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            @endif
                        @endif

                        @for($i = $startPage; $i <= $endPage; $i++)
                        <li class="page-item {{ $i == $currentPage ? 'active' : '' }}">
                            <a class="page-link trending-pagination-link" href="?trending_page={{ $i }}">{{ $i }}</a>
                        </li>
                        @endfor

                        @if($endPage < $totalPages)
                            @if($endPage < $totalPages - 1)
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            @endif
                            <li class="page-item">
                                <a class="page-link trending-pagination-link" href="?trending_page={{ $totalPages }}">{{ $totalPages }}</a>
                            </li>
                        @endif

                        @if($currentPage < $totalPages)
                        <li class="page-item">
                            <a class="page-link trending-pagination-link" href="?trending_page={{ $currentPage + 1 }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        @endif
                    </ul>
                </div>
                @endif

                @else
                <div class="text-center mt-4">
                    <p>No trending tokens available at the moment.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

@auth
<!-- Promote Token Modal -->
<div class="modal custom--modal fade" id="promoteTokenModal" tabindex="-1" aria-labelledby="promoteTokenModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promoteTokenModalLabel">@lang('Promote Token')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('user.token.promote') }}" method="POST">
                    @csrf
                    <input type="hidden" name="chain_id" value="">
                    <input type="hidden" name="token_address" value="">
                    <div class="form-group">
                        <label>@lang('Token')</label>
                        <div class="input-group">
                            <span class="form-control token-name-display"></span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Current Promote Credits')</label>
                        <div class="input-group">
                            <span class="form-control">{{ auth()->user()->promote_credits }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Promotion Days')</label>
                        <div class="input-group">
                            <input type="number" name="days" class="form-control" min="1" max="100" value="1" required>
                            <span class="input-group-text">Days</span>
                        </div>
                        <small class="text-muted">Each day costs 1 promote credit. The token will be displayed in the Promoted Coins section for the specified number of days.</small>
                    </div>
                    <div class="form-group mt-3 text-end">
                        <button type="submit" class="btn btn--base w-100">@lang('Confirm Promotion')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Vote Token Modal -->
<div class="modal custom--modal fade" id="voteTokenModal" tabindex="-1" aria-labelledby="voteTokenModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="voteTokenModalLabel">@if(auth()->user()->trend_votes > 0)@lang('Use Trend Votes')@else Buy Trend Votes @endif</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="las la-info-circle"></i> You've already used your free daily vote for this token. You can use your trend votes to give it an extra boost to trend higher on the homepage.
                </div>
                <form action="{{ route('token.trend-vote') }}" method="POST">
                    @csrf
                    <input type="hidden" name="chain_id" value="">
                    <input type="hidden" name="token_address" value="">
                    <div class="form-group">
                        <label>@lang('Token')</label>
                        <div class="input-group">
                            <span class="form-control token-name-display"></span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Available Trend Votes')</label>
                        <div class="input-group">
                            <span class="form-control">{{ auth()->user()->trend_votes }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Number of Trend Votes to Use')</label>
                        <div class="input-group">
                            <input type="number" name="quantity" class="form-control" min="1" max="{{ auth()->user()->trend_votes }}" value="1" required>
                        </div>
                    </div>
                    <div class="form-group mt-3 text-end">
                        @if(auth()->user()->trend_votes > 0)
                            <button type="submit" class="btn btn--base w-100">@lang('Use Trend Votes')</button>
                        @else
                            <a href="{{ route('user.plans.buy.trend.votes') }}" class="btn btn--base w-100">Buy Trend Votes</a>
                        @endif
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endauth

@push('style')
<link rel="stylesheet" href="{{ asset('assets/templates/dark/css/token-sorting.css') }}">
<style>
    .trending-tokens {
        background-color: var(--section-bg);
        margin-bottom: 20px;
    }
    .trending-tokens-header {
        padding-bottom: 5px;
        margin-bottom: 5px;
    }
    .fire-emoji {
        font-size: 24px;
    }
    .section-title {
        margin-left: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #243242;
        border-radius: 6px;
        padding: 6px 12px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        font-family: 'Chakra Petch', sans-serif;
        font-weight: 700;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        background: linear-gradient(to bottom, #ffffff, #BE8400);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .section-title:hover {
        transform: translateY(-2px);
        box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.5);
    }

    @media (max-width: 767px) {
        .section-title {
            font-size: 1.2rem;
            width: 100%;
            margin-bottom: 5px;
        }

        .bolt-emoji,
        .fire-emoji {
            font-size: 18px;
        }
    }
    .coin-icon {
        border-radius: 50%;
        object-fit: cover;
    }

    /* Token name and symbol alignment */
    .token-info-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .trending-tokens-table {
        color: var(--text-color);
        font-size: 14px;
    }
    .trending-tokens-table thead th {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-weight: 500;
        font-size: 13px;
        white-space: nowrap;
    }

    /* Ensure the first header column (rank number) has enough width */
    .trending-tokens-table thead th:first-child {
        min-width: 50px; /* Match the width of the first column in tbody */
    }
    .trending-tokens-table tbody td {
        padding: 12px 8px;
        vertical-align: middle;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        white-space: nowrap;
    }

    /* Ensure the first column (rank number) has enough width */
    .trending-tokens-table tbody td:first-child {
        min-width: 50px; /* Provide enough space for large rank numbers */
    }

    /* Responsive styles for token tables */
    @media (max-width: 1024px) {
        /* Container styling */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            position: relative;
        }

        .trending-tokens-table {
            min-width: 1200px; /* Ensure table has minimum width for scrolling */
            border-collapse: separate;
            border-spacing: 0;
        }

        .trending-tokens-table thead th,
        .trending-tokens-table tbody td {
            padding: 10px 6px;
            font-size: 13px;
        }

        /* Token name and symbol alignment for tablets */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        /* First column styling */
        .trending-tokens-table th:nth-child(1),
        .trending-tokens-table td:nth-child(1) {
            position: sticky;
            left: 0;
            z-index: 2;
        }

        /* Second column styling */
        .trending-tokens-table th:nth-child(2),
        .trending-tokens-table td:nth-child(2) {
            position: sticky;
            left: 40px; /* Width of the first column */
            z-index: 2;
        }

        /* Header cells in sticky columns */
        .trending-tokens-table thead th:nth-child(1),
        .trending-tokens-table thead th:nth-child(2) {
            z-index: 3;
        }

        /* Apply solid backgrounds to all cells in the first column */
        .trending-tokens-table tbody tr td:nth-child(1),
        .trending-tokens-table thead tr th:nth-child(1) {
            background-color: var(--section-bg) !important;
            /* Use a solid color as fallback */
            background: #0E1621 !important;
        }

        /* Apply solid backgrounds to all cells in the second column */
        .trending-tokens-table tbody tr td:nth-child(2),
        .trending-tokens-table thead tr th:nth-child(2) {
            background-color: var(--section-bg) !important;
            /* Use a solid color as fallback */
            background: #0E1621 !important;
        }

        /* Add shadow to the second column to indicate scrollable content */
        .trending-tokens-table tbody tr td:nth-child(2),
        .trending-tokens-table thead tr th:nth-child(2) {
            box-shadow: 5px 0 5px -2px rgba(0, 0, 0, 0.3);
        }

        /* Ensure backgrounds are applied even on hover states */
        .trending-tokens-table tbody tr:hover td:nth-child(1),
        .trending-tokens-table tbody tr:hover td:nth-child(2) {
            background-color: var(--section-bg) !important;
            background: #0E1621 !important;
        }

        /* Add shadow to indicate scrollable content */
        .table-responsive::after {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 15px;
            background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.3));
            pointer-events: none;
        }
    }

    /* Specific iPad adjustments */
    @media (width: 1024px) and (height: 1366px),
           (width: 1024px) and (height: 600px),
           (width: 768px) and (height: 1024px) {
        .trending-tokens-table {
            min-width: 1200px;
        }

        .trending-tokens-table thead th,
        .trending-tokens-table tbody td {
            padding: 10px 6px;
        }

        .coin-icon {
            width: 28px;
            height: 28px;
        }

        .rank-number {
            min-width: 22px;
            height: 22px;
            padding: 0 7px; /* Increased horizontal padding */
            font-size: 11px;
            overflow: visible;
            box-sizing: content-box;
            max-width: none;
            width: auto;
        }
    }
    .chain-badge {
        background-color: #BE8400;
        color: #ffffff;
        font-weight: 500;
    }
    .boost-badge {
        display: inline-flex;
        align-items: center;
        background-color: rgba(0, 123, 255, 0.1);
        border: 1px solid rgba(0, 123, 255, 0.3);
        border-radius: 12px;
        padding: 2px 8px;
        font-size: 12px;
        color: var(--primary-color);
    }
    .boost-btn {
        padding: 5px 15px;
        font-size: 12px;
        font-weight: 500;
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    .boost-btn:hover {
        background-color: var(--primary-color-hover);
        border-color: var(--primary-color-hover);
    }
    .softcap {
        color: rgba(255, 255, 255, 0.5);
    }
    .presale-badge-list {
        background-color: #BE8400;
        color: #ffffff;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.85rem;
        display: inline-block;
    }
    .presale-na-list {
        color: #ffffff !important;
        font-weight: bold !important;
        font-size: 0.9rem !important;
    }

    .presale-label {
        color: #BE8400 !important;
        font-weight: bold !important;
        font-size: 0.8rem !important;
    }

    .presale-value-list {
        color: #ffffff !important;
        font-weight: bold !important;
        font-size: 0.9rem !important;
    }
        /* Pagination responsive styles */
    /* Hide unnecessary elements */
    .pagination-wrapper .d-flex.justify-content-between.flex-fill,
    .pagination-container .d-flex.justify-content-between.flex-fill {
        display: none !important;
    }

    /* Main container for pagination */
    .pagination-wrapper,
    .pagination-container {
        width: 100%;
        overflow-x: auto;
        padding: 5px 0;
        margin: 10px 0;
        scrollbar-width: thin;
    }

    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        display: block !important;
        width: 100%;
    }

    /* Left align the "Showing X to Y of Z results" text */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child {
        text-align: left !important;
        margin-bottom: 10px;
    }

    /* Center the pagination buttons */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-wrapper nav,
    .pagination-wrapper .pagination,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-container nav,
    .pagination-container .pagination,
    .pagination-container nav > ul {
        display: flex;
        justify-content: center !important;
        width: 100%;
    }

    /* Improved pagination styling for all devices */
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
        margin: 0;
        padding: 0;
        justify-content: center;
        max-width: 100%;
    }

    /* Handle large number of pagination items - general fallback */
    .pagination.pagination-sm {
        justify-content: center;
        padding: 5px 0;
    }

    /* Ensure consistent spacing between pagination items */
    .pagination .page-item {
        margin: 3px;
    }

    /* Style for pagination links */
    .pagination .page-item .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 56px; /* Increased from 36px to accommodate up to 7 digits */
        height: 36px;
        padding: 0 10px;
        border-radius: 4px !important;
        font-size: 14px;
        transition: all 0.3s ease;
        background-color: rgba(0, 123, 255, 0.1);
        border-color: rgba(0, 123, 255, 0.3);
        color: var(--text-color);
        overflow: hidden; /* Prevent content from overflowing */
        text-overflow: ellipsis; /* Add ellipsis for any overflow */
        white-space: nowrap; /* Keep text on a single line */
    }

    /* Style for active pagination items */
    .pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    /* Style for forward and back buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item.previous .page-link,
    .pagination .page-item.next .page-link {
        font-size: 14px;
        padding: 0 12px;
        min-width: 40px; /* Slightly wider for navigation buttons */
    }

    /* Ensure arrow icons are visible */
    .pagination .page-item .page-link span[aria-hidden="true"] {
        display: inline-block;
        line-height: 1;
    }

    /* Style for disabled pagination items */
    .pagination .page-item.disabled .page-link {
        background-color: hsl(0deg 0% 100% / 40%);
        opacity: 0.7;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 575px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 3px 0;
            margin: 8px 0;
        }

        .pagination {
            gap: 3px;
        }

        .pagination .page-item {
            margin: 2px;
        }

        .pagination .page-item .page-link {
            min-width: 48px; /* Increased from 32px to accommodate larger numbers */
            height: 32px;
            font-size: 13px;
        }

        /* Adjust forward/back buttons on small screens */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 36px;
            padding: 0 10px;
        }
    }

    /* Responsive adjustments for very small screens */
    @media (max-width: 375px) {
        .pagination .page-item .page-link {
            min-width: 44px; /* Increased from 30px to accommodate larger numbers */
            height: 30px;
            font-size: 12px;
            padding: 0 6px; /* Slightly reduced padding to maintain overall width */
        }

        /* Token name and symbol alignment for extra small devices */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        .token-info-container .fw-bold {
            font-size: 12px;
        }

        .token-info-container .text-muted.small {
            font-size: 10px;
        }

        .rank-number {
            min-width: 18px;
            height: 18px;
            padding: 0 5px; /* Increased horizontal padding */
            font-size: 9px;
            overflow: visible;
            box-sizing: content-box;
            max-width: none;
            width: auto;
        }
    }

    /* Tablet-specific adjustments */
    @media (min-width: 768px) and (max-width: 1024px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 8px 0;
            margin: 12px 0;
        }

        .pagination {
            gap: 6px;
        }

        .pagination .page-item {
            margin: 3px;
        }

        .pagination .page-item .page-link {
            min-width: 58px; /* Increased from 38px to accommodate larger numbers */
            height: 38px;
            font-size: 15px;
        }

        /* Adjust forward/back buttons on tablets */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 42px;
            padding: 0 12px;
            font-size: 15px;
        }
    }

    /* Token action icons styling */
    .token-actions-wrapper {
        display: flex;
        justify-content: flex-start;
        gap: 10px;
        margin-bottom: 5px;
    }

    .token-action-icon {
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-block;
    }

    .promote-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.7));
    }

    .vote-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 100, 0, 0.7));
    }

    .watchlist-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 255, 0, 0.7));
    }

    .watchlist-icon.active {
        color: #FFD700;
    }

    .rank-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #BE8400;
        color: white;
        min-width: 24px;
        height: 24px;
        padding: 0 8px; /* Increased horizontal padding */
        border-radius: 4px;
        font-weight: bold;
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
        overflow: visible; /* Ensure text doesn't get cut off */
        box-sizing: content-box; /* Ensure padding is added to the content width */
        max-width: none; /* Remove any max-width constraints */
        width: auto; /* Allow the width to adjust to content */
    }

    .vote-count {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-weight: 500;
    }

    .fire-emoji-small {
        font-size: 14px;
        margin-right: 4px;
    }

    /* Search menu items styling */
    .search-menu-items {
        display: flex;
        gap: 10px;
        margin-left: 15px;
    }

    .search-menu-item-wrapper {
        background-color: #1C2631;
        border-radius: 8px;
        padding: 5px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .search-menu-item {
        display: inline-block;
        background-color: #BE8400;
        color: white;
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 13px;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .search-menu-item:hover {
        background-color: #d99600;
        color: white;
        text-decoration: none;
    }

    /* Mobile responsive styles for search menu */
    @media (max-width: 767px) {
        .promoted-coins-header,
        .trending-tokens-header {
            flex-direction: column;
            align-items: flex-start !important;
        }

        /* Token name and symbol alignment for mobile */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        .promoted-coins-header .d-flex,
        .trending-tokens-header .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
            width: 100%;
        }

        .search-menu-items {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-left: 0;
            margin-top: 10px;
            width: 100%;
        }

        .search-menu-item-wrapper {
            margin-bottom: 5px;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .search-menu-items {
            gap: 5px;
        }

        .search-menu-item {
            font-size: 12px;
            padding: 3px 8px;
        }

        .search-menu-item-wrapper {
            padding: 3px;
        }

        .rank-number {
            min-width: 20px;
            height: 20px;
            padding: 0 6px; /* Increased horizontal padding */
            font-size: 10px;
            overflow: visible;
            box-sizing: content-box;
            max-width: none;
            width: auto;
        }
    }

    /* Blockchain dropdown styling */
    .blockchain-dropdown-container {
        position: relative;
    }

    .blockchain-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background-color: #1C2631;
        border: 1px solid rgba(255, 255, 255, 0.1);
        padding: 5px;
        margin-top: 5px;
        min-width: 120px;
        max-height: 400px;
        overflow-y: auto;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        z-index: 1000;
        border-radius: 4px;
        /* Firefox */
        scrollbar-width: auto;
        scrollbar-color: rgba(var(--main), 0.7) rgba(255, 255, 255, 0.1);
    }

    /* Mobile responsive styles for blockchain dropdown */
    @media (max-width: 767px) {
        .blockchain-menu {
            position: absolute;
            left: 0;
            width: 200px;
            max-width: 90vw;
        }
    }

    /* Chrome, Edge, Safari */
    .blockchain-menu::-webkit-scrollbar {
        width: 12px;
    }
    .blockchain-menu::-webkit-scrollbar-thumb {
        background-color: rgba(var(--main), 0.7);
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 10px;
    }
    .blockchain-menu::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }

    .blockchain-item {
        display: block;
        color: white;
        padding: 6px 12px;
        font-size: 13px;
        border-radius: 4px;
        margin-bottom: 2px;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .blockchain-item:hover {
        background-color: #BE8400;
        color: white;
        text-decoration: none;
    }

    .blockchain-dropdown-container.active .blockchain-menu {
        display: block;
    }
</style>
@endpush

@push('script')
<script src="{{ asset('assets/templates/dark/js/token-sorting.js') }}"></script>
<script>
    // Global variable to track if a watchlist notification has been shown
    var watchlistNotificationShown = false;

    $(document).ready(function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // Reset notification flag when page is fully loaded
        watchlistNotificationShown = false;

        // Handle promote icon click
        $('.promote-icon').on('click', function() {
            var tokenAddress = $(this).data('token-address');
            var chainId = $(this).data('chain-id');
            var tokenSymbol = $(this).data('token-symbol');

            @auth
                // Show promote modal
                $('#promoteTokenModal').modal('show');
                $('#promoteTokenModal').find('input[name="chain_id"]').val(chainId);
                $('#promoteTokenModal').find('input[name="token_address"]').val(tokenAddress);
                $('#promoteTokenModal').find('.token-name-display').text(tokenSymbol);
            @else
                // Redirect to login page
                window.location.href = '{{ route("user.login") }}';
            @endauth
        });

        // Flag to prevent duplicate vote submissions
        var isVoteSubmitting = false;

        // Pre-check vote status for all tokens
        @auth
        function checkVoteStatus() {
            // Collect all tokens data
            var tokens = [];
            $('.vote-icon').each(function() {
                var icon = $(this);
                tokens.push({
                    chain_id: icon.data('chain-id'),
                    token_address: icon.data('token-address'),
                    element: icon[0] // Store the DOM element for later reference
                });
            });

            // If no tokens, exit early
            if (tokens.length === 0) return;

            // Make a single batch request instead of multiple individual requests
            $.ajax({
                url: '{{ route("token.vote.batch.check") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    tokens: tokens.map(function(t) {
                        return {
                            chain_id: t.chain_id,
                            token_address: t.token_address
                        };
                    })
                },
                success: function(response) {
                    if (response.success && response.results) {
                        // Store the results in a global object for quick lookup when clicking vote icons
                        window.voteStatusCache = {};

                        // Process each result
                        response.results.forEach(function(result, index) {
                            // Create a key for the cache
                            var cacheKey = result.chain_id + '_' + result.token_address;

                            // Store the result in the cache
                            window.voteStatusCache[cacheKey] = {
                                has_voted: result.has_voted,
                                has_negative_voted: result.has_negative_voted
                            };
                        });
                    }
                }
            });
        }

        // Call the function when page loads
        checkVoteStatus();
        @endauth

        // Handle vote icon click
        $('.vote-icon').on('click', function() {
            // Prevent duplicate submissions
            if (isVoteSubmitting) {
                return false;
            }

            var voteIcon = $(this);
            var tokenAddress = voteIcon.data('token-address');
            var chainId = voteIcon.data('chain-id');
            var tokenSymbol = voteIcon.data('token-symbol');

            @auth
                // Set flag to prevent duplicate submissions
                isVoteSubmitting = true;

                // Add visual feedback
                voteIcon.css('opacity', '0.5');

                // Check if we have cached vote status
                var cacheKey = chainId + '_' + tokenAddress;
                if (window.voteStatusCache && window.voteStatusCache[cacheKey]) {
                    var cachedStatus = window.voteStatusCache[cacheKey];

                    if (cachedStatus.has_voted) {
                        // User has already used their daily vote, show trend vote modal
                        $('#voteTokenModal').modal('show');
                        $('#voteTokenModal').find('input[name="chain_id"]').val(chainId);
                        $('#voteTokenModal').find('input[name="token_address"]').val(tokenAddress);
                        $('#voteTokenModal').find('.token-name-display').text(tokenSymbol);

                        // Reset submission flag and visual feedback
                        isVoteSubmitting = false;
                        voteIcon.css('opacity', '1');
                        return;
                    }

                    // User hasn't voted today, submit regular vote
                    var form = $('<form>', {
                        'method': 'POST',
                        'action': '{{ route("token.vote") }}'
                    }).append($('<input>', {
                        'type': 'hidden',
                        'name': '_token',
                        'value': '{{ csrf_token() }}'
                    })).append($('<input>', {
                        'type': 'hidden',
                        'name': 'chain_id',
                        'value': chainId
                    })).append($('<input>', {
                        'type': 'hidden',
                        'name': 'token_address',
                        'value': tokenAddress
                    }));

                    $('body').append(form);

                    // Use setTimeout to ensure we don't submit the form multiple times
                    setTimeout(function() {
                        form.submit();
                    }, 100);
                    return;
                }

                // If we don't have cached status, fall back to individual check
                $.ajax({
                    url: '{{ route("check.token.vote") }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        chain_id: chainId,
                        token_address: tokenAddress
                    },
                    success: function(response) {
                        if (response.has_voted) {
                            // User has already used their daily vote, show trend vote modal
                            $('#voteTokenModal').modal('show');
                            $('#voteTokenModal').find('input[name="chain_id"]').val(chainId);
                            $('#voteTokenModal').find('input[name="token_address"]').val(tokenAddress);
                            $('#voteTokenModal').find('.token-name-display').text(tokenSymbol);

                            // Reset submission flag and visual feedback
                            isVoteSubmitting = false;
                            voteIcon.css('opacity', '1');
                        } else {
                            // User hasn't voted today, submit regular vote
                            var form = $('<form>', {
                                'method': 'POST',
                                'action': '{{ route("token.vote") }}'
                            }).append($('<input>', {
                                'type': 'hidden',
                                'name': '_token',
                                'value': '{{ csrf_token() }}'
                            })).append($('<input>', {
                                'type': 'hidden',
                                'name': 'chain_id',
                                'value': chainId
                            })).append($('<input>', {
                                'type': 'hidden',
                                'name': 'token_address',
                                'value': tokenAddress
                            }));

                            $('body').append(form);

                            // Use setTimeout to ensure we don't submit the form multiple times
                            setTimeout(function() {
                                form.submit();
                            }, 100);
                        }
                    },
                    error: function() {
                        // On error, default to showing the modal
                        $('#voteTokenModal').modal('show');
                        $('#voteTokenModal').find('input[name="chain_id"]').val(chainId);
                        $('#voteTokenModal').find('input[name="token_address"]').val(tokenAddress);
                        $('#voteTokenModal').find('.token-name-display').text(tokenSymbol);

                        // Reset submission flag and visual feedback
                        isVoteSubmitting = false;
                        voteIcon.css('opacity', '1');
                    }
                });
            @else
                // Redirect to login page
                window.location.href = '{{ route("user.login") }}';
            @endauth
        });

        // Handle watchlist icon click
        $('.watchlist-icon').on('click', function() {
            var tokenAddress = $(this).data('token-address');
            var chainId = $(this).data('chain-id');
            var tokenSymbol = $(this).data('token-symbol');
            var icon = $(this);

            @auth
                // Toggle watchlist status
                $.ajax({
                    url: '{{ route("token.watchlist.toggle") }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        chain_id: chainId,
                        token_address: tokenAddress
                    },
                    success: function(response) {
                        if (response.success) {
                            if (response.is_watchlisted) {
                                icon.addClass('active');
                                icon.attr('title', 'Already in watchlist');
                                var tooltip = bootstrap.Tooltip.getInstance(icon[0]);
                                if (tooltip) {
                                    tooltip.dispose();
                                }
                                new bootstrap.Tooltip(icon[0], {
                                    title: 'Already in watchlist'
                                });

                                // Check if token was already in watchlist
                                if (response.already_exists) {
                                    if (!watchlistNotificationShown) {
                                        notify('info', 'Token already in watchlist');
                                        watchlistNotificationShown = true;
                                        // Reset the flag after 2 seconds to allow future notifications
                                        setTimeout(function() {
                                            watchlistNotificationShown = false;
                                        }, 2000);
                                    }
                                } else {
                                    // Token was just added
                                    if (!watchlistNotificationShown) {
                                        notify('success', 'Token added to watchlist');
                                        watchlistNotificationShown = true;
                                        // Reset the flag after 2 seconds to allow future notifications
                                        setTimeout(function() {
                                            watchlistNotificationShown = false;
                                        }, 2000);
                                    }
                                }
                            } else {
                                icon.removeClass('active');
                                icon.attr('title', 'Add to watchlist');
                                var tooltip = bootstrap.Tooltip.getInstance(icon[0]);
                                if (tooltip) {
                                    tooltip.dispose();
                                }
                                new bootstrap.Tooltip(icon[0], {
                                    title: 'Add to watchlist'
                                });
                                if (!watchlistNotificationShown) {
                                    notify('success', 'Token removed from watchlist');
                                    watchlistNotificationShown = true;
                                    // Reset the flag after 2 seconds to allow future notifications
                                    setTimeout(function() {
                                        watchlistNotificationShown = false;
                                    }, 2000);
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Watchlist error:', xhr.responseText);
                        // Don't show error notification as the token might still be added
                        // notify('error', 'Something went wrong');
                    }
                });
            @else
                // Redirect to login page
                window.location.href = '{{ route("user.login") }}';
            @endauth
        });

        // Check watchlist status for all tokens
        @auth
        function checkWatchlistStatus() {
            // Collect all tokens data
            var tokens = [];
            $('.watchlist-icon').each(function() {
                var icon = $(this);
                tokens.push({
                    chain_id: icon.data('chain-id'),
                    token_address: icon.data('token-address'),
                    element: icon[0] // Store the DOM element for later reference
                });
            });

            // If no tokens, exit early
            if (tokens.length === 0) return;

            // Make a single batch request instead of multiple individual requests
            $.ajax({
                url: '{{ route("token.watchlist.batch.check") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    tokens: tokens.map(function(t) {
                        return {
                            chain_id: t.chain_id,
                            token_address: t.token_address
                        };
                    })
                },
                success: function(response) {
                    if (response.success && response.results) {
                        // Process each result
                        response.results.forEach(function(result, index) {
                            if (result.is_watchlisted) {
                                var icon = $(tokens[index].element);
                                icon.addClass('active');
                                icon.attr('title', 'Remove from watchlist');
                                var tooltip = bootstrap.Tooltip.getInstance(icon[0]);
                                if (tooltip) {
                                    tooltip.dispose();
                                }
                                new bootstrap.Tooltip(icon[0], {
                                    title: 'Remove from watchlist'
                                });
                            }
                        });
                    }
                }
            });
        }

        // Call the function when page loads
        checkWatchlistStatus();
        @endauth

        // Initialize blockchain dropdown
        $('#trendingBlockchainToggle').on('click', function(e) {
            e.preventDefault();
            $(this).parent('.blockchain-dropdown-container').toggleClass('active');
        });

        // Close dropdown when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.blockchain-dropdown-container').length) {
                $('.blockchain-dropdown-container').removeClass('active');
            }
        });

        // Handle trending tokens pagination scrolling
        $('.trending-pagination-link').on('click', function(e) {
            // Store the target URL
            var targetUrl = $(this).attr('href');
            
            // Prevent default navigation
            e.preventDefault();
            
            // Show loading indicator
            $('body').append('<div id="pagination-loading" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 8px; z-index: 9999; font-size: 16px;"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
            
            // Navigate to the new page
            window.location.href = targetUrl;
        });

        // Auto-scroll to trending tokens section if we're on a trending page
        @if(request()->query('trending_page') && request()->query('trending_page') > 1)
        $(window).on('load', function() {
            // Show loading indicator during scroll
            $('body').append('<div id="scroll-loading" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 15px 25px; border-radius: 8px; z-index: 9999; font-size: 14px;"><i class="fas fa-arrow-down fa-bounce"></i> Scrolling to tokens...</div>');
            
            setTimeout(function() {
                $('html, body').animate({
                    scrollTop: $('.trending-tokens').offset().top - 20
                }, 400, function() {
                    // Remove loading indicator after scroll completes
                    $('#scroll-loading').fadeOut(200, function() {
                        $(this).remove();
                    });
                });
            }, 50);
        });
        @endif
    });
</script>
@endpush
