<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserBanner;
use Illuminate\Http\Request;

class ManageAdsController extends Controller
{
    /**
     * Display a listing of all ads.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $pageTitle = 'Manage Ads';
        $search = $request->search;
        $status = $request->status;
        $emptyMessage = 'No ads found';

        $adsQuery = UserBanner::with(['user', 'adPosition', 'impressions']);

        // Apply search filter if provided
        if ($search) {
            $adsQuery->where(function($query) use ($search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('username', 'like', "%$search%");
                })->orWhereHas('adPosition', function($q) use ($search) {
                    $q->where('name', 'like', "%$search%");
                });
            });
        }

        // Apply status filter if provided
        if ($status !== null && $status !== '') {
            $adsQuery->where('status', $status);
        }

        $ads = $adsQuery->orderBy('created_at', 'desc')
            ->paginate(getPaginate(10)); // Set pagination to 10 items per page

        return view('admin.advertise.manage_ads', compact('pageTitle', 'ads', 'search', 'status', 'emptyMessage'));
    }
}
