<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CryptoSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_key',
        'display_count',
        'last_updated',
        'is_active'
    ];

    protected $casts = [
        'display_count' => 'integer',
        'last_updated' => 'datetime',
        'is_active' => 'boolean'
    ];
} 