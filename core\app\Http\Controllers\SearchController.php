<?php

namespace App\Http\Controllers;

use App\Models\DexscreenerToken;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{
    protected $activeTemplate;

    public function __construct()
    {
        $this->activeTemplate = activeTemplate();
    }

    /**
     * Search for tokens based on query
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        $pageTitle = 'Search Results: ' . $query;

        if (empty($query)) {
            return redirect()->route('home');
        }

        // Set pagination parameters
        $perPage = 100;
        $page = $request->input('page', 1);

        // Build the base query with database-level vote count calculation
        $baseQuery = DexscreenerToken::select([
            'dexscreener_tokens.*',
            DB::raw('(
                SELECT COALESCE(SUM(CASE WHEN is_negative = 0 THEN 1 ELSE 0 END), 0) + COALESCE(trend_votes, 0)
                FROM token_votes 
                WHERE token_votes.chain_id COLLATE utf8mb4_unicode_ci = dexscreener_tokens.chain_id COLLATE utf8mb4_unicode_ci
                AND token_votes.token_address COLLATE utf8mb4_unicode_ci = dexscreener_tokens.token_address COLLATE utf8mb4_unicode_ci
            ) as total_vote_count')
        ])
        ->where(function($q) {
            $q->where('is_verified', true)
              ->orWhereNull('submitted_by_user_id'); // Admin added tokens have null user_id
        });

        // Apply search filters based on query type
        if (strtolower($query) === 'presale') {
            // Find tokens that are presale tokens (token address starts with 'presale_')
            $baseQuery->where('token_address', 'like', 'presale_%');
        } elseif (in_array(strtolower($query), ['fair launch', 'fairlaunch', 'fair_launch'])) {
            // Get fair launch addresses from SubmitCoin table
            $fairLaunchAddresses = \App\Models\SubmitCoin::where('is_fair_launch', 1)
                ->pluck('contract_address')
                ->toArray();

            // Find tokens that are fair launch tokens
            $baseQuery->where(function($q) use ($fairLaunchAddresses) {
                $q->where('token_address', 'like', 'fairlaunch_%')
                  ->orWhereIn('token_address', $fairLaunchAddresses);
            });
        } elseif (strtolower($query) === 'newest') {
            // Find tokens added within the last 24 hours, ordered by newest first
            $baseQuery->where('created_at', '>=', now()->subHours(24))
                ->orderBy('created_at', 'desc');
        } else {
            // General search by name, symbol, address, or chain
            $baseQuery->where(function($q) use ($query) {
                $q->where('token_name', 'like', '%' . $query . '%')
                  ->orWhere('token_symbol', 'like', '%' . $query . '%')
                  ->orWhere('token_address', 'like', '%' . $query . '%')
                  ->orWhere('chain_id', 'like', '%' . $query . '%');
            });
        }

        // Apply ordering based on search type
        if (strtolower($query) !== 'newest') {
            // For all searches except 'newest', order by vote count (highest first), then by creation date
            $baseQuery->orderBy('total_vote_count', 'desc')
                     ->orderBy('created_at', 'desc');
        }

        // Get paginated results with vote counts already calculated
        $tokens = $baseQuery->paginate($perPage);

        // Calculate global ranks for the current page
        $startRank = ($page - 1) * $perPage + 1;

        // Process each token to add additional data
        foreach ($tokens as $index => $token) {
            // Set the vote count from the calculated field
            $token->vote_count = $token->total_vote_count ?? 0;

            // Set global rank based on position across all pages
            $token->global_rank = $startRank + $index;

            // Check if this is a presale or fair launch token
            $isPresale = strpos($token->token_address, 'presale_') === 0;
            $isFairLaunch = strpos($token->token_address, 'fairlaunch_') === 0;
            $token->is_presale = $isPresale;
            $token->is_fair_launch = $isFairLaunch;

            // Get the corresponding SubmitCoin record for presale or fair launch tokens
            if ($isPresale || $isFairLaunch) {
                $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token->token_address)
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($submitCoin) {
                    // For presale tokens, use the SubmitCoin value
                    if (!$isPresale) {
                        $token->is_presale = $submitCoin->is_presale == 1;
                    }

                    // For fair launch tokens, keep it as fair launch if the address starts with 'fairlaunch_'
                    if (!$isFairLaunch) {
                        $token->is_fair_launch = $submitCoin->is_fair_launch == 1;
                    }

                    // Add additional data from the SubmitCoin record
                    $token->presale_start_date = $submitCoin->presale_start_date;
                    $token->presale_end_date = $submitCoin->presale_end_date;
                    $token->presale_url = $submitCoin->presale_url;
                    $token->softcap = $submitCoin->softcap;
                    $token->hardcap = $submitCoin->hardcap;
                }
            } else {
                // Check if this token is in the SubmitCoin table and marked as a fair launch token
                $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token->token_address)
                    ->where('is_fair_launch', 1)
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($submitCoin) {
                    $token->is_fair_launch = true;
                    $token->presale_start_date = $submitCoin->presale_start_date;
                    $token->presale_end_date = $submitCoin->presale_end_date;
                    $token->presale_url = $submitCoin->presale_url;
                    $token->softcap = $submitCoin->softcap;
                    $token->hardcap = $submitCoin->hardcap;
                }
            }
        }

        return view($this->activeTemplate . 'search.results', compact('pageTitle', 'tokens', 'query'));
    }
}
