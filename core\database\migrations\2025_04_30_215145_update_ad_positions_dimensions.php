<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\AdPosition;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update header left ad dimensions
        $headerLeftAd = AdPosition::where('key', 'header_left')->first();
        if ($headerLeftAd) {
            $headerLeftAd->size = '570x100 (Desktop), 350x100 (Tablet), 160x100 (Mobile)';
            $headerLeftAd->save();
        }

        // Update header right ad dimensions
        $headerRightAd = AdPosition::where('key', 'header_right')->first();
        if ($headerRightAd) {
            $headerRightAd->size = '570x100 (Desktop), 350x100 (Tablet), 160x100 (Mobile)';
            $headerRightAd->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert header left ad dimensions
        $headerLeftAd = AdPosition::where('key', 'header_left')->first();
        if ($headerLeftAd) {
            $headerLeftAd->size = '468x60 (Desktop), 320x50 (Tablet), 160x50 (Mobile)';
            $headerLeftAd->save();
        }

        // Revert header right ad dimensions
        $headerRightAd = AdPosition::where('key', 'header_right')->first();
        if ($headerRightAd) {
            $headerRightAd->size = '468x60 (Desktop), 320x50 (Tablet), 160x50 (Mobile)';
            $headerRightAd->save();
        }
    }
};
