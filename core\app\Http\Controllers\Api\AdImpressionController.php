<?php

namespace App\Http\Controllers\Api;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\AdImpression;
use App\Models\UserBanner;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AdImpressionController extends Controller
{
    /**
     * Record an impression for a banner
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function recordImpression(Request $request)
    {
        $request->validate([
            'banner_id' => 'required|integer|exists:user_banners,id',
        ]);

        $bannerId = $request->banner_id;

        // Check if the banner exists and is active
        $banner = UserBanner::where('id', $bannerId)
            ->where('status', 1) // Only count impressions for active banners
            ->first();

        if (!$banner) {
            return response()->json(['success' => false, 'message' => 'Banner not found or not active'], 404);
        }

        // Get the user who owns the banner
        $user = User::find($banner->user_id);

        // Check if user has enough ad credits for this impression
        if ($user->ad_credits <= 0) {
            // If no credits, don't record impression and return error
            return response()->json(['success' => false, 'message' => 'User does not have enough ad credits'], 403);
        }

        // Deduct 1 credit for this impression
        $user->ad_credits -= 1;
        $user->save();

        // Record the impression
        AdImpression::incrementImpression($bannerId);

        // If user has run out of credits, pause all their active banners
        if ($user->ad_credits <= 0) {
            // Find all active banners for this user and pause them
            $pausedCount = UserBanner::where('user_id', $user->id)
                ->where('status', Status::ENABLE) // Status 1 = active/enabled
                ->update(['status' => 3]); // Status 3 = paused

            // Log the action for debugging
            Log::info('User ran out of ad credits, paused banners', [
                'user_id' => $user->id,
                'banners_paused' => $pausedCount
            ]);
        }

        return response()->json(['success' => true]);
    }
}
