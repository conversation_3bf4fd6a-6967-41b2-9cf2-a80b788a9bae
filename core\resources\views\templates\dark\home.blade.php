@extends($activeTemplate . 'layouts.frontend')
@section('content')
    {{-- Always include the promoted coins section first --}}
    @include($activeTemplate . 'sections.promoted_coins')

    {{-- Include the trending tokens section --}}
    @include($activeTemplate . 'sections.trending_tokens')

    {{-- Include other sections from the database --}}
    @if (isset($sections) && $sections->secs != null)
        @php
            $sectionList = json_decode($sections->secs);
            if (!is_array($sectionList)) {
                $sectionList = [];
            }
        @endphp

        @foreach($sectionList as $sec)
            @if($sec != 'promoted_coins') {{-- Skip promoted_coins since we already included it --}}
                @include($activeTemplate . 'sections.' . $sec)
            @endif
        @endforeach
    @endif
@endsection
