<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\AdPosition;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create new ad positions for banners above Promoted Coins and Trending Tokens
        $adPositions = [
            [
                'name' => 'Promoted Coins Top Banner',
                'key' => 'promoted_coins_top',
                'size' => 'Full Width (Responsive)',
                'description' => 'Banner displayed above the Promoted Coins section',
                'status' => 1,
            ],
            [
                'name' => 'Trending Tokens Top Banner',
                'key' => 'trending_tokens_top',
                'size' => 'Full Width (Responsive)',
                'description' => 'Banner displayed above the Trending Tokens section',
                'status' => 1,
            ],
        ];

        foreach ($adPositions as $position) {
            // Check if position already exists
            $exists = AdPosition::where('key', $position['key'])->exists();
            if (!$exists) {
                AdPosition::create($position);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the added ad positions
        AdPosition::whereIn('key', [
            'promoted_coins_top',
            'trending_tokens_top',
        ])->delete();
    }
};
