<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserTokenPurchase extends Model
{
    use HasFactory;

    // Token transfer status constants
    const STATUS_PENDING = 'Pending';
    const STATUS_COMPLETED = 'Completed';

    protected $fillable = [
        'user_id',
        'presale_id',
        'token_name',
        'token_symbol',
        'amount',
        'tokens',
        'price',
        'transaction_id',
        'token_transfer_status'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'tokens' => 'decimal:8',
        'price' => 'decimal:8',
    ];

    /**
     * Get the user who purchased the tokens
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the presale associated with this purchase
     */
    public function presale()
    {
        return $this->belongsTo(Presale::class);
    }

    /**
     * Get the transaction associated with this purchase
     */
    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }
}
