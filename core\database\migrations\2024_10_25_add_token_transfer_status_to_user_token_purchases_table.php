<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_token_purchases', function (Blueprint $table) {
            if (!Schema::hasColumn('user_token_purchases', 'token_transfer_status')) {
                $table->string('token_transfer_status')->default('Pending')->after('transaction_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_token_purchases', function (Blueprint $table) {
            if (Schema::hasColumn('user_token_purchases', 'token_transfer_status')) {
                $table->dropColumn('token_transfer_status');
            }
        });
    }
};
