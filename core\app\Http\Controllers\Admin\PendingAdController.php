<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserBanner;
use App\Models\AdPosition;
use Illuminate\Http\Request;

class PendingAdController extends Controller
{
    public function index()
    {
        $pageTitle = 'Pending Ads';
        $pendingAds = UserBanner::where('status', 0)
            ->with(['user', 'adPosition'])
            ->orderBy('created_at', 'desc')
            ->paginate(getPaginate());
            
        return view('admin.advertise.pending_ads', compact('pageTitle', 'pendingAds'));
    }
    
    public function approve($id)
    {
        $banner = UserBanner::findOrFail($id);
        
        if ($banner->status != 0) {
            $notify[] = ['error', 'This banner is not in pending status'];
            return back()->withNotify($notify);
        }
        
        $banner->status = 1; // Approved
        $banner->save();
        
        $notify[] = ['success', 'Banner has been approved successfully'];
        return back()->withNotify($notify);
    }
    
    public function reject(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'reason' => 'required|string|max:255'
        ]);
        
        $banner = UserBanner::findOrFail($request->id);
        
        if ($banner->status != 0) {
            $notify[] = ['error', 'This banner is not in pending status'];
            return back()->withNotify($notify);
        }
        
        $banner->status = 2; // Rejected
        $banner->rejection_reason = $request->reason;
        $banner->save();
        
        $notify[] = ['success', 'Banner has been rejected successfully'];
        return back()->withNotify($notify);
    }
    
    public function delete($id)
    {
        $banner = UserBanner::findOrFail($id);
        
        // Delete the banner image
        fileManager()->removeFile(getFilePath('ads_images') . '/' . $banner->image);
        
        // Delete the banner record
        $banner->delete();
        
        $notify[] = ['success', 'Banner has been deleted successfully'];
        return back()->withNotify($notify);
    }
}
