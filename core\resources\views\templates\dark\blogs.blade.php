@extends($activeTemplate . 'layouts.frontend')

@section('content')
    <section class="blog py-100">
        <div class="container">
            <div class="row gy-4 justify-content-center">
                @foreach ($blogs as $blog)
                    <div class="col-lg-4 col-md-6">
                        <div class="blog-item">
                            <div class="blog-item__thumb">
                                <a class="blog-item__thumb-link" href="{{ route('blog.details', $blog->slug) }}"><img
                                        src="{{ frontendImage('blog', 'thumb_' . @$blog->data_values->blog_image, '300x200') }}"
                                        alt=""></a>
                                <span class="blog-item__date"> <span class="blog-item__date-icon"><i
                                            class="fas fa-calendar-alt"></i></span>
                                    {{ showDateTime($blog->created_at, 'd, M') }} </span>
                            </div>
                            <div class="blog-item__content">
                                <h4 class="blog-item__title"><a class="blog-item__title-link"
                                        href="{{ route('blog.details', $blog->slug) }}">{{ __($blog->data_values->title) }}</a>
                                </h4>
                                <p class="blog-item__desc">@php echo strLimit(strip_tags($blog->data_values->description), 80) @endphp</p>
                                <a class="btn--simple" href="{{ route('blog.details', $blog->slug) }}">@lang('Read More')
                                    <span class="btn--simple__icon"><i class="las la-chevron-right"></i></span> </a>
                            </div>
                        </div>
                    </div>
                @endforeach

                <div class="pagination-wrapper mt-4 mt-md-5">
                    {{ paginateLinks($blogs) }}
                </div>
            </div>
        </div>
    </section>

    @if ($sections->secs != null)
        @foreach (json_decode($sections->secs) as $sec)
            @include($activeTemplate . 'sections.' . $sec)
        @endforeach
    @endif

@endsection

@push('style')
    <style>
        /* Pagination responsive styles */
        /* Hide unnecessary elements */
        .pagination-wrapper .d-flex.justify-content-between.flex-fill {
            display: none !important;
        }

        /* Main container for pagination */
        .pagination-wrapper {
            width: 100%;
            overflow-x: auto;
            padding: 5px 0;
            margin: 10px 0;
            scrollbar-width: thin;
        }

        .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
            display: block !important;
            width: 100%;
        }

        /* Left align the "Showing X to Y of Z results" text */
        .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child {
            text-align: left !important;
            margin-bottom: 10px;
        }

        /* Center the pagination buttons */
        .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
        .pagination-wrapper nav,
        .pagination-wrapper .pagination {
            display: flex;
            justify-content: center !important;
            width: 100%;
        }

        /* Improved pagination styling for all devices */
        .pagination {
            flex-wrap: wrap;
            gap: 5px;
            margin: 0;
            padding: 0;
            justify-content: center;
            max-width: 100%;
        }

        /* Handle large number of pagination items - general fallback */
        .pagination.pagination-sm {
            justify-content: center;
            padding: 5px 0;
        }

        /* Ensure consistent spacing between pagination items */
        .pagination .page-item {
            margin: 3px;
        }

        /* Style for pagination links */
        .pagination .page-item .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
            padding: 0 10px;
            border-radius: 4px !important;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        /* Style for forward and back buttons */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            font-size: 14px;
            padding: 0 12px;
            min-width: 40px; /* Slightly wider for navigation buttons */
        }

        /* Ensure arrow icons are visible */
        .pagination .page-item .page-link span[aria-hidden="true"] {
            display: inline-block;
            line-height: 1;
        }

        /* Style for disabled pagination items */
        .pagination .page-item.disabled .page-link {
            background-color: hsl(0deg 0% 100% / 40%);
            opacity: 0.7;
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 575px) {
            .pagination-wrapper {
                padding: 3px 0;
                margin: 8px 0;
            }

            .pagination {
                gap: 3px;
            }

            .pagination .page-item {
                margin: 2px;
            }

            .pagination .page-item .page-link {
                min-width: 32px;
                height: 32px;
                font-size: 13px;
            }

            /* Adjust forward/back buttons on small screens */
            .pagination .page-item:first-child .page-link,
            .pagination .page-item:last-child .page-link,
            .pagination .page-item.previous .page-link,
            .pagination .page-item.next .page-link {
                min-width: 36px;
                padding: 0 10px;
            }
        }

        /* Responsive adjustments for very small screens */
        @media (max-width: 375px) {
            .pagination .page-item .page-link {
                min-width: 30px;
                height: 30px;
                font-size: 12px;
                padding: 0 8px;
            }
        }

        /* Tablet-specific adjustments */
        @media (min-width: 768px) and (max-width: 1024px) {
            .pagination-wrapper {
                padding: 8px 0;
                margin: 12px 0;
            }

            .pagination {
                gap: 6px;
            }

            .pagination .page-item {
                margin: 3px;
            }

            .pagination .page-item .page-link {
                min-width: 38px;
                height: 38px;
                font-size: 15px;
            }

            /* Adjust forward/back buttons on tablets */
            .pagination .page-item:first-child .page-link,
            .pagination .page-item:last-child .page-link,
            .pagination .page-item.previous .page-link,
            .pagination .page-item.next .page-link {
                min-width: 42px;
                padding: 0 12px;
                font-size: 15px;
            }
        }
    </style>
@endpush
