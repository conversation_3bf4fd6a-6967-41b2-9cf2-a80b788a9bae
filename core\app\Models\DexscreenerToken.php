<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DexscreenerToken extends Model
{
    use HasFactory;

    // Make sure timestamps are enabled (Laravel default is true, but let's be explicit)
    public $timestamps = true;

    protected $casts = [
        'metadata' => 'object',
        'price_change_24h' => 'string',
        'price_usd' => 'string',
        'volume_24h' => 'float',
        'market_cap' => 'float',
        'liquidity_usd' => 'float',
        'price_change_5m' => 'string',
        'price_change_1h' => 'string',
        'price_change_6h' => 'string',
        'txn_24h' => 'integer',
        'token_age' => 'integer',
        'buy_count' => 'integer',
        'sell_count' => 'integer',
        'is_verified' => 'boolean',
        'submitted_by_user_id' => 'integer',
        'trend_votes' => 'integer',
    ];

    protected $fillable = [
        'chain_id',
        'token_address',
        'token_name',
        'token_symbol',
        'price_usd',
        'price_change_24h',
        'volume_24h',
        'market_cap',
        'liquidity_usd',
        'metadata',
        'pair_address',
        'dex_id',
        'image_url',
        'price_change_5m',
        'price_change_1h',
        'price_change_6h',
        'txn_24h',
        'token_age',
        'buy_count',
        'sell_count',
        'is_verified',
        'submitted_by_user_id',
        'trend_votes',
    ];

    /**
     * Get the metadata attribute with proper object conversion.
     */
    public function getMetadataAttribute($value)
    {
        if (is_string($value)) {
            return json_decode($value);
        }
        return $value;
    }

    /**
     * Get the price_usd attribute with proper formatting
     */
    public function getPriceUsdAttribute($value)
    {
        // Ensure the value is a string to maintain precision
        return (string)$value;
    }

    /**
     * Set the price_usd attribute with proper formatting
     */
    public function setPriceUsdAttribute($value)
    {
        $this->attributes['price_usd'] = (string)$value;
    }

    /**
     * Get the price_change_24h attribute with proper formatting
     */
    public function getPriceChange24hAttribute($value)
    {
        // Ensure the value is a string to maintain precision
        return (string)$value;
    }

    /**
     * Set the price_change_24h attribute with proper formatting
     */
    public function setPriceChange24hAttribute($value)
    {
        $this->attributes['price_change_24h'] = (string)$value;
    }
}