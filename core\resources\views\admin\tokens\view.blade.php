@extends('admin.layouts.app')
@section('panel')
<div class="row">
    <div class="col-xl-4 col-lg-5 col-md-5">
        <div class="card b-radius--10 overflow-hidden box--shadow1">
            <div class="card-body p-0">
                <div class="p-3 bg--white">
                    <div class="text-center">
                        @if($token->image_url)
                            <img src="{{ getImage(getFilePath('coinLogos').'/'.$token->image_url, getFileSize('coinLogos')) }}" alt="@lang('Token Logo')" class="b-radius--8 w-120">
                        @else
                            <img src="{{ asset('assets/images/default.png') }}" alt="@lang('Token Logo')" class="b-radius--8 w-120">
                        @endif
                        <h4 class="mt-3">{{ $token->token_name }}</h4>
                        <h6 class="text--muted">({{ $token->token_symbol }})</h6>
                    </div>
                    <div class="mt-3">
                        <h5 class="mb-2">@lang('Blockchain')</h5>
                        <span class="badge badge--primary">{{ $token->chain_id }}</span>
                    </div>
                    <div class="mt-3">
                        <h5 class="mb-2">@lang('Contract Address')</h5>
                        <span class="font-weight-bold">{{ $token->token_address }}</span>
                    </div>
                    <div class="mt-3">
                        <h5 class="mb-2">@lang('Status')</h5>
                        @if($token->is_verified)
                            <span class="badge badge--success">@lang('Verified')</span>
                        @else
                            <span class="badge badge--warning">@lang('Pending')</span>
                        @endif
                    </div>
                    <div class="mt-3">
                        <h5 class="mb-2">@lang('Submitted By')</h5>
                        @if($user = \App\Models\User::find($token->submitted_by_user_id))
                            <a href="{{ route('admin.users.detail', $user->id) }}">{{ $user->username }} ({{ $user->email }})</a>
                        @else
                            <span class="text-danger">@lang('User not found')</span>
                        @endif
                    </div>
                    <div class="mt-3">
                        <h5 class="mb-2">@lang('Submission Date')</h5>
                        <span>{{ showDateTime($token->created_at) }}</span>
                    </div>
                    @if(!$token->is_verified)
                        <div class="mt-4 d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn--success mr-2 verifyBtn" data-id="{{ $token->id }}" data-token="{{ $token->token_name }} ({{ $token->token_symbol }})">
                                <i class="las la-check-circle"></i> @lang('Verify')
                            </button>
                            <button type="button" class="btn btn--danger rejectBtn" data-id="{{ $token->id }}" data-token="{{ $token->token_name }} ({{ $token->token_symbol }})">
                                <i class="las la-times-circle"></i> @lang('Reject')
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-8 col-lg-7 col-md-7">
        <div class="card b-radius--10 overflow-hidden box--shadow1">
            <div class="card-body p-0">
                <div class="p-3 bg--white">
                    <div class="nav-tabs-custom">
                        <ul class="nav nav-tabs">
                            <li class="nav-item">
                                <a href="#details" data-toggle="tab" class="nav-link active">@lang('Details')</a>
                            </li>
                            <li class="nav-item">
                                <a href="#links" data-toggle="tab" class="nav-link">@lang('Links')</a>
                            </li>
                        </ul>
                        <div class="tab-content mt-4">
                            <div class="tab-pane active" id="details">
                                @php
                                    if (is_object($token->metadata)) {
                                        $metadata = $token->metadata;
                                    } elseif (is_string($token->metadata)) {
                                        $metadata = json_decode($token->metadata ?? '{}');
                                    } else {
                                        $metadata = new \stdClass();
                                    }
                                @endphp

                                @if($metadata && isset($metadata->description))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Description')</h5>
                                    <p>{{ $metadata->description }}</p>
                                </div>
                                @endif

                                @if($metadata && isset($metadata->is_presale))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Presale Status')</h5>
                                    <p>
                                        @if($metadata->is_presale)
                                            <span class="badge badge--primary">@lang('Yes')</span>
                                        @else
                                            <span class="badge badge--dark">@lang('No')</span>
                                        @endif
                                    </p>
                                </div>
                                @endif

                                @if($token->price_usd)
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Price (USD)')</h5>
                                    <p>${{ $token->price_usd }}</p>
                                </div>
                                @endif

                                @if($token->price_change_24h)
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('24h Price Change')</h5>
                                    <p class="{{ $token->price_change_24h > 0 ? 'text-success' : 'text-danger' }}">
                                        {{ $token->price_change_24h > 0 ? '+' : '' }}{{ $token->price_change_24h }}%
                                    </p>
                                </div>
                                @endif

                                @if($token->volume_24h)
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('24h Volume')</h5>
                                    <p>${{ number_format($token->volume_24h, 2) }}</p>
                                </div>
                                @endif

                                @if($token->market_cap)
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Market Cap')</h5>
                                    <p>${{ number_format($token->market_cap, 2) }}</p>
                                </div>
                                @endif

                                @if($token->liquidity_usd)
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Liquidity (USD)')</h5>
                                    <p>${{ number_format($token->liquidity_usd, 2) }}</p>
                                </div>
                                @endif
                            </div>
                            <div class="tab-pane" id="links">
                                @php
                                    if (is_object($token->metadata)) {
                                        $metadata = $token->metadata;
                                    } elseif (is_string($token->metadata)) {
                                        $metadata = json_decode($token->metadata ?? '{}');
                                    } else {
                                        $metadata = new \stdClass();
                                    }
                                @endphp

                                @if($metadata && isset($metadata->website))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Website')</h5>
                                    <p><a href="{{ $metadata->website }}" target="_blank">{{ $metadata->website }}</a></p>
                                </div>
                                @endif

                                @if($metadata && isset($metadata->telegram))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Telegram')</h5>
                                    <p><a href="{{ $metadata->telegram }}" target="_blank">{{ $metadata->telegram }}</a></p>
                                </div>
                                @endif

                                @if($metadata && isset($metadata->twitter))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Twitter')</h5>
                                    <p><a href="{{ $metadata->twitter }}" target="_blank">{{ $metadata->twitter }}</a></p>
                                </div>
                                @endif

                                @if($metadata && isset($metadata->discord))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Discord')</h5>
                                    <p><a href="{{ $metadata->discord }}" target="_blank">{{ $metadata->discord }}</a></p>
                                </div>
                                @endif

                                @if($metadata && isset($metadata->facebook))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Facebook')</h5>
                                    <p><a href="{{ $metadata->facebook }}" target="_blank">{{ $metadata->facebook }}</a></p>
                                </div>
                                @endif

                                @if($metadata && isset($metadata->reddit))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Reddit')</h5>
                                    <p><a href="{{ $metadata->reddit }}" target="_blank">{{ $metadata->reddit }}</a></p>
                                </div>
                                @endif

                                @if($metadata && isset($metadata->linktree))
                                <div class="form-group">
                                    <h5 class="mb-2">@lang('Linktree')</h5>
                                    <p><a href="{{ $metadata->linktree }}" target="_blank">{{ $metadata->linktree }}</a></p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- VERIFY MODAL --}}
<div id="verifyModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Verify Token')</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="" method="POST">
                @csrf
                <div class="modal-body">
                    <p>@lang('Are you sure to verify') <span class="font-weight-bold token-name"></span>?</p>
                    <p>@lang('This token will be displayed on the homepage as a promoted token.')</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-dismiss="modal">@lang('No')</button>
                    <button type="submit" class="btn btn--success">@lang('Yes')</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- REJECT MODAL --}}
<div id="rejectModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Reject Token')</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="" method="POST">
                @csrf
                <div class="modal-body">
                    <p>@lang('Are you sure to reject') <span class="font-weight-bold token-name"></span>?</p>
                    <p>@lang('This token will be removed from the system.')</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-dismiss="modal">@lang('No')</button>
                    <button type="submit" class="btn btn--danger">@lang('Yes')</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    (function($) {
        "use strict";

        $('.verifyBtn').on('click', function() {
            var modal = $('#verifyModal');
            modal.find('.token-name').text($(this).data('token'));
            modal.find('form').attr('action', `{{ route('admin.tokens.verify', '') }}/${$(this).data('id')}`);
            modal.modal('show');
        });

        $('.rejectBtn').on('click', function() {
            var modal = $('#rejectModal');
            modal.find('.token-name').text($(this).data('token'));
            modal.find('form').attr('action', `{{ route('admin.tokens.reject', '') }}/${$(this).data('id')}`);
            modal.modal('show');
        });

    })(jQuery);
</script>
@endpush