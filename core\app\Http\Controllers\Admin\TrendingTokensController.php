<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DexscreenerToken;
use App\Models\TokenVote;
use App\Models\TokenPromotion;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class TrendingTokensController extends Controller
{
    public function index(Request $request)
    {
        $pageTitle = 'Trending Tokens';
        $search = $request->search;

        // Get all tokens with base query
        $baseQuery = DexscreenerToken::where(function($query) {
                $query->where('is_verified', true)
                      ->orWhereNull('submitted_by_user_id'); // Admin added tokens have null user_id
            });

        // Apply search filter if provided
        if ($search) {
            $baseQuery->where(function($query) use ($search) {
                $query->where('token_name', 'like', "%{$search}%")
                      ->orWhere('token_symbol', 'like', "%{$search}%")
                      ->orWhere('token_address', 'like', "%{$search}%");
            });
        }

        // Get all tokens for processing
        $allTokens = $baseQuery->get();

        // Compute votes for each token
        foreach ($allTokens as $token) {
            // Get regular user votes
            $token->regular_votes = TokenVote::getVoteCountByAddress($token->chain_id, $token->token_address);

            // Get negative votes
            $token->negative_votes = TokenVote::getNegativeVoteCountByAddress($token->chain_id, $token->token_address);

            // Get admin trend votes
            $token->admin_trend_votes = $token->trend_votes ?? 0;

            // Calculate total votes (no deduction for negative votes)
            $token->vote_count = $token->regular_votes + $token->admin_trend_votes;
        }

        // Filter tokens to only include those with at least 1 vote
        $filteredTokens = $allTokens->filter(function($token) {
            return $token->vote_count > 0;
        });

        // Sort by total votes (highest first)
        $sortedTokens = $filteredTokens->sortByDesc('vote_count');

        // Add global rank to each token
        $sortedTokens = $sortedTokens->values(); // Re-index the collection
        $rank = 1;
        foreach ($sortedTokens as $token) {
            $token->global_rank = $rank++;
        }

        // Paginate the collection manually
        $page = $request->input('page', 1);
        $perPage = 10;
        $offset = ($page - 1) * $perPage;

        // Get the paginated items
        $items = $sortedTokens->slice($offset, $perPage)->all();

        // Create a new paginator instance
        $tokens = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $sortedTokens->count(),
            $perPage,
            $page,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        return view('admin.tokens.trending', compact('pageTitle', 'tokens', 'search'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'token_id' => 'required|exists:dexscreener_tokens,id',
            'trend_votes' => 'required|integer|min:0',
        ]);

        $token = DexscreenerToken::findOrFail($request->token_id);
        $token->trend_votes = $request->trend_votes;
        $token->save();

        $notify[] = ['success', 'Token trend votes updated successfully'];
        return back()->withNotify($notify);
    }

    /**
     * Promote a token from admin panel
     */
    public function promote(Request $request)
    {
        $request->validate([
            'token_id' => 'required|exists:dexscreener_tokens,id',
            'days' => 'required|integer|min:1|max:100',
        ]);

        $token = DexscreenerToken::findOrFail($request->token_id);

        // Check if token is already being promoted
        $existingPromotion = TokenPromotion::where('chain_id', $token->chain_id)
            ->where('token_address', $token->token_address)
            ->where('is_active', true)
            ->where('end_date', '>', now())
            ->first();

        if ($existingPromotion) {
            // Extend the existing promotion
            $days = (int) $request->days;
            $existingPromotion->days += $days;
            $existingPromotion->end_date = Carbon::parse($existingPromotion->end_date)->addDays($days);
            $existingPromotion->save();
            $promotion = $existingPromotion;

            $notify[] = ['success', 'Token promotion extended successfully for ' . $days . ' additional days.'];
        } else {
            // Create a new promotion
            $days = (int) $request->days;
            $promotion = new TokenPromotion();
            $promotion->user_id = 1; // Admin user ID (usually 1)
            $promotion->chain_id = $token->chain_id;
            $promotion->token_address = $token->token_address;
            $promotion->days = $days;
            $promotion->start_date = now();
            $promotion->end_date = now()->addDays($days);
            $promotion->is_active = true;
            $promotion->save();

            $notify[] = ['success', 'Token has been promoted successfully for ' . $days . ' days.'];
        }

        return back()->withNotify($notify);
    }
}
