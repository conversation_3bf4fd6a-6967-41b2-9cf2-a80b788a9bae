<?php

namespace App\Http\Controllers;

use App\Constants\Status;
use App\Lib\CurlRequest;
use App\Models\CronJob;
use App\Models\CronJobLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CronController extends Controller
{
    public function updateCoinMarketCapData(Request $request)
    {
        try {

            if ($request->password !== config('app.cron_password')) {
                Log::info('==== Wrong password for cron ====');
                abort(403, 'Unauthorized access');
            }

            // Super detailed logging
            Log::info('==== CRYPTO DATA FETCH STARTED ====');
            Log::info('Request source: ' . (request()->path() ?? 'Unknown'));

            // Update last cron time
            $general = gs();
            $general->last_cron = now();
            $general->save();
            Log::info('General last_cron time updated');

            // Get crypto settings
            $setting = \App\Models\CryptoSetting::first();

            if (!$setting || !$setting->api_key) {
                Log::error('Crypto cron job failed: API key not configured');
                return false;
            }

            Log::info('Using API key: ' . substr($setting->api_key, 0, 5) . '...' . substr($setting->api_key, -5));
            Log::info('Display count: ' . $setting->display_count);

            // Make API request
            try {
                Log::info('Sending request to CoinMarketCap API...');

                $response = \Illuminate\Support\Facades\Http::withHeaders([
                    'X-CMC_PRO_API_KEY' => $setting->api_key,
                    'Accept' => 'application/json'
                ])->get('https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest', [
                    'limit' => $setting->display_count,
                    'convert' => 'USD'
                ]);

                Log::info('API request completed with status: ' . $response->status());

                if ($response->successful()) {
                    $data = $response->json();
                    Log::info('Successfully parsed JSON response');
                    Log::info('Data count: ' . count($data['data']));

                    // Clear existing data
                    \App\Models\CryptoCurrency::truncate();
                    Log::info('Existing crypto data cleared');

                    // Insert new data
                    foreach ($data['data'] as $crypto) {
                        $record = new \App\Models\CryptoCurrency();
                        $record->name = $crypto['name'];
                        $record->symbol = $crypto['symbol'];
                        $record->price = $crypto['quote']['USD']['price'];
                        $record->percent_change_24h = $crypto['quote']['USD']['percent_change_24h'];
                        $record->market_cap = $crypto['quote']['USD']['market_cap'];
                        $record->volume_24h = $crypto['quote']['USD']['volume_24h'];
                        $record->last_updated = now();
                        $record->save();

                        Log::info('Added crypto: ' . $crypto['name'] . ' (' . $crypto['symbol'] . ')');
                    }

                    // Update last updated time
                    $setting->last_updated = now();
                    $setting->save();
                    return true;
                } else {
                    $errorMsg = 'Failed to fetch crypto data: ' . ($response->json()['status']['error_message'] ?? 'Unknown error');
                    Log::error($errorMsg);
                    Log::error('Response body: ' . $response->body());
                    return false;
                }
            } catch (\Exception $e) {
                Log::error('API Error in fetchCryptoData: ' . $e->getMessage());
                Log::error($e->getTraceAsString());
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Unexpected error in fetchCryptoData: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }

        return true;
    }

    public function updatePresaleTokensData(Request $request)
    {

        try {

            if ($request->password !== config('app.cron_password')) {
                Log::info('==== Wrong password for cron ====');
                abort(403, 'Unauthorized access');
            }

            Log::info('==== DEXSCREENER DATA FETCH STARTING ====');
            try {
                // Clear all token-related caches
                \Illuminate\Support\Facades\Cache::forget('dexscreener_tokens');
                \Illuminate\Support\Facades\Cache::forget('admin_tokens');
                \Illuminate\Support\Facades\Cache::forget('admin_tokens_active');
                \Illuminate\Support\Facades\Cache::forget('admin_tokens_all');

                // Get the DexscreenerService
                $dexscreenerService = app(\App\Services\DexscreenerService::class);

                // Check for presale tokens with real contract addresses that might be live now
                Log::info('Checking for presale tokens with real contract addresses that might be live now...');

                // Use the DexscreenerService method to update presale tokens
                $updatedCount = $dexscreenerService->updatePresaleTokensWithPriceData();

                Log::info('Updated ' . $updatedCount . ' presale tokens with live data');

            } catch (\Exception $dexError) {
                Log::error('Dexscreener API Error: ' . $dexError->getMessage());
                Log::error($dexError->getTraceAsString());
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Unexpected error in updatePresaleTokensData: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
        
    }

    public function updatePopularTokensData(Request $request)
    {

        try {

            if ($request->password !== config('app.cron_password')) {
                Log::info('==== Wrong password for cron ====');
                abort(403, 'Unauthorized access');
            }

            // Clear all token-related caches
            \Illuminate\Support\Facades\Cache::forget('dexscreener_tokens');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens_active');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens_all');

            // Get the DexscreenerService
            $dexscreenerService = app(\App\Services\DexscreenerService::class);

            // Now run the regular token refresh
            Log::info('==== DEXSCREENER DATA FETCH STARTING ====');
            $dexResult = $dexscreenerService->refreshPopularTokens();
            Log::info('==== DEXSCREENER DATA FETCH COMPLETED SUCCESSFULLY ====');

            if ($dexResult) {
                Log::info('==== DEXSCREENER DATA FETCH COMPLETED SUCCESSFULLY ====');
                return true;
            } else {
                Log::error('==== DEXSCREENER DATA FETCH FAILED ====');
                return false;
            }
        } catch (\Exception $dexError) {
            Log::error('Dexscreener API Error: ' . $dexError->getMessage());
            Log::error($dexError->getTraceAsString());
            return false;
        }

        return true;
        
    }


    public function updateUnpopularTokensData(Request $request)
    {

        if ($request->password !== config('app.cron_password')) {
            Log::info('==== Wrong password for cron ====');
            abort(403, 'Unauthorized access');
        }

        try {
            // Clear all token-related caches
            \Illuminate\Support\Facades\Cache::forget('dexscreener_tokens');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens_active');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens_all');

            // Get the DexscreenerService
            $dexscreenerService = app(\App\Services\DexscreenerService::class);

            // Now run the regular token refresh
            Log::info('==== DEXSCREENER DATA FETCH STARTING ====');
            $dexResult = $dexscreenerService->refreshUnpopularTokens();
            Log::info('==== DEXSCREENER DATA FETCH COMPLETED SUCCESSFULLY ====');

            if ($dexResult) {
                Log::info('==== DEXSCREENER DATA FETCH COMPLETED SUCCESSFULLY ====');
                return true;
            } else {
                Log::error('==== DEXSCREENER DATA FETCH FAILED ====');
                return false;
            }
        } catch (\Exception $dexError) {
            Log::error('Dexscreener API Error: ' . $dexError->getMessage());
            Log::error($dexError->getTraceAsString());
            return false;
        }

        return true;
        
    }
}
