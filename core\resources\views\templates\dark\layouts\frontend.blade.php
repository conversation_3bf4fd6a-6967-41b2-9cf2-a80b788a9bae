@extends($activeTemplate . 'layouts.app')
@section('panel')
    @if(gs('notification_status'))
        <div class="sitewide-notification {{ gs('notification_url') ? 'has-link' : '' }}">
            <div class="container">
                <div class="notification-content">
                    <span>📢 {{ gs('notification_message') }}</span>
                    @if(gs('notification_url'))
                        <a href="{{ gs('notification_url') }}" target="_blank" class="notification-btn">{{ gs('notification_button_text') ?? 'Learn More' }}</a>
                    @endif
                </div>
            </div>
        </div>
    @endif
    @include($activeTemplate . 'partials.crypto_ticker')
    @include($activeTemplate . 'partials.header')

    @if(request()->routeIs('home'))
        <!-- Search Bar -->
        <div class="search-container py-3">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <form action="{{ route('search') }}" method="GET" class="search-form">
                            <div class="position-relative">
                                <input type="text" name="query" class="form-control search-input" placeholder="search by name, address, blockchain, presale, fair launch, newest..." required>
                                <button type="submit" class="btn search-btn">
                                    <i class="las la-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @include($activeTemplate . 'sections.trending_articles')
    @else
        <!-- Global Banner (only on non-home pages) -->
        <div class="global-banner-container">
            <div class="container">
                <x-ad position="global_banner" />
            </div>
        </div>
    @endif

    @yield('content')

    @include($activeTemplate . 'partials.footer')

    @php
        $cookie = App\Models\Frontend::where('data_keys', 'cookie.data')->first();
    @endphp
    @if ($cookie->data_values->status == Status::ENABLE && !\Cookie::get('gdpr_cookie'))
        <div class="cookies-card hide text-center">
            <div class="cookies-card__icon bg--base">
                <i class="las la-cookie-bite"></i>
            </div>
            <p class="mt-4">{{ __($cookie->data_values->short_desc) }} <a class="text--base" href="{{ route('cookie.policy') }}" target="_blank">@lang('learn more')</a></p>
            <div class="cookies-card__btn mt-4">
                <a class="btn btn--base w-100 policy" href="javascript:void(0)">@lang('Allow')</a>
            </div>
        </div>
    @endif
@endsection

@push('style')
<style>
    .header-top {
        padding: 5px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .header-top-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .header-top-left {
        width: 100%;
    }
    .header-top-left-cont {
        display: flex;
        align-items: center;
    }
    .header-top-left-cont span {
        margin-right: 20px;
        color: #fff;
        font-size: 14px;
    }
    .header-top-left-cont span a {
        color: #fff;
        margin-left: 5px;
    }
    .header-top-left-cont span small {
        margin-left: 5px;
    }
    .header-top-left-cont span small.text-success {
        color: #28a745 !important;
    }
    .header-top-left-cont span small.text-danger {
        color: #dc3545 !important;
    }

    /* Search Bar Styles */
    .search-container {
        background-color: var(--section-bg);
    }
    .search-form {
        width: 100%;
    }
    .search-input {
        height: 50px;
        border-radius: 25px;
        border: 2px solid #BE8400;
        background-color: rgba(0, 0, 0, 0.2);
        color: #fff;
        padding-left: 20px;
        font-size: 16px;
    }
    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }
    .search-input:focus {
        box-shadow: 0 0 0 0.25rem rgba(190, 132, 0, 0.25) !important;
        border-color: #BE8400 !important;
        background-color: rgba(0, 0, 0, 0.3);
        color: #fff;
    }
    .search-btn {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        border-radius: 25px;
        background-color: #BE8400;
        border: none;
        color: #fff;
        width: 60px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        z-index: 10;
    }
    .search-btn:hover {
        background-color: #a37200;
        border-color: #a37200;
        color: #fff;
    }

    /* Global Banner Styles */
    .global-banner-container {
        background-color: var(--section-bg);
        padding: 15px 0;
    }

    /* Sitewide Notification Styles */
    .sitewide-notification {
        background-color: #BE8400;
        color: #fff;
        padding: 10px 0;
        text-align: center;
        font-weight: 500;
    }
    .sitewide-notification .notification-content {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
    }
    .sitewide-notification a {
        color: #fff;
        text-decoration: none;
    }
    .sitewide-notification.has-link a:hover {
        text-decoration: none;
    }
    .notification-btn {
        background-color: #1C2631;
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.3s;
    }
    .notification-btn:hover {
        background-color: #2A3642;
    }

    /* Responsive styles for sitewide notification */
    @media (min-width: 768px) and (max-width: 1024px) {
        .sitewide-notification .notification-content {
            gap: 10px;
        }
        .notification-btn {
            padding: 5px 15px;
            font-size: 13px;
            min-width: 110px;
            text-align: center;
        }
    }

    @media (max-width: 767px) {
        .sitewide-notification .notification-content {
            flex-direction: column;
            gap: 10px;
            padding: 5px 0;
        }
        .notification-btn {
            padding: 5px 15px;
            font-size: 13px;
            display: inline-block;
            margin-bottom: 5px;
            min-width: 120px;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .sitewide-notification {
            padding: 8px 0;
        }
        .notification-btn {
            padding: 4px 12px;
            font-size: 12px;
            width: auto;
            min-width: 100px;
        }
    }

    /* Specific tablet device styles */
    @media (width: 768px) and (height: 1024px),
           (width: 820px) and (height: 1180px),
           (width: 912px) and (height: 1368px),
           (width: 853px) and (height: 1280px),
           (width: 1024px) and (height: 1366px) {
        .notification-btn {
            padding: 6px 16px;
            font-size: 14px;
            min-width: 120px;
            text-align: center;
        }
    }
</style>
@endpush

@push('script')
    <script>
        (function($) {
            $('.policy').on('click', function() {
                $.get('{{ route('cookie.accept') }}', function(response) {
                    $('.cookies-card').addClass('d-none');
                });
            });
            setTimeout(function() {
                $('.cookies-card').removeClass('hide')
            }, 2000);
        })(jQuery)
    </script>
@endpush
