<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            $table->string('price_change_5m')->nullable()->after('price_change_24h');
            $table->string('price_change_1h')->nullable()->after('price_change_5m');
            $table->string('price_change_6h')->nullable()->after('price_change_1h');
            $table->integer('txn_24h')->nullable()->after('price_change_6h');
            $table->integer('token_age')->nullable()->after('txn_24h');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            $table->dropColumn([
                'price_change_5m',
                'price_change_1h',
                'price_change_6h',
                'txn_24h',
                'token_age'
            ]);
        });
    }
};
