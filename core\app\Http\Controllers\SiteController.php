<?php

namespace App\Http\Controllers;

use App\Constants\Status;
use App\Models\AdminNotification;
use App\Models\CclToken;
use App\Models\Frontend;
use App\Models\Language;
use App\Models\Page;
use App\Models\Subscriber;
use App\Models\SupportMessage;
use App\Models\SupportTicket;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Validator;

class SiteController extends Controller
{
    public function index()
    {
        $reference = @$_GET['reference'];
        if ($reference) {
            session()->put('reference', $reference);
        }

        $pageTitle = 'Home';
        $sections = Page::where('tempname', activeTemplate())->where('slug', '/')->first();
        $seoContents = $sections->seo_content;
        $seoImage = @$seoContents->image ? getImage(getFilePath('seo') . '/' . @$seoContents->image, getFileSize('seo')) : null;

        return view('Template::home', compact('pageTitle', 'sections', 'seoContents', 'seoImage'));
    }

    public function pages($slug)
    {
        $page = Page::where('tempname', activeTemplate())->where('slug', $slug)->firstOrFail();
        $pageTitle = $page->name;
        $sections = $page->secs;
        $seoContents = $page->seo_content;
        $seoImage = @$seoContents->image ? getImage(getFilePath('seo') . '/' . @$seoContents->image, getFileSize('seo')) : null;
        return view('Template::pages', compact('pageTitle', 'sections', 'seoContents', 'seoImage'));
    }


    public function contact()
    {
        // If user is not logged in, redirect to home
        if (!auth()->check()) {
            return redirect()->route('home');
        }

        $pageTitle = "Create Ticket";
        $user = auth()->user();
        $sections = Page::where('tempname', activeTemplate())->where('slug', 'contact')->first();
        $seoContents = $sections->seo_content;
        $seoImage = @$seoContents->image ? getImage(getFilePath('seo') . '/' . @$seoContents->image, getFileSize('seo')) : null;
        return view('Template::contact', compact('pageTitle', 'user', 'sections', 'seoContents', 'seoImage'));
    }

    public function contact2()
    {
        // If user is logged in, redirect to home
        if (auth()->check()) {
            return redirect()->route('home');
        }

        $pageTitle = "Contact";
        $user = auth()->user();
        $sections = Page::where('tempname', activeTemplate())->where('slug', 'contact')->first();
        $seoContents = $sections->seo_content;
        $seoImage = @$seoContents->image ? getImage(getFilePath('seo') . '/' . @$seoContents->image, getFileSize('seo')) : null;
        return view('Template::contact2', compact('pageTitle', 'user', 'sections', 'seoContents', 'seoImage'));
    }

    public function contactSubmit(Request $request)
    {
        // If user is not logged in, redirect to home
        if (!auth()->check()) {
            return redirect()->route('home');
        }

        $request->validate([
            'name' => 'required',
            'email' => 'required',
            'subject' => 'required|string|max:255',
            'message' => 'required',
        ]);

        $request->session()->regenerateToken();

        if (!verifyCaptcha()) {
            $notify[] = ['error', 'Invalid captcha provided'];
            return back()->withNotify($notify);
        }

        $random = getNumber();

        $ticket = new SupportTicket();
        $ticket->user_id = auth()->id() ?? 0;
        $ticket->name = $request->name;
        $ticket->email = $request->email;
        $ticket->priority = Status::PRIORITY_MEDIUM;


        $ticket->ticket = $random;
        $ticket->subject = $request->subject;
        $ticket->last_reply = Carbon::now();
        $ticket->status = Status::TICKET_OPEN;
        $ticket->save();

        $adminNotification = new AdminNotification();
        $adminNotification->user_id = auth()->user() ? auth()->user()->id : 0;
        $adminNotification->title = 'A new contact message has been submitted';
        $adminNotification->click_url = urlPath('admin.ticket.view', $ticket->id);
        $adminNotification->save();

        $message = new SupportMessage();
        $message->support_ticket_id = $ticket->id;
        $message->message = $request->message;
        $message->save();

        $notify[] = ['success', 'Ticket created successfully!'];

        return to_route('ticket.view', [$ticket->ticket])->withNotify($notify);
    }

    public function contactSubmit2(Request $request)
    {
        // If user is logged in, redirect to home
        if (auth()->check()) {
            return redirect()->route('home');
        }

        $request->validate([
            'name' => 'required',
            'email' => 'required|email',
            'subject' => 'required|string|max:255',
            'message' => 'required',
        ]);

        $request->session()->regenerateToken();

        if (!verifyCaptcha()) {
            $notify[] = ['error', 'Invalid captcha provided'];
            return back()->withNotify($notify);
        }

        // Create admin notification
        $adminNotification = new AdminNotification();
        $adminNotification->user_id = auth()->user() ? auth()->user()->id : 0;
        $adminNotification->title = 'New message from contact form';
        $adminNotification->click_url = '#';
        $adminNotification->save();

        // Get general settings
        $general = gs();

        // Format message with HTML
        $messageBody = '<strong>Name:</strong> ' . $request->name . '<br>';
        $messageBody .= '<strong>Email:</strong> ' . $request->email . '<br>';
        $messageBody .= '<strong>Subject:</strong> ' . $request->subject . '<br>';
        $messageBody .= '<strong>Message:</strong><br>' . nl2br($request->message);

        // Create a properly formatted admin recipient
        $adminReceiver = (object)[
            'username' => 'Admin',
            'email' => $general->email_from,
            'fullname' => $general->site_name . ' Admin'
        ];

        // Send email to admin using DEFAULT template which is guaranteed to exist
        notify($adminReceiver, 'DEFAULT', [
            'subject' => 'Contact Form: ' . $request->subject,
            'message' => $messageBody
        ], ['email']);

        // Send confirmation to user (optional)
        if ($request->email) {
            $userReceiver = (object)[
                'username' => $request->name,
                'email' => $request->email,
                'fullname' => $request->name
            ];

            // Confirmation message to user
            $userMessage = 'Thank you for contacting us. We have received your message and will respond to you shortly.<br><br>';
            $userMessage .= '<strong>Your message details:</strong><br>';
            $userMessage .= '<strong>Subject:</strong> ' . $request->subject . '<br>';
            $userMessage .= '<strong>Message:</strong><br>' . nl2br($request->message);

            // Send using DEFAULT template
            notify($userReceiver, 'DEFAULT', [
                'subject' => 'Your message has been received - ' . $general->site_name,
                'message' => $userMessage
            ], ['email']);
        }

        $notify[] = ['success', 'Your message has been sent successfully!'];
        return back()->withNotify($notify);
    }

    public function policyPages($slug)
    {
        $policy = Frontend::activeTemplate()->where('slug', $slug)->where('data_keys', 'policy_pages.element')->firstOrFail();
        $pageTitle = $policy->data_values->title;
        $seoContents = $policy->seo_content;
        $seoImage = @$seoContents->image ? frontendImage('policy_pages', $seoContents->image, getFileSize('seo'), true) : null;
        return view('Template::policy', compact('policy', 'pageTitle', 'seoContents', 'seoImage'));
    }

    public function changeLanguage($lang = null)
    {
        $language = Language::where('code', $lang)->first();
        if (!$language) $lang = 'en';
        session()->put('lang', $lang);
        return back();
    }

    public function plans()
    {
        $pageTitle = 'Mining Plans';
        $sections  = Page::activeTemplate()->where('slug', 'plans')->first();
        return view('Template::plans', compact('pageTitle', 'sections'));
    }

    public function blogs()
    {
        $pageTitle = 'Articles';
        $sections  = Page::activeTemplate()->where('slug', 'blogs')->first();
        $blogs     = Frontend::activeTemplate()->where('data_keys', 'blog.element')->orderBy('id', 'desc')->paginate(9);
        return view('Template::blogs', compact('pageTitle', 'sections', 'blogs'));
    }

    public function blogDetails($slug)
    {
        $blog         = Frontend::activeTemplate()->where('slug', $slug)->where('data_keys', 'blog.element')->firstOrFail();
        $pageTitle    = 'Article Details';
        $seoContents  = $blog->seo_content;
        $seoImage     = @$seoContents->image ? frontendImage('blog', $seoContents->image, getFileSize('seo'), true) : null;
        $latestBlogs  = Frontend::activeTemplate()->where('slug', '!=', $slug)->where('data_keys', 'blog.element')->take(5)->get();
        return view('Template::blog_details', compact('blog', 'pageTitle', 'seoContents', 'seoImage', 'latestBlogs'));
    }

    public function cclToken()
    {
        $cclToken = CclToken::getData();

        // If CCL Token is disabled, redirect to home
        if (!$cclToken->is_enabled) {
            return redirect()->route('home');
        }

        // Get active presales
        $presales = \App\Models\Presale::where('is_active', true)
            ->orderBy('position', 'asc')
            ->get();

        // Sort presales to display 'Active' status first, 'Upcoming' second, then others
        $activePresales = collect();
        $upcomingPresales = collect();
        $otherPresales = collect();

        foreach ($presales as $presale) {
            if ($presale->getStatus() == 'Active') {
                $activePresales->push($presale);
            } elseif ($presale->getStatus() == 'Upcoming') {
                $upcomingPresales->push($presale);
            } else {
                $otherPresales->push($presale);
            }
        }

        // Combine the collections with active presales first, upcoming second, then others
        $sortedPresales = $activePresales->merge($upcomingPresales)->merge($otherPresales);
        $presales = $sortedPresales;

        $pageTitle = 'Launchpad';
        return view('Template::launchpad', compact('pageTitle', 'cclToken', 'presales'));
    }


    public function cookieAccept()
    {
        Cookie::queue('gdpr_cookie', gs('site_name'), 43200);
    }

    public function cookiePolicy()
    {
        $cookie = Frontend::where('data_keys', 'cookie.data')->first();
        abort_if($cookie->data_values->status != Status::ENABLE, 404);
        $pageTitle = 'Cookie Policy';
        return view('Template::cookie', compact('pageTitle', 'cookie'));
    }

    public function placeholderImage($size = null)
    {
        $imgWidth = explode('x', $size)[0];
        $imgHeight = explode('x', $size)[1];
        $text = $imgWidth . '×' . $imgHeight;
        $fontFile = realpath('assets/font/solaimanLipi_bold.ttf');
        $fontSize = round(($imgWidth - 50) / 8);
        if ($fontSize <= 9) {
            $fontSize = 9;
        }
        if ($imgHeight < 100 && $fontSize > 30) {
            $fontSize = 30;
        }

        $image     = imagecreatetruecolor($imgWidth, $imgHeight);
        $colorFill = imagecolorallocate($image, 100, 100, 100);
        $bgFill    = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $bgFill);
        $textBox = imagettfbbox($fontSize, 0, $fontFile, $text);
        $textWidth  = abs($textBox[4] - $textBox[0]);
        $textHeight = abs($textBox[5] - $textBox[1]);
        $textX      = ($imgWidth - $textWidth) / 2;
        $textY      = ($imgHeight + $textHeight) / 2;
        header('Content-Type: image/jpeg');
        imagettftext($image, $fontSize, 0, $textX, $textY, $colorFill, $fontFile, $text);
        imagejpeg($image);
        imagedestroy($image);
    }

    public function maintenance()
    {
        $pageTitle = 'Maintenance Mode';
        if (gs('maintenance_mode') == Status::DISABLE) {
            return to_route('home');
        }
        $maintenance = Frontend::where('data_keys', 'maintenance.data')->first();
        return view('Template::maintenance', compact('pageTitle', 'maintenance'));
    }

    public function addSubscriber(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid Email']);
        }

        $exist = Subscriber::where('email', $request->email)->first();
        if (!$exist) {
            $subscribe        = new Subscriber();
            $subscribe->email = $request->email;
            $subscribe->save();

            return response()->json(['success' => 'Subscribed successfully']);
        } else {
            return response()->json(['error' => 'Already subscribed']);
        }
    }
}
