@extends('Template::layouts.master')

@push('style')
<link rel="shortcut icon" href="{{ siteFavicon() }}" type="image/x-icon">
@endpush

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card custom--card" style="background-color: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.15); border-radius: 5px;">
                <div class="card-header" style="background-color: #BE8400; color: #ffffff; border-radius: 5px 5px 0 0;">
                    <h5 class="card-title mb-0">@lang('My Watchlist')</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover custom--table">
                            <thead>
                                <tr>
                                    <th>@lang('Token')</th>
                                    <th>@lang('Chain')</th>
                                    <th>@lang('Price')</th>
                                    <th>@lang('24h Change')</th>
                                    <th>@lang('Added Date')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($tokens as $token)
                                    <tr>
                                        <td data-label="@lang('Token')">
                                            <div class="d-flex align-items-center">
                                                <img src="@if(isset($token['image_url']) && !filter_var($token['image_url'], FILTER_VALIDATE_URL) && strpos($token['image_url'], 'http') !== 0){{ asset('assets/images/coin_logos/'.$token['image_url']) }}@else{{ $token['image_url'] ?? asset('assets/images/default.png') }}@endif" alt="{{ $token['token_symbol'] }}" class="coin-icon me-2" width="32" height="32">
                                                <div class="token-info-container">
                                                    <div class="fw-bold text-white">{{ $token['token_symbol'] }}</div>
                                                    <div class="text-muted small">{{ Str::limit($token['token_name'], 20) }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td data-label="@lang('Chain')">
                                            <span class="badge chain-badge">{{ ucfirst($token['chain_id']) }}</span>
                                        </td>
                                        <td data-label="@lang('Price')">
                                            ${{ is_numeric($token['price_usd']) ? showAmount($token['price_usd']) : '0.00' }}
                                        </td>
                                        <td data-label="@lang('24h Change')">
                                            @php
                                                $priceChange24h = $token['price_change_24h'];
                                                $change24hClass = floatval($priceChange24h) >= 0 ? 'text-success' : 'text-danger';
                                            @endphp
                                            <span class="{{ $change24hClass }}">
                                                {{ is_numeric($priceChange24h) ? showAmount($priceChange24h) : '0.00' }}%
                                            </span>
                                        </td>
                                        <td data-label="@lang('Added Date')">{{ showDateTime($token['added_at']) }}</td>
                                        <td data-label="@lang('Action')">
                                            <div class="d-flex flex-wrap gap-1">
                                                <a href="{{ $token['token_details_url'] }}" class="btn btn-sm btn-primary">
                                                    <i class="las la-eye"></i> @lang('View')
                                                </a>
                                                <button class="btn btn-sm btn-danger remove-watchlist"
                                                    data-chain-id="{{ $token['chain_id'] }}"
                                                    data-token-address="{{ $token['token_address'] }}"
                                                    data-token-name="{{ $token['token_name'] }}">
                                                    <i class="las la-trash"></i> @lang('Remove')
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">@lang('No tokens in your watchlist')</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    @if($tokens->hasPages())
                    <div class="mt-4 pagination-container">
                        {{ paginateLinks($tokens) }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

<!-- Remove Token Modal -->
<div class="modal custom--modal fade" id="removeWatchlistModal" tabindex="-1" aria-labelledby="removeWatchlistModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff; border-bottom-color: #2d3748;">
                <h5 class="modal-title text-white" id="removeWatchlistModalLabel">@lang('Remove from Watchlist')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-white">@lang('Are you sure you want to remove') <span class="fw-bold token-name-display"></span> @lang('from your watchlist?')</p>
            </div>
            <div class="modal-footer" style="border-top-color: #2d3748;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                <button type="button" class="btn btn-danger confirm-remove">@lang('Remove')</button>
            </div>
        </div>
    </div>
</div>

@push('script')
<script>
    $(document).ready(function() {
        // Variables to store current token data
        var currentChainId, currentTokenAddress, currentButton;

        // Handle remove from watchlist button click
        $('.remove-watchlist').on('click', function() {
            // Store the current token data
            currentChainId = $(this).data('chain-id');
            currentTokenAddress = $(this).data('token-address');
            currentButton = $(this);

            // Set the token name in the modal
            var tokenName = $(this).data('token-name');
            $('#removeWatchlistModal').find('.token-name-display').text(tokenName);

            // Show the modal
            $('#removeWatchlistModal').modal('show');
        });

        // Handle confirm remove button click
        $('.confirm-remove').on('click', function() {
            $.ajax({
                url: '{{ route("token.watchlist.toggle") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    chain_id: currentChainId,
                    token_address: currentTokenAddress,
                    remove: 1 // Explicitly request removal
                },
                success: function(response) {
                    if (response.success) {
                        // Hide the modal
                        $('#removeWatchlistModal').modal('hide');

                        // Remove the row from the table
                        currentButton.closest('tr').remove();

                        // If no more tokens, show empty message
                        if ($('tbody tr').length === 0) {
                            $('tbody').html('<tr><td colspan="6" class="text-center">@lang("No tokens in your watchlist")</td></tr>');
                        }

                        notify('success', 'Token removed from watchlist');
                    }
                },
                error: function() {
                    // Hide the modal
                    $('#removeWatchlistModal').modal('hide');
                    notify('error', 'Something went wrong');
                }
            });
        });
    });
</script>
@endpush

@push('style')
<style>
    .coin-icon {
        border-radius: 50%;
        object-fit: cover;
    }
    .chain-badge {
        background-color: #BE8400;
        color: #ffffff;
        font-weight: 500;
    }
    /* Token name and symbol alignment */
    .token-info-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    /* Table styling */
    .table {
        color: #fff;
    }

    .table thead th {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-weight: 500;
    }

    .table tbody td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        vertical-align: middle;
    }

    /* Button spacing */
    .gap-1 {
        gap: 0.25rem !important;
    }

    /* Base table responsive styles */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        width: 100%;
    }

    .custom--table {
        min-width: 800px;
        width: 100%;
    }

    /* Modal styling */
    .modal-content.bg-dark {
        background-color: #1e2530 !important;
    }

    .modal-content.bg-dark .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);
    }

    /* Pagination responsive styles */
    /* Hide unnecessary elements */
    .pagination-wrapper .d-flex.justify-content-between.flex-fill,
    .pagination-container .d-flex.justify-content-between.flex-fill {
        display: none !important;
    }

    /* Main container for pagination */
    .pagination-wrapper,
    .pagination-container {
        width: 100%;
        overflow-x: auto;
        padding: 5px 0;
        margin: 10px 0;
        scrollbar-width: thin;
    }

    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        display: block !important;
        width: 100%;
    }

    /* Center the pagination buttons */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-wrapper nav,
    .pagination-wrapper .pagination,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-container nav,
    .pagination-container .pagination,
    .pagination-container nav > ul {
        display: flex;
        justify-content: center !important;
        width: 100%;
    }

    /* Improved pagination styling for all devices */
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
        margin: 0;
        padding: 0;
        justify-content: center;
        max-width: 100%;
    }

    /* Handle large number of pagination items - general fallback */
    .pagination.pagination-sm {
        justify-content: center;
        padding: 5px 0;
    }

    /* Ensure consistent spacing between pagination items */
    .pagination .page-item {
        margin: 3px;
    }

    /* Style for pagination links */
    .pagination .page-item .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        padding: 0 10px;
        border-radius: 4px !important;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    /* Style for forward and back buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item.previous .page-link,
    .pagination .page-item.next .page-link {
        font-size: 14px;
        padding: 0 12px;
        min-width: 40px; /* Slightly wider for navigation buttons */
    }

    /* Ensure arrow icons are visible */
    .pagination .page-item .page-link span[aria-hidden="true"] {
        display: inline-block;
        line-height: 1;
    }

    /* Style for disabled pagination items */
    .pagination .page-item.disabled .page-link {
        background-color: hsl(0deg 0% 100% / 40%);
        opacity: 0.7;
    }

    /* Responsive styles for watchlist card */
    @media (max-width: 1024px) {
        .custom--card .card-body {
            padding: 20px;
        }

        /* Table responsive styles for all devices */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            position: relative;
            width: 100%;
            margin-bottom: 15px;
        }

        .custom--table {
            min-width: 800px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom--table thead th,
        .custom--table tbody td {
            padding: 10px 6px;
            font-size: 13px;
            white-space: nowrap;
        }

        .coin-icon {
            max-width: 28px;
            max-height: 28px;
        }

        /* Pagination responsive adjustments */
        .pagination-wrapper,
        .pagination-container {
            padding: 3px 0;
            margin: 8px 0;
        }

        .pagination {
            gap: 3px;
        }

        .pagination .page-item {
            margin: 2px;
        }

        .pagination .page-item .page-link {
            min-width: 32px;
            height: 32px;
            font-size: 13px;
        }

        /* Adjust forward/back buttons on small screens */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 36px;
            padding: 0 10px;
        }
    }

    /* Tablet specific styles */
    @media (min-width: 768px) and (max-width: 1024px) {
        .custom--card {
            width: 100%;
        }

        /* Ensure table is scrollable on tablets */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
        }

        .table-responsive table {
            min-width: 800px;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 12px;
        }

        .d-flex.flex-wrap.gap-1 {
            display: flex;
            flex-wrap: wrap;
            gap: 5px !important;
        }

        /* Token name and symbol alignment for tablets */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        /* Tablet-specific pagination adjustments */
        .pagination-wrapper,
        .pagination-container {
            padding: 8px 0;
            margin: 12px 0;
        }

        .pagination {
            gap: 6px;
        }

        .pagination .page-item {
            margin: 3px;
        }

        .pagination .page-item .page-link {
            min-width: 38px;
            height: 38px;
            font-size: 15px;
        }

        /* Adjust forward/back buttons on tablets */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 42px;
            padding: 0 12px;
            font-size: 15px;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .custom--card .card-header {
            padding: 15px;
        }

        .custom--card .card-body {
            padding: 15px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .coin-icon {
            max-width: 24px;
            max-height: 24px;
        }

        /* Improve button display on small screens */
        .d-flex.flex-wrap.gap-1 {
            display: flex;
            flex-direction: column;
            gap: 5px !important;
        }

        .d-flex.flex-wrap.gap-1 .btn {
            width: 100%;
            margin-bottom: 5px;
        }

        /* Token name and symbol alignment for mobile */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        /* Ensure horizontal scrolling for tables on mobile */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
            max-width: 100%;
            margin-bottom: 15px;
        }

        .table-responsive table {
            min-width: 800px;
            width: 100%;
        }

        .table-responsive table th,
        .table-responsive table td {
            white-space: nowrap;
            padding: 8px 10px;
            font-size: 12px;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .custom--card .card-header {
            padding: 12px;
        }

        .custom--card .card-body {
            padding: 12px;
        }

        .card-title {
            font-size: 16px;
        }

        .btn-sm {
            padding: 3px 6px;
            font-size: 11px;
        }

        /* Ensure table remains scrollable on extra small devices */
        .table-responsive {
            margin: 0 -12px;
            width: calc(100% + 24px);
            padding: 0;
        }

        .table-responsive table th,
        .table-responsive table td {
            padding: 6px 8px;
            font-size: 11px;
        }

        .coin-icon {
            max-width: 20px;
            max-height: 20px;
        }

        /* Token name and symbol alignment for extra small devices */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        .token-info-container .fw-bold {
            font-size: 12px;
        }

        .token-info-container .text-muted.small {
            font-size: 10px;
        }

        /* Very small screen pagination adjustments */
        .pagination .page-item .page-link {
            min-width: 30px;
            height: 30px;
            font-size: 12px;
            padding: 0 8px;
        }
    }
</style>
@endpush
