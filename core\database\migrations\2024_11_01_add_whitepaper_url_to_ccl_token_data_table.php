<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ccl_token_data', function (Blueprint $table) {
            if (!Schema::hasColumn('ccl_token_data', 'whitepaper_url')) {
                $table->string('whitepaper_url')->nullable()->after('buy_token_url');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ccl_token_data', function (Blueprint $table) {
            if (Schema::hasColumn('ccl_token_data', 'whitepaper_url')) {
                $table->dropColumn('whitepaper_url');
            }
        });
    }
};
