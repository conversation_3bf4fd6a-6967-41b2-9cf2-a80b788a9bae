@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form action="{{ route('admin.crypto.settings') }}" method="POST">
                    @csrf
                    <div class="card-body">
                        <div class="form-group">
                            <label>CoinMarketCap API Key</label>
                            <input type="text" class="form-control" name="api_key" value="{{ $setting->api_key }}" required>
                            <small class="text-muted">Get your API key from <a href="https://coinmarketcap.com/api/" target="_blank">CoinMarketCap Developer Portal</a></small>
                        </div>

                        <div class="form-group">
                            <label>Number of Tokens to Display</label>
                            <input type="number" class="form-control" name="display_count" value="{{ $setting->display_count ?? 5 }}" min="1" max="50" required>
                        </div>

                        <div class="form-group">
                            <label>Enable Crypto Ticker</label>
                            <input type="checkbox" data-width="100%" data-height="40px" data-onstyle="-success" data-offstyle="-danger" data-toggle="toggle" data-on="@lang('Enabled')" data-off="@lang('Disabled')" name="is_active" @if($setting->is_active) checked @endif>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn--primary w-100 h-45">@lang('Save Settings')</button>
                    </div>
                </form>
            </div>
        </div>
                    <div class="crypto-data-container">
                        <div class="table-responsive">
                            <table class="table table--light style--two crypto-table">
                                <thead>
                                    <tr>
                                        <th>@lang('Token')</th>
                                        <th>@lang('Symbol')</th>
                                        <th class="text-end">@lang('Price (USD)')</th>
                                        <th class="text-end">@lang('24h Change')</th>
                                        <th class="text-end">@lang('Market Cap')</th>
                                        <th class="text-end">@lang('24h Volume')</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($currencies as $currency)
                                        <tr>
                                            <td><span class="fw-bold">{{ $currency->name }}</span></td>
                                            <td><span class="badge bg-light text-dark">{{ $currency->symbol }}</span></td>
                                            <td class="text-end">${{ number_format($currency->price, 2) }}</td>
                                            <td class="text-end {{ $currency->percent_change_24h < 0 ? 'text--danger' : 'text--success' }}">
                                                {{ $currency->percent_change_24h > 0 ? '+' : '' }}{{ number_format($currency->percent_change_24h, 2) }}%
                                            </td>
                                            <td class="text-end">${{ number_format($currency->market_cap) }}</td>
                                            <td class="text-end">${{ number_format($currency->volume_24h) }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center">@lang('No crypto data found. Please fetch data from CoinMarketCap.')</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
<style>
    .toggle-group .toggle-on, .toggle-group .toggle-off {
        padding-top: 7px;
    }

    .crypto-data-container {
        margin-top: 15px;
    }

    .crypto-table th {
        font-weight: 600;
        background-color: #f7f7f7;
    }

    .crypto-table td {
        vertical-align: middle;
        padding: 12px 15px;
    }

    .crypto-table tr:hover {
        background-color: rgba(0,0,0,0.03);
    }

    .text--success {
        color: #28a745 !important;
        font-weight: 600;
    }

    .text--danger {
        color: #dc3545 !important;
        font-weight: 600;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .cron-info-section {
        border-bottom: 1px dashed rgba(0,0,0,0.1);
    }

    .method-card {
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .method-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .cron-methods-section {
        background-color: rgba(0,0,0,0.02) !important;
    }
</style>
@endpush

@push('script')
<script>
    function copyCommand() {
        var copyText = document.getElementById("cronCommand");
        copyText.select();
        copyText.setSelectionRange(0, 99999);
        document.execCommand("copy");

        var btn = document.querySelector(".copyBtn");
        btn.innerHTML = '<i class="fa fa-check"></i>';
        setTimeout(function() {
            btn.innerHTML = '<i class="fa fa-copy"></i>';
        }, 1500);
    }
</script>
@endpush
