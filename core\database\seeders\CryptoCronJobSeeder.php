<?php

namespace Database\Seeders;

use App\Constants\Status;
use App\Models\CronJob;
use App\Models\CronSchedule;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class CryptoCronJobSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, ensure we have a cron schedule
        $schedule = CronSchedule::firstOrCreate(
            ['name' => 'Hourly'],
            [
                'name' => 'Hourly',
                'interval' => 3600, // 1 hour in seconds
                'status' => Status::ENABLE
            ]
        );

        // Add the crypto fetching cron job
        CronJob::firstOrCreate(
            ['alias' => 'fetch_crypto_data'],
            [
                'name' => 'Fetch Crypto Data',
                'alias' => 'fetch_crypto_data',
                'action' => ['App\Http\Controllers\CronController', 'fetchCryptoData'],
                'next_run' => Carbon::now(),
                'cron_schedule_id' => $schedule->id,
                'is_default' => Status::YES,
                'is_running' => Status::YES
            ]
        );
    }
} 