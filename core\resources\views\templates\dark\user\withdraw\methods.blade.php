@extends($activeTemplate . 'layouts.master')

@section('content')
    <div class="card custom--card">
        <div class="card-header">
            <h5 class="card-title">{{ __($pageTitle) }}</h5>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <p class="mb-2">@lang('Total Referral Earnings'): <strong>{{ showAmount($totalReferralEarnings) }}</strong></p>
                        <p class="mb-0">@lang('Minimum Withdrawal Amount'): <strong>{{ showAmount($general->referral_min_withdrawal) }}</strong><span style="color: red;">*</span></p>
                    </div>
                </div>
            </div>

            @if($canWithdraw)
                <form action="{{ route('user.withdraw.money') }}" method="post">
                    @csrf
                    <div class="form-group">
                        <label>@lang('Amount')<span style="color: red;">*</span></label>
                        <div class="input-group">
                            <input type="number" step="any" name="amount" class="form-control form--control" value="{{ old('amount') }}" min="{{ $general->referral_min_withdrawal }}" required>
                            <span class="input-group-text">{{ $general->cur_text }}</span>
                        </div>
                        <small class="text-muted">@lang('Minimum withdrawal amount is') {{ showAmount($general->referral_min_withdrawal) }}. @lang('You can withdraw up to') {{ showAmount($totalReferralEarnings) }}</small>
                    </div>

                    <div class="form-group">
                        <label>@lang('Withdrawal Address')</label>
                        <input type="text" name="wallet_address" class="form-control form--control" required>
                        <small class="text-muted">@lang('Enter your wallet address where you want to receive funds')</small>
                    </div>

                    <div class="form-group text-center">
                        <button type="submit" class="btn btn--base withdraw-submit-btn">@lang('Submit')</button>
                    </div>
                </form>
            @else
                <div class="alert alert-warning">
                    <p class="mb-0">@lang('You need at least') {{ showAmount($general->referral_min_withdrawal) }} @lang('to withdraw referral earnings')</p>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('style')
<style>
    /* Responsive styles for withdraw card */
    .custom--card {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 5px;
        width: 100%;
    }

    .custom--card .card-header {
        background-color: #BE8400;
        color: #ffffff;
        border-radius: 5px 5px 0 0;
        padding: 20px;
    }

    .custom--card .card-body {
        padding: 25px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .withdraw-submit-btn {
        width: auto;
        min-width: 150px;
        padding: 10px 25px;
    }

    /* Tablet styles */
    @media (min-width: 768px) and (max-width: 1024px) {
        .custom--card .card-header {
            padding: 18px;
        }

        .custom--card .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 18px;
        }

        .withdraw-submit-btn {
            min-width: 140px;
            padding: 8px 20px;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .custom--card .card-header {
            padding: 15px;
        }

        .custom--card .card-body {
            padding: 15px;
        }

        .card-title {
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .input-group {
            flex-wrap: nowrap;
        }

        .withdraw-submit-btn {
            min-width: 120px;
            padding: 8px 15px;
            font-size: 14px;
        }

        .alert p {
            font-size: 13px;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .custom--card .card-header {
            padding: 12px;
        }

        .custom--card .card-body {
            padding: 12px;
        }

        .card-title {
            font-size: 15px;
        }

        .form-group label {
            font-size: 14px;
        }

        .form-control {
            font-size: 13px;
            height: 45px;
        }

        .input-group-text {
            font-size: 13px;
        }

        .text-muted {
            font-size: 12px;
        }

        .withdraw-submit-btn {
            width: 100%;
            padding: 8px 15px;
        }
    }
</style>
@endpush
