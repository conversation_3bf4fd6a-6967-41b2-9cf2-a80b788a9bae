@extends($activeTemplate . 'layouts.master')

@include('templates.dark.user.plans.custom_style')

@section('content')

<div class="row mb-4">
    <div class="col-md-12">
        <div class="alert alert-info deposit-alert" style="background-color: rgba(49, 215, 169, 0.1); border-color: #31D7A9; color: #B9BABB;">
            <div class="deposit-alert-content">
                <p class="mb-0">You need to deposit funds to your account balance before purchasing plans.</p>
                <a href="{{ route('user.deposit.funds') }}" class="btn btn--base btn-sm deposit-btn">@lang('Deposit Funds')</a>
            </div>
        </div>
    </div>
</div>

<!-- Ad Credits Plans -->
@php
    $adCreditsPlans = $plans->filter(function($plan) {
        return $plan->ad_credits > 0 && $plan->trend_votes == 0 && $plan->promote_credits == 0;
    });
@endphp

@if(count($adCreditsPlans) > 0)
<div class="mb-4">
    <div style="background-color: rgba(49, 215, 169, 0.1); padding: 15px; border-radius: 5px; border-left: 4px solid #31D7A9; margin-bottom: 20px;">
        <h4 style="color: #31D7A9; margin-bottom: 0;">📢 @lang('Buy Ad Credits')</h4>
    </div>

    <div class="row gy-4">
        @foreach($adCreditsPlans as $plan)
        <div class="col-xl-4 col-md-6">
            <div class="price-item">
                <div class="price-item__header">
                    <h2 class="price-item__price">{{ gs('cur_sym') . showAmount($plan->price, currencyFormat: false) }}</h2>
                </div>
                <div class="price-item__content">
                    <div class="price-item__body">
                        <ul class="text-list">
                            <li class="text-list__item">
                                <i class="las la-ad text--base"></i> @lang('Ad Credits'): {{ $plan->ad_credits }}
                            </li>
                            @if($plan->description)
                            <li class="text-list__item">{{ $plan->description }}</li>
                            @endif
                            @foreach ($plan->features ?? [] as $feature)
                            <li class="text-list__item">{{ $feature }}</li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="price-item__button">
                        <button class="btn--base buy-plan" data-bs-toggle="modal" data-bs-target="#buyPlanModal"
                            data-id="{{ $plan->id }}"
                            data-price="{{ showAmount($plan->price, currencyFormat: false) }}"
                            data-ad-credits="{{ $plan->ad_credits }}">@lang('Buy Now')</button>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>
@endif

<!-- Trend Votes Plans -->
@php
    $trendVotesPlans = $plans->filter(function($plan) {
        return $plan->trend_votes > 0 && $plan->promote_credits == 0;
    });
@endphp

@if(count($trendVotesPlans) > 0)
<div class="mb-4">
    <div style="background-color: rgba(49, 215, 169, 0.1); padding: 15px; border-radius: 5px; border-left: 4px solid #31D7A9; margin-bottom: 20px;">
        <h4 style="color: #31D7A9; margin-bottom: 0;">🔥 @lang('Buy Trend Votes')</h4>
    </div>

    <div class="row gy-4">
        @foreach($trendVotesPlans as $plan)
        <div class="col-xl-4 col-md-6">
            <div class="price-item">
                <div class="price-item__header">
                    <h2 class="price-item__price">{{ gs('cur_sym') . showAmount($plan->price, currencyFormat: false) }}</h2>
                </div>
                <div class="price-item__content">
                    <div class="price-item__body">
                        <ul class="text-list">
                            <li class="text-list__item">
                                <i class="las la-fire text--base"></i> @lang('Trend Votes'): {{ $plan->trend_votes }}
                            </li>
                            @if($plan->description)
                            <li class="text-list__item">{{ $plan->description }}</li>
                            @endif
                            @foreach ($plan->features ?? [] as $feature)
                            <li class="text-list__item">{{ $feature }}</li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="price-item__button">
                        <button class="btn--base buy-plan" data-bs-toggle="modal" data-bs-target="#buyPlanModal"
                            data-id="{{ $plan->id }}"
                            data-price="{{ showAmount($plan->price, currencyFormat: false) }}"
                            data-votes="{{ $plan->trend_votes }}"
                            data-credits="{{ $plan->promote_credits }}">@lang('Buy Now')</button>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>
@endif

<!-- Promote Credits Plans -->
@php
    $promoteCreditsPlans = $plans->filter(function($plan) {
        return $plan->promote_credits > 0 && $plan->trend_votes == 0;
    });
@endphp

@if(count($promoteCreditsPlans) > 0)
<div class="mb-4">
    <div style="background-color: rgba(49, 215, 169, 0.1); padding: 15px; border-radius: 5px; border-left: 4px solid #31D7A9; margin-bottom: 20px;">
        <h4 style="color: #31D7A9; margin-bottom: 0;">⚡ @lang('Buy Promote Credits')</h4>
    </div>

    <div class="row gy-4">
        @foreach($promoteCreditsPlans as $plan)
        <div class="col-xl-4 col-md-6">
            <div class="price-item">
                <div class="price-item__header">
                    <h2 class="price-item__price">{{ gs('cur_sym') . showAmount($plan->price, currencyFormat: false) }}</h2>
                </div>
                <div class="price-item__content">
                    <div class="price-item__body">
                        <ul class="text-list">
                            <li class="text-list__item">
                                <i class="las la-bolt text--base"></i> @lang('Promote Credits'): {{ $plan->promote_credits }}
                            </li>
                            @if($plan->description)
                            <li class="text-list__item">{{ $plan->description }}</li>
                            @endif
                            @foreach ($plan->features ?? [] as $feature)
                            <li class="text-list__item">{{ $feature }}</li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="price-item__button">
                        <button class="btn--base buy-plan" data-bs-toggle="modal" data-bs-target="#buyPlanModal"
                            data-id="{{ $plan->id }}"
                            data-price="{{ showAmount($plan->price, currencyFormat: false) }}"
                            data-votes="{{ $plan->trend_votes }}"
                            data-credits="{{ $plan->promote_credits }}">@lang('Buy Now')</button>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>
@endif

<!-- Combined Plans -->
@php
    $combinedPlans = $plans->filter(function($plan) {
        return $plan->trend_votes > 0 && $plan->promote_credits > 0;
    });
@endphp

@if(count($combinedPlans) > 0)
<div class="row gy-4">
    @foreach($combinedPlans as $plan)
    <div class="col-xl-4 col-md-6">
        <div class="price-item">
            <div class="price-item__header">
                <h2 class="price-item__price">{{ gs('cur_sym') . showAmount($plan->price, currencyFormat: false) }}</h2>
            </div>
            <div class="price-item__content">
                <div class="price-item__body">
                    <ul class="text-list">
                        <li class="text-list__item">
                            <i class="las la-fire text--base"></i> @lang('Trend Votes'): {{ $plan->trend_votes }}
                        </li>
                        <li class="text-list__item">
                            <i class="las la-bolt text--base"></i> @lang('Promote Credits'): {{ $plan->promote_credits }}
                        </li>
                        @if($plan->description)
                        <li class="text-list__item">{{ $plan->description }}</li>
                        @endif
                        @foreach ($plan->features ?? [] as $feature)
                        <li class="text-list__item">{{ $feature }}</li>
                        @endforeach
                    </ul>
                </div>
                <div class="price-item__button">
                    <button class="btn--base buy-plan" data-bs-toggle="modal" data-bs-target="#buyPlanModal"
                        data-id="{{ $plan->id }}"
                        data-price="{{ showAmount($plan->price, currencyFormat: false) }}"
                        data-votes="{{ $plan->trend_votes }}"
                        data-credits="{{ $plan->promote_credits }}">@lang('Buy Now')</button>
                </div>
            </div>
        </div>
    </div>
    @endforeach
</div>
@endif

@if($plans->hasPages())
<div class="mt-4">
    {{ paginateLinks($plans) }}
</div>
@endif

<!-- Buy Plan Modal -->
<div class="modal custom--modal fade" id="buyPlanModal" tabindex="-1" aria-labelledby="buyPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="buyPlanModalLabel">@lang('Buy Plan')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('user.plans.purchase') }}" method="POST">
                    @csrf
                    <input type="hidden" name="plan_id" id="plan_id">
                    <div class="plan-ad-credits-section" style="display: none;">
                        <div class="form-group">
                            <label>@lang('Ad Credits')</label>
                            <div class="input-group">
                                <span class="plan-ad-credits form-control"></span>
                            </div>
                        </div>
                    </div>
                    <div class="plan-votes-section" style="display: none;">
                        <div class="form-group">
                            <label>@lang('Trend Votes')</label>
                            <div class="input-group">
                                <span class="plan-votes form-control"></span>
                            </div>
                        </div>
                    </div>
                    <div class="plan-credits-section" style="display: none;">
                        <div class="form-group">
                            <label>@lang('Promote Credits')</label>
                            <div class="input-group">
                                <span class="plan-credits form-control"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>@lang('Price')</label>
                        <div class="input-group">
                            <span class="plan-price form-control"></span>
                            <span class="input-group-text">{{ gs('cur_text') }}</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>@lang('Current Balance')</label>
                        <div class="input-group">
                            <span class="form-control">{{ showAmount(auth()->user()->balance) }}</span>
                            <span class="input-group-text">{{ gs('cur_text') }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <input type="hidden" name="payment_method" value="1">
                    </div>
                    <div class="form-group mt-3 text-end">
                        <button type="submit" class="btn btn--base w-100">@lang('Confirm Purchase')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    (function($) {
        "use strict";

        $('.buy-plan').on('click', function() {
            var modal = $('#buyPlanModal');
            var votes = $(this).data('votes');
            var credits = $(this).data('credits');
            var adCredits = $(this).data('ad-credits');

            modal.find('#plan_id').val($(this).data('id'));
            modal.find('.plan-price').text($(this).data('price'));

            // Handle Trend Votes
            if (votes && votes > 0) {
                modal.find('.plan-votes').text(votes);
                modal.find('.plan-votes-section').show();
            } else {
                modal.find('.plan-votes-section').hide();
            }

            // Handle Promote Credits
            if (credits && credits > 0) {
                modal.find('.plan-credits').text(credits);
                modal.find('.plan-credits-section').show();
            } else {
                modal.find('.plan-credits-section').hide();
            }

            // Handle Ad Credits
            if (adCredits && adCredits > 0) {
                modal.find('.plan-ad-credits').text(adCredits);
                modal.find('.plan-ad-credits-section').show();
            } else {
                modal.find('.plan-ad-credits-section').hide();
            }
        });

        // Override default styling for dropdowns
        $('select').on('focus', function() {
            // Apply theme colors to all option elements
            $(this).find('option').each(function() {
                this.style.backgroundColor = '#2C2F3E';
                this.style.color = '#fff';
            });

            // Apply styling to the selected option
            $(this).find('option:selected').css({
                'background-color': '#BE8400',
                'color': '#fff'
            });
        });

        // When dropdown changes, apply styles to selected option
        $('select').on('change', function() {
            $(this).find('option').each(function() {
                if (this.selected) {
                    this.style.backgroundColor = '#BE8400';
                } else {
                    this.style.backgroundColor = '#2C2F3E';
                }
            });
        });
    })(jQuery);
</script>
@endpush

@push('style')
<style>
    .modal-content {
        text-align: unset !important;
    }

    .custom--modal .list-group-item:last-child {
        border-bottom: 1px solid #ffffff19;
    }

    .custom--modal select.form-control {
        background-color: #2C2F3E !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #fff !important;
        -webkit-appearance: menulist !important;
        -moz-appearance: menulist !important;
        appearance: menulist !important;
        background-image: none !important;
    }

    .custom--modal select.form-control option {
        background-color: #2C2F3E;
        color: #fff;
    }

    .custom--modal select.form-control option:hover,
    .custom--modal select.form-control option:focus,
    .custom--modal select.form-control option:active {
        background: linear-gradient(#BE8400, #BE8400) !important;
        background-color: #BE8400 !important;
        color: #fff !important;
    }

    .custom--modal select.form-control option:checked {
        background: linear-gradient(#2C2F3E, #2C2F3E) !important;
        background-color: #2C2F3E !important;
        color: #fff !important;
    }

    .custom--modal select.form-control:focus {
        box-shadow: 0 0 0 0.25rem rgba(190, 132, 0, 0.25) !important;
        border-color: #BE8400 !important;
    }

    /* Override browser default styling */
    .custom--modal select.form-control {
        outline: none !important;
    }

    /* This is needed to override browser's built-in styling */
    .form-control:focus {
        border-color: #BE8400 !important;
        box-shadow: 0 0 0 0.25rem rgba(190, 132, 0, 0.25) !important;
    }

    /* Specifically for Firefox */
    .custom--modal select.form-control:-moz-focusring {
        color: transparent !important;
        text-shadow: 0 0 0 #fff !important;
    }

    /* For webkit browsers */
    .custom--modal select.form-control::-webkit-focus-inner {
        border: 0 !important;
    }

    /* Additional overrides for the dropdown */
    select:focus-visible {
        outline-color: #BE8400 !important;
    }

    /* Deposit alert responsive styles */
    .deposit-alert {
        padding: 15px;
    }

    .deposit-alert-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .deposit-alert-content p {
        flex: 1;
        min-width: 200px;
    }

    .deposit-btn {
        white-space: nowrap;
    }

    @media (max-width: 767px) {
        .deposit-alert {
            padding: 12px;
        }

        .deposit-alert-content {
            flex-direction: column;
            align-items: flex-start;
        }

        .deposit-alert-content p {
            margin-bottom: 10px !important;
            width: 100%;
        }

        .deposit-btn {
            align-self: flex-start;
        }
    }

    @media (max-width: 575px) {
        .deposit-alert {
            padding: 10px;
        }

        .deposit-alert-content p {
            font-size: 14px;
            margin-bottom: 8px !important;
        }
    }

    @media (max-width: 375px) {
        .deposit-alert {
            padding: 8px;
        }

        .deposit-alert-content p {
            font-size: 13px;
            margin-bottom: 6px !important;
        }

        .deposit-btn {
            padding: 5px 10px;
            font-size: 12px;
        }
    }
</style>
@endpush

@section('head')
<style>
    /* Global override for select options */
    select option {
        background-color: #2C2F3E !important;
        color: #fff !important;
    }

    select option:hover,
    select option:focus,
    select option:active {
        background: #BE8400 !important;
        color: #fff !important;
    }

    select option:checked,
    select option:selected {
        background: #2C2F3E !important;
        color: #fff !important;
    }

    /* Direct override for blue highlight */
    option:hover, option:focus {
        background-color: #BE8400 !important;
    }

    /* -webkit-appearance: none causes issues with Chrome - restore but with theme colors */
    select option:hover { background-color: #BE8400 !important; }

    /* Additional attempt for webkit */
    @supports (-webkit-appearance:none) {
        select option:checked {
            background: linear-gradient(#2C2F3E, #2C2F3E) !important;
            color: #fff !important;
        }

        select option:hover {
            background: linear-gradient(#BE8400, #BE8400) !important;
            color: #fff !important;
        }
    }
</style>
@endsection
