<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\CryptoSetting;
use App\Models\CryptoCurrency;
use App\Services\DexscreenerService; // Added for consistency, though not directly used in this specific command's copied logic
use Carbon\Carbon;

class UpdateCoinMarketCapDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-coin-market-cap-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetches and updates cryptocurrency data from CoinMarketCap API.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('==== UpdateCoinMarketCapDataCommand: CRYPTO DATA FETCH STARTED ====');
        
        try {
            // Update last cron time (using a generic approach for scheduled tasks)
            // If you have a specific model/table for tracking general cron runs, you can use it here.
            // For now, we'll log the start. A gs()->last_cron update might still be relevant if gs() is a global helper for general settings.
            // Let's assume gs() is available and used like in the controller.
            if (function_exists('gs')) {
                $general = gs();
                $general->last_cron = now();
                $general->save();
                Log::info('General last_cron time updated by UpdateCoinMarketCapDataCommand');
            }

            $setting = CryptoSetting::first();

            if (!$setting || !$setting->api_key) {
                Log::error('UpdateCoinMarketCapDataCommand: Crypto cron job failed: API key not configured');
                $this->error('Crypto cron job failed: API key not configured.');
                return Command::FAILURE;
            }

            Log::info('UpdateCoinMarketCapDataCommand: Using API key: ' . substr($setting->api_key, 0, 5) . '...' . substr($setting->api_key, -5));
            Log::info('UpdateCoinMarketCapDataCommand: Display count: ' . $setting->display_count);

            try {
                Log::info('UpdateCoinMarketCapDataCommand: Sending request to CoinMarketCap API...');

                $response = Http::withHeaders([
                    'X-CMC_PRO_API_KEY' => $setting->api_key,
                    'Accept' => 'application/json'
                ])->get('https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest', [
                    'limit' => $setting->display_count,
                    'convert' => 'USD'
                ]);

                Log::info('UpdateCoinMarketCapDataCommand: API request completed with status: ' . $response->status());

                if ($response->successful()) {
                    $data = $response->json();
                    Log::info('UpdateCoinMarketCapDataCommand: Successfully parsed JSON response');
                    Log::info('UpdateCoinMarketCapDataCommand: Data count: ' . (isset($data['data']) ? count($data['data']) : 'N/A'));

                    CryptoCurrency::truncate();
                    Log::info('UpdateCoinMarketCapDataCommand: Existing crypto data cleared');

                    if (isset($data['data']) && is_array($data['data'])) {
                        foreach ($data['data'] as $crypto) {
                            $record = new CryptoCurrency();
                            $record->name = $crypto['name'];
                            $record->symbol = $crypto['symbol'];
                            $record->price = $crypto['quote']['USD']['price'];
                            $record->percent_change_24h = $crypto['quote']['USD']['percent_change_24h'];
                            $record->market_cap = $crypto['quote']['USD']['market_cap'];
                            $record->volume_24h = $crypto['quote']['USD']['volume_24h'];
                            $record->last_updated = now(); // Or Carbon::parse($crypto['quote']['USD']['last_updated']); if preferred from API
                            $record->save();
                            Log::info('UpdateCoinMarketCapDataCommand: Added crypto: ' . $crypto['name'] . ' (' . $crypto['symbol'] . ')');
                        }
                    } else {
                        Log::warning('UpdateCoinMarketCapDataCommand: No data array in API response or data is not an array.');
                    }
                    

                    $setting->last_updated = now();
                    $setting->save();
                    Log::info('UpdateCoinMarketCapDataCommand: Crypto settings last_updated time updated');
                    
                    Log::info('==== UpdateCoinMarketCapDataCommand: CRYPTO DATA FETCH COMPLETED SUCCESSFULLY ====');
                    $this->info('CoinMarketCap data updated successfully.');
                    return Command::SUCCESS;

                } else {
                    $errorMsg = 'UpdateCoinMarketCapDataCommand: Failed to fetch crypto data: ' . ($response->json()['status']['error_message'] ?? 'Unknown error');
                    Log::error($errorMsg);
                    Log::error('UpdateCoinMarketCapDataCommand: Response body: ' . $response->body());
                    $this->error($errorMsg);
                    return Command::FAILURE;
                }
            } catch (\Exception $e) {
                Log::error('UpdateCoinMarketCapDataCommand: API Error: ' . $e->getMessage());
                Log::error($e->getTraceAsString());
                $this->error('API Error: ' . $e->getMessage());
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            Log::error('UpdateCoinMarketCapDataCommand: Unexpected error: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            $this->error('Unexpected error: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
} 