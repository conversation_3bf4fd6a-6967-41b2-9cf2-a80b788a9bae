@extends($activeTemplate.'layouts.frontend')
@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card custom--card">
                <div class="card-header">
                    <h5 class="card-title">@lang('PayPal Express Checkout')</h5>
                </div>
                <div class="card-body">
                    <form action="{{ $data->url }}" method="{{ $data->method }}">
                        <ul class="list-group text-center">
                            <li class="list-group-item d-flex justify-content-between">
                                @lang('You have to pay '):
                                <strong>{{showAmount($deposit->final_amount)}} {{__($deposit->method_currency)}}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                @lang('You will get '):
                                <strong>{{showAmount($deposit->amount)}}  {{__($general->cur_text)}}</strong>
                            </li>
                        </ul>
                        <script src="https://www.paypal.com/sdk/js?client-id={{ $data->val['client_id'] }}"></script>
                        <div id="paypal-button-container" class="mt-3"></div>
                        
                        @foreach($data->val as $key => $value)
                            <input type="hidden" name="{{$key}}" value="{{$value}}">
                        @endforeach
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    (function ($) {
        "use strict";
        var paymentForm = $('form');
        paymentForm.submit();
    })(jQuery);
</script>
@endpush 