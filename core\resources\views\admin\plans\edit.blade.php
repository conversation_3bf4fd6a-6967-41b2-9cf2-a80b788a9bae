@extends('admin.layouts.app')
@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <form action="{{ route('admin.plans.update', $plan->id) }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Price')</label>
                                    <div class="input-group">
                                        <input type="number" step="any" class="form-control" name="price" required value="{{ getAmount($plan->price) }}">
                                        <span class="input-group-text">{{ __(gs('cur_text')) }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Trend Votes')</label>
                                    <input type="number" class="form-control" name="trend_votes" required value="{{ $plan->trend_votes }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Ad Credits')</label>
                                    <input type="number" class="form-control" name="ad_credits" required value="{{ $plan->ad_credits }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Promote Credits')</label>
                                    <input type="number" class="form-control" name="promote_credits" required value="{{ $plan->promote_credits ?? 0 }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Status')</label>
                                    <select name="status" class="form-control" required>
                                        <option value="1" {{ $plan->status == Status::ENABLE ? 'selected' : '' }}>@lang('Active')</option>
                                        <option value="0" {{ $plan->status == Status::DISABLE ? 'selected' : '' }}>@lang('Inactive')</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-control-label font-weight-bold">@lang('Is Featured')</label>
                                    <input type="checkbox" data-width="100%" data-onstyle="-success" data-offstyle="-danger" data-bs-toggle="toggle" data-on="@lang('Yes')" data-off="@lang('No')" name="is_featured" @if($plan->is_featured) checked @endif>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>@lang('Description')</label>
                                    <textarea name="description" class="form-control" rows="4">{{ $plan->description }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-12 mt-3">
                                <div class="form-group">
                                    <label class="d-block">@lang('Features')</label>
                                    <div class="feature-list">
                                        @if($plan->features && count($plan->features) > 0)
                                            @foreach($plan->features as $feature)
                                                <div class="feature-item input-group mb-3">
                                                    <input type="text" class="form-control" name="features[]" value="{{ $feature }}" placeholder="@lang('Feature')">
                                                    <button class="btn btn--danger input-group-text remove-feature" type="button"><i class="las la-times"></i></button>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="feature-item input-group mb-3">
                                                <input type="text" class="form-control" name="features[]" placeholder="@lang('Feature')">
                                                <button class="btn btn--danger input-group-text remove-feature" type="button"><i class="las la-times"></i></button>
                                            </div>
                                        @endif
                                    </div>
                                    <button type="button" class="btn btn-sm btn--primary add-feature"><i class="las la-plus"></i> @lang('Add Feature')</button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-3">
                            <button type="submit" class="btn btn--primary w-100 h-45">@lang('Submit')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.plans.index') }}" class="btn btn-sm btn-outline--primary">
        <i class="las la-undo"></i> @lang('Back')
    </a>
@endpush

@push('script')
    <script>
        (function($) {
            "use strict";
            $(document).on('click', '.add-feature', function() {
                var html = `
                <div class="feature-item input-group mb-3">
                    <input type="text" class="form-control" name="features[]" placeholder="@lang('Feature')">
                    <button class="btn btn--danger input-group-text remove-feature" type="button"><i class="las la-times"></i></button>
                </div>`;

                $('.feature-list').append(html);
            });

            $(document).on('click', '.remove-feature', function() {
                $(this).closest('.feature-item').remove();
            });
        })(jQuery);
    </script>
@endpush