<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\AdPosition;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update footer full ad dimensions
        $footerFullAd = AdPosition::where('key', 'footer_full')->first();
        if ($footerFullAd) {
            $footerFullAd->size = 'Full Width (Responsive)';
            $footerFullAd->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert footer full ad dimensions
        $footerFullAd = AdPosition::where('key', 'footer_full')->first();
        if ($footerFullAd) {
            $footerFullAd->size = '970x90 (Desktop), 728x90 (Tablet), 320x100 (Mobile)';
            $footerFullAd->save();
        }
    }
};
