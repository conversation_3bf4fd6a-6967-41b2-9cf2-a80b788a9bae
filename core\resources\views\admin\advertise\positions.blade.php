@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('S.N.')</th>
                                <th>@lang('Name')</th>
                                <th>@lang('Key')</th>
                                <th>@lang('Size')</th>
                                <th>@lang('Image')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($adPositions as $position)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $position->name }}</td>
                                    <td>{{ $position->key }}</td>
                                    <td>{{ $position->size }}</td>
                                    <td>
                                        @if($position->image)
                                            <a href="{{ getImage(getFilePath('ads_images').'/'.$position->image) }}" target="_blank">
                                                <img src="{{ getImage(getFilePath('ads_images').'/'.$position->image) }}" alt="@lang('Ad Image')" class="w-100" style="max-width: 100px;">
                                            </a>
                                        @else
                                            <span class="text-muted">@lang('No image')</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($position->status == 1)
                                            <span class="badge badge--success">@lang('Active')</span>
                                        @else
                                            <span class="badge badge--danger">@lang('Inactive')</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="button--group">
                                            <button type="button" class="btn btn-sm btn-outline--primary editBtn"
                                                data-id="{{ $position->id }}"
                                                data-name="{{ $position->name }}"
                                                data-key="{{ $position->key }}"
                                                data-size="{{ $position->size }}"
                                                data-description="{{ $position->description }}"
                                                data-status="{{ $position->status }}">
                                                <i class="la la-pencil"></i> @lang('Edit')
                                            </button>
                                            @if($position->image)
                                            <button type="button" class="btn btn-sm btn-outline--danger confirmationBtn" data-action="{{ route('admin.advertise.positions.delete', $position->id) }}" data-question="@lang('Are you sure you want to delete the image for this ad position?')">
                                                <i class="la la-trash"></i> @lang('Delete Image')
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td class="text-muted text-center" colspan="100%">{{ __('No ad positions found') }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($adPositions->hasPages())
                <div class="card-footer py-4">
                    {{ paginateLinks($adPositions) }}
                </div>
            @endif
        </div>
    </div>
</div>

<div class="modal fade" id="positionModal" tabindex="-1" role="dialog" aria-labelledby="createModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="createModalLabel"></h4>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <form class="form-horizontal" method="post" action="{{ route('admin.advertise.positions.store') }}" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="id" id="position_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Name')</label>
                        <input type="text" class="form-control" name="name" id="name" required>
                        <small class="text-muted">@lang('Display name for the ad position')</small>
                    </div>
                    <div class="form-group">
                        <label>@lang('Key')</label>
                        <input type="text" class="form-control" name="key" id="key" required>
                        <small class="text-muted">@lang('Unique identifier for the ad position (e.g., header_ad, footer_ad)')</small>
                    </div>
                    <div class="form-group">
                        <label>@lang('Size')</label>
                        <input type="text" class="form-control" name="size" id="size" placeholder="e.g. 300x250">
                        <small class="text-muted">@lang('Recommended size for the ad (e.g., 300x250, 728x90)')</small>
                    </div>
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="description" id="description" rows="3"></textarea>
                        <small class="text-muted">@lang('Brief description of the ad position')</small>
                    </div>
                    <div class="form-group">
                        <label>@lang('Ad Image')</label>
                        <input type="file" class="form-control" name="image" id="image" accept="image/*">
                        <small class="text-muted">@lang('Upload an image for this ad position. The image will be responsive and adjust to different screen sizes.')</small>
                        <div id="image_preview" class="mt-3 d-none">
                            <img src="" alt="@lang('Ad Preview')" class="w-100">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>@lang('Status')</label>
                        <select name="status" id="status" class="form-control" required>
                            <option value="1">@lang('Active')</option>
                            <option value="0">@lang('Inactive')</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Save')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<x-confirmation-modal />
@endsection

@push('breadcrumb-plugins')
<button class="btn btn-sm btn--primary box--shadow1 text--small addBtn">
    <i class="las la-plus"></i> @lang('Add New')
</button>
@endpush

@push('script')
<script>
    (function($) {
        "use strict";

        $('.addBtn').on('click', function() {
            $('#position_id').val('');
            $('#name').val('');
            $('#key').val('');
            $('#size').val('');
            $('#description').val('');
            $('#status').val(1);
            $('#image_preview').addClass('d-none');
            $('#image_preview img').attr('src', '');
            $('#createModalLabel').text('Add New Ad Position');
            $('#positionModal').modal('show');
        });

        $('.editBtn').on('click', function() {
            var modal = $('#positionModal');
            $('#position_id').val($(this).data('id'));
            $('#name').val($(this).data('name'));
            $('#key').val($(this).data('key'));
            $('#size').val($(this).data('size'));
            $('#description').val($(this).data('description'));
            $('#status').val($(this).data('status'));
            $('#createModalLabel').text('Edit Ad Position');
            modal.modal('show');
        });

        // Image preview
        $('#image').on('change', function() {
            var file = this.files[0];
            if (file) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#image_preview').removeClass('d-none');
                    $('#image_preview img').attr('src', e.target.result);
                }
                reader.readAsDataURL(file);
            } else {
                $('#image_preview').addClass('d-none');
                $('#image_preview img').attr('src', '');
            }
        });
    })(jQuery);
</script>
@endpush
