@props(['position'])

@php
    $adPosition = \App\Models\AdPosition::where('key', $position)->where('status', 1)->first();
    $selectedBanner = null;
    $isUserBanner = false;

    if ($adPosition) {
        // Check if admin has set a banner for this position
        $hasAdminBanner = $adPosition->image ? true : false;

        // Check for active user banners
        $userBanners = collect();

        // Get banners that are active and not expired
        $activeBanner = \App\Models\UserBanner::where('ad_position_id', $adPosition->id)
            ->where('status', 1) // Only approved banners
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>', now());
            })
            ->inRandomOrder()
            ->first();

        // Only consider the user banner if the user has ad credits
        if ($activeBanner) {
            $user = \App\Models\User::find($activeBanner->user_id);
            if ($user && $user->ad_credits > 0) {
                $userBanners->push($activeBanner);
            } else {
                // If user has no credits, pause all their active banners
                \App\Models\UserBanner::where('user_id', $activeBanner->user_id)
                    ->where('status', 1) // Only active banners
                    ->update(['status' => 3]); // Set to paused
            }
        }

        // Decide which banner to show - give both admin and user banners a chance
        if ($hasAdminBanner && $userBanners->isNotEmpty()) {
            // Both admin and user banners exist - randomly choose
            // Add admin banner to the mix with equal chance
            if (rand(0, 1) == 0) {
                // Show admin banner
                $selectedBanner = $adPosition;
                $isUserBanner = false;
            } else {
                // Show user banner
                $selectedBanner = $userBanners->first();
                $isUserBanner = true;
            }
        } elseif ($hasAdminBanner) {
            // Only admin banner exists
            $selectedBanner = $adPosition;
            $isUserBanner = false;
        } elseif ($userBanners->isNotEmpty()) {
            // Only user banners exist
            $selectedBanner = $userBanners->first();
            $isUserBanner = true;
        }
    }
@endphp

@if($selectedBanner && $isUserBanner)
    {{-- User banner - add data-banner-id for impression tracking --}}
    <div class="ad-container ad-{{ $position }}" data-banner-id="{{ $selectedBanner->id }}">
        @if($selectedBanner->redirect_url)
            <a href="{{ $selectedBanner->redirect_url }}" target="_blank" rel="noopener noreferrer">
                <img src="{{ getImage(getFilePath('ads_images').'/'.$selectedBanner->image) }}" alt="{{ $adPosition->name }}" class="img-fluid responsive-ad">
            </a>
        @else
            <img src="{{ getImage(getFilePath('ads_images').'/'.$selectedBanner->image) }}" alt="{{ $adPosition->name }}" class="img-fluid responsive-ad">
        @endif
    </div>
@elseif($selectedBanner && !$isUserBanner)
    {{-- Admin banner - no data-banner-id to prevent impression tracking --}}
    <div class="ad-container ad-{{ $position }}">
        <img src="{{ getImage(getFilePath('ads_images').'/'.$selectedBanner->image) }}" alt="{{ $selectedBanner->name }}" class="img-fluid responsive-ad">
    </div>
@endif

@push('style')
<style>
    .ad-container {
        width: 100%;
        margin: 15px 0;
        text-align: center;
        overflow: hidden;
    }

    .responsive-ad {
        max-width: 100%;
        height: auto;
        display: inline-block;
        width: 100%;
        object-fit: cover;
    }

    .ad-container a {
        display: block;
        cursor: pointer;
    }

    /* Media queries for responsive ads */
    @media (max-width: 991px) {
        .ad-container {
            margin: 10px 0;
        }
    }

    @media (max-width: 767px) {
        .ad-container {
            margin: 8px 0;
        }
    }
</style>
@endpush

@push('script')
<script>
    (function($) {
        "use strict";

        // Initialize Intersection Observer to track ad impressions
        document.addEventListener('DOMContentLoaded', function() {
            // Check if Intersection Observer API is supported
            if ('IntersectionObserver' in window) {
                // Check if we've already initialized the observer (prevents multiple initializations)
                if (window.adImpressionObserverInitialized) {
                    return;
                }

                // Mark as initialized to prevent duplicate initialization
                window.adImpressionObserverInitialized = true;

                const adContainers = document.querySelectorAll('.ad-container[data-banner-id]');

                // Create a map to track which ads have already been counted
                const impressionTracked = new Map();

                const adObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        // Check if ad is fully visible and hasn't been counted yet
                        if (entry.isIntersecting && entry.intersectionRatio >= 1.0) {
                            const bannerId = entry.target.getAttribute('data-banner-id');

                            // Only track impression once per page view
                            if (bannerId && !impressionTracked.get(bannerId)) {
                                impressionTracked.set(bannerId, true);

                                // Send AJAX request to record impression
                                $.ajax({
                                    url: "{{ url('api/ad-impression') }}",
                                    type: 'POST',
                                    data: {
                                        banner_id: bannerId,
                                        _token: "{{ csrf_token() }}"
                                    },
                                    success: function(response) {
                                        console.log('Ad impression recorded');
                                    },
                                    error: function(error) {
                                        console.error('Error recording ad impression');
                                    }
                                });

                                // Stop observing this ad after recording impression
                                adObserver.unobserve(entry.target);
                            }
                        }
                    });
                }, {
                    threshold: 1.0 // 100% of the ad must be visible
                });

                // Start observing all ad containers
                adContainers.forEach(container => {
                    adObserver.observe(container);
                });
            }
        });
    })(jQuery);
</script>
@endpush
