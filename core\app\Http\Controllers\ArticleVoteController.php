<?php

namespace App\Http\Controllers;

use App\Models\ArticleVote;
use App\Models\Frontend;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ArticleVoteController extends Controller
{
    public function vote(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            $notify[] = ['error', 'You must be logged in to vote for articles'];
            return back()->withNotify($notify);
        }

        $articleId = $request->article_id;
        $ipAddress = $request->ip();
        $today = Carbon::now()->format('Y-m-d');

        // Verify article exists
        $article = Frontend::where('id', $articleId)
            ->where('data_keys', 'blog.element')
            ->first();

        if (!$article) {
            $notify[] = ['error', 'Article not found'];
            return back()->withNotify($notify);
        }

        // Check if user has already voted today
        if (ArticleVote::hasVotedToday($articleId, $ipAddress)) {
            $notify[] = ['error', 'You have already voted for this article today'];
            return back()->withNotify($notify);
        }

        // Record the vote
        ArticleVote::create([
            'article_id' => $articleId,
            'user_id' => auth()->id(),
            'ip_address' => $ipAddress,
            'voted_at' => $today,
        ]);

        $notify[] = ['success', 'Your vote has been counted! This helps the article trend on the homepage.'];
        return back()->withNotify($notify);
    }

    public function useTrendVote(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            $notify[] = ['error', 'You must be logged in to use trend votes'];
            return back()->withNotify($notify);
        }

        $articleId = $request->article_id;
        $ipAddress = $request->ip();
        $today = Carbon::now()->format('Y-m-d');
        $user = auth()->user();
        
        // Get quantity of votes to use (default to 1 if not specified)
        $quantity = max(1, intval($request->quantity));

        // Verify article exists
        $article = Frontend::where('id', $articleId)
            ->where('data_keys', 'blog.element')
            ->first();

        if (!$article) {
            $notify[] = ['error', 'Article not found'];
            return back()->withNotify($notify);
        }

        // Check if user has enough trend votes available
        if ($user->trend_votes < $quantity) {
            $notify[] = ['error', 'You don\'t have enough trend votes. You requested ' . $quantity . ' but have ' . $user->trend_votes . ' available.'];
            return back()->withNotify($notify);
        }

        // Apply multiple votes at once
        for ($i = 0; $i < $quantity; $i++) {
            // Generate a unique random suffix for the IP to bypass the unique constraint
            $randomSuffix = '_tv_' . uniqid();
            $modifiedIp = $ipAddress . $randomSuffix;

            // Record the vote using trend vote - with modified IP to bypass unique constraint
            ArticleVote::create([
                'article_id' => $articleId,
                'user_id' => $user->id,
                'ip_address' => $modifiedIp, // Use modified IP to bypass unique constraint
                'voted_at' => $today,
                'used_trend_vote' => true,
            ]);
        }

        // Decrease user's trend votes
        $user->trend_votes -= $quantity;
        $user->save();

        $notify[] = ['success', 'Your ' . $quantity . ' trend vote(s) have been counted! This gives the article an extra boost to trend on the homepage.'];
        return back()->withNotify($notify);
    }
} 