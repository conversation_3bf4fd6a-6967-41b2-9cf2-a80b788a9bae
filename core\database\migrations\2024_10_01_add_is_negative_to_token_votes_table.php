<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('token_votes', function (Blueprint $table) {
            if (!Schema::hasColumn('token_votes', 'is_negative')) {
                $table->boolean('is_negative')->default(false)->after('used_trend_vote');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('token_votes', function (Blueprint $table) {
            if (Schema::hasColumn('token_votes', 'is_negative')) {
                $table->dropColumn('is_negative');
            }
        });
    }
};
