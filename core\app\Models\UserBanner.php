<?php

namespace App\Models;

use App\Constants\Status;
use Illuminate\Database\Eloquent\Model;

class UserBanner extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function adPosition()
    {
        return $this->belongsTo(AdPosition::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', Status::ENABLE)
            ->where(function ($q) {
                $q->whereNull('end_date')
                  ->orWhere('end_date', '>', now());
            });
    }

    public function impressions()
    {
        return $this->hasMany(AdImpression::class);
    }

    /**
     * Get total impressions for this banner
     *
     * @return int
     */
    public function getTotalImpressions()
    {
        return $this->impressions()->sum('count');
    }
}
