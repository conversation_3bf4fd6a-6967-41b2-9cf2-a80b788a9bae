<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ccl_token_data', function (Blueprint $table) {
            if (!Schema::hasColumn('ccl_token_data', 'is_enabled')) {
                $table->boolean('is_enabled')->default(true)->after('distribution_items');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ccl_token_data', function (Blueprint $table) {
            if (Schema::hasColumn('ccl_token_data', 'is_enabled')) {
                $table->dropColumn('is_enabled');
            }
        });
    }
};
