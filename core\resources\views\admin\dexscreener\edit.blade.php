@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title"></h5>
            </div>
            <form action="{{ route('admin.dexscreener.update', $token->id) }}" method="POST">
                @csrf
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="chain_id">@lang('Blockchain')</label>
                                <select name="chain_id" id="chain_id" class="form-control" required>
                                    <option value="">@lang('Select Blockchain')</option>
                                    @foreach($blockchains as $id => $name)
                                        <option value="{{ $id }}" {{ old('chain_id', $token->chain_id) == $id ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="token_address">@lang('Token Contract Address')</label>
                                <input type="text" name="token_address" id="token_address" class="form-control" value="{{ old('token_address', $token->token_address) }}" required placeholder="0x..." />
                                <small class="text-muted">@lang('Enter the contract address of the token (e.g., 0x1234...)')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="token_name">@lang('Token Name')</label>
                                <input type="text" name="token_name" id="token_name" class="form-control" value="{{ old('token_name', $token->token_name) }}" required placeholder="Bitcoin" />
                                <small class="text-muted">@lang('Enter the full name of the token (e.g., Bitcoin)')</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="token_symbol">@lang('Token Symbol')</label>
                                <input type="text" name="token_symbol" id="token_symbol" class="form-control" value="{{ old('token_symbol', $token->token_symbol) }}" required placeholder="BTC" />
                                <small class="text-muted">@lang('Enter the symbol of the token (e.g., BTC)')</small>
                            </div>
                        </div>
                    </div>

                    @if($isPresale)
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <i class="las la-exclamation-triangle me-2"></i> @lang('This is a presale token. If you update the contract address to a real address, the system will try to fetch price and other market data from the DexScreener API.')
                            </div>
                        </div>
                    </div>
                    @else
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="las la-info-circle me-2"></i> @lang('After updating the token, you can refresh the data to fetch the latest price and market information from the DexScreener API.')
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                <div class="card-footer">
                    <div class="form-group">
                        <button type="submit" class="btn btn--primary w-100 h-45">@lang('Update Token')</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.dexscreener.index') }}" class="btn btn-sm btn--primary">
        <i class="las la-list"></i> @lang('All Tokens')
    </a>
@endpush

@push('script')
<script>
    (function($) {
        "use strict";

        // You can add any additional script here if needed

    })(jQuery);
</script>
@endpush
