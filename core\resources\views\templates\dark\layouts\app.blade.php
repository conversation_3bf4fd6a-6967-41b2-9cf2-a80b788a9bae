<!doctype html>
<html lang="en" itemscope itemtype="http://schema.org/WebPage">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title> {{ gs()->siteName(__(@$customPageTitle ?? $pageTitle)) }}</title>

    @include('partials.seo')
    <link href="{{ asset('assets/global/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/global/css/all.min.css') }}" rel="stylesheet">
    <link href=" {{ asset($activeTemplateTrue . 'css/slick.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/global/css/line-awesome.min.css') }}" rel="stylesheet" />
    <link href="{{ asset($activeTemplateTrue . 'css/style.css') }}" rel="stylesheet">
    <link href="{{ asset($activeTemplateTrue . 'css/custom.css') }}" rel="stylesheet">
    <link type="text/css" href="{{ asset($activeTemplateTrue . 'css/color.php') }}?color={{ gs('base_color') }}" rel="stylesheet">
    <link href="{{ asset($activeTemplateTrue . 'css/dropdown-fix.css') }}?v={{ time() }}" rel="stylesheet">

    <style>
        /* Custom pagination styling */
        .pagination-container {
            display: block;
            width: 100%;
        }

        /* Style for the "Showing X to Y of Z results" text */
        .pagination-container .d-sm-flex {
            display: block !important;
        }

        /* Left align the results text */
        .pagination-container .text-sm-start,
        .pagination-container p {
            text-align: left !important;
            margin-bottom: 10px;
        }

        /* Center the pagination numbers only */
        .pagination-container .pagination {
            display: flex;
            justify-content: center !important;
            margin: 0 auto;
        }

        .pagination .page-item {
            margin: 0 3px;
        }

        /* Make sure the pagination is centered */
        .pagination-container nav > div > div:last-child,
        .pagination-container nav > ul {
            display: flex;
            justify-content: center !important;
            width: 100%;
        }

        /* Fix for specific Bootstrap 5 pagination structure */
        .pagination-container .justify-content-sm-between {
            display: block;
            width: 100%;
        }

        .pagination-container .justify-content-sm-between > p {
            text-align: left !important;
            margin-bottom: 10px;
        }

        .pagination-container .justify-content-sm-between > nav {
            margin: 0 auto;
            display: flex;
            justify-content: center;
        }

        /* Additional fixes for all possible variations */
        .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center {
            display: block !important;
        }

        .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center > div:first-child {
            text-align: left !important;
            margin-bottom: 10px;
        }

        .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center > div:last-child {
            display: flex;
            justify-content: center;
        }
    </style>

    @stack('style-lib')
    @stack('style')
</head>

@php echo  loadExtension('google-analytics') @endphp

<body>
    @stack('fbComment')
    <div class="preloader">
        <div class="loader-p"></div>
    </div>
    <div class="sidebar-overlay"></div>

    @yield('panel')

    <a class="scroll-top"><i class="fas fa-angle-double-up"></i></a>

    <script src="{{ asset('assets/global/js/jquery-3.7.1.min.js') }}"></script>
    <script src="{{ asset('assets/global/js/bootstrap.bundle.min.js') }}"></script>

    <script src="{{ asset($activeTemplateTrue . 'js/slick.min.js') }}"></script>
    <script src="{{ asset($activeTemplateTrue . 'js/main.js') }}"></script>

    @stack('script-lib')

    @php echo  loadExtension('tawk-chat') @endphp

    @include('partials.notify')

    @if (gs('pn'))
        @include('partials.push_script')
    @endif

    @stack('script')
    @stack('modal')

    <script>
        'use strict';
        (function($) {
            let disableSubmission = false;
            $('.disableSubmission').on('submit', function(e) {
                if (disableSubmission) {
                    e.preventDefault()
                } else {
                    disableSubmission = true;
                }
            });

            // Fix for all dropdowns - apply theme color to all select elements
            $(document).ready(function() {
                // Apply theme colors to all select elements
                $('select').css({
                    'background-color': '#2C2F3E',
                    'color': '#fff',
                    'border': '1px solid rgba(255, 255, 255, 0.1)'
                });

                // Handle option hover and selection
                $(document).on('mouseover', 'select option', function() {
                    $(this).css({
                        'background-color': '#BE8400',
                        'color': '#fff'
                    });
                });

                // Force style on option change
                $('select').on('change', function() {
                    $(this).find('option:selected').css({
                        'background-color': '#BE8400',
                        'color': '#fff'
                    });
                });

                // Apply colors immediately after page load
                $('select option:selected').css({
                    'background-color': '#BE8400',
                    'color': '#fff'
                });
            });
        })(jQuery)
    </script>

</body>

</html>
