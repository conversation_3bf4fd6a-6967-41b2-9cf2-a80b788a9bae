<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    public function index()
    {
        $pageTitle = 'Manage Plans';
        $plans = Plan::latest()->paginate(getPaginate());
        return view('admin.plans.index', compact('pageTitle', 'plans'));
    }

    public function create()
    {
        $pageTitle = 'Create Plan';
        return view('admin.plans.create', compact('pageTitle'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'trend_votes' => 'required|integer|min:0',
            'promote_credits' => 'required|integer|min:0',
            'ad_credits' => 'required|integer|min:0',
            'is_featured' => 'nullable|boolean',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'status' => 'required|in:0,1',
        ]);

        // Handle features properly to avoid NULL issues
        $features = [];
        if ($request->has('features') && is_array($request->features)) {
            $features = array_filter($request->features);
        }

        $plan = new Plan();
        $plan->name = null;
        $plan->description = $request->description;
        $plan->price = $request->price;
        $plan->trend_votes = $request->trend_votes;
        $plan->promote_credits = $request->promote_credits;
        $plan->ad_credits = $request->ad_credits;
        $plan->is_featured = $request->is_featured ? 1 : 0;
        $plan->features = $features; // Always an array, even if empty
        $plan->status = $request->status;
        $plan->save();

        $notify[] = ['success', 'Plan created successfully'];
        return to_route('admin.plans.index')->withNotify($notify);
    }

    public function edit($id)
    {
        $pageTitle = 'Edit Plan';
        $plan = Plan::findOrFail($id);
        return view('admin.plans.edit', compact('pageTitle', 'plan'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'trend_votes' => 'required|integer|min:0',
            'promote_credits' => 'required|integer|min:0',
            'ad_credits' => 'required|integer|min:0',
            'is_featured' => 'nullable|boolean',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'status' => 'required|in:0,1',
        ]);

        // Handle features properly to avoid NULL issues
        $features = [];
        if ($request->has('features') && is_array($request->features)) {
            $features = array_filter($request->features);
        }

        $plan = Plan::findOrFail($id);
        $plan->name = null;
        $plan->description = $request->description;
        $plan->price = $request->price;
        $plan->trend_votes = $request->trend_votes;
        $plan->promote_credits = $request->promote_credits;
        $plan->ad_credits = $request->ad_credits;
        $plan->is_featured = $request->is_featured ? 1 : 0;
        $plan->features = $features; // Always an array, even if empty
        $plan->status = $request->status;
        $plan->save();

        $notify[] = ['success', 'Plan updated successfully'];
        return back()->withNotify($notify);
    }

    public function delete($id)
    {
        $plan = Plan::findOrFail($id);

        // Check if the plan has any purchases
        if ($plan->purchases()->count() > 0) {
            $notify[] = ['error', 'Plan cannot be deleted as it has active purchases'];
            return back()->withNotify($notify);
        }

        $plan->delete();

        $notify[] = ['success', 'Plan deleted successfully'];
        return back()->withNotify($notify);
    }

    public function changeStatus($id)
    {
        $plan = Plan::findOrFail($id);

        if ($plan->status == Status::ENABLE) {
            $plan->status = Status::DISABLE;
            $msg = 'Plan disabled successfully';
        } else {
            $plan->status = Status::ENABLE;
            $msg = 'Plan enabled successfully';
        }

        $plan->save();

        $notify[] = ['success', $msg];
        return back()->withNotify($notify);
    }
}