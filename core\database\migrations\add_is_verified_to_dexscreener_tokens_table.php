<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            $table->boolean('is_verified')->default(false)->after('sell_count');
            $table->bigInteger('submitted_by_user_id')->nullable()->after('is_verified');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            $table->dropColumn(['is_verified', 'submitted_by_user_id']);
        });
    }
}; 