<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Presale extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'token_name',
        'token_symbol',
        'start_date',
        'end_date',
        'quantity',
        'price',
        'next_price',
        'sold',
        'is_active',
        'position'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'price' => 'decimal:8',
        'next_price' => 'decimal:8',
        'sold' => 'decimal:8',
        'is_active' => 'boolean',
        'position' => 'integer'
    ];

    /**
     * Get the progress percentage of the presale
     *
     * @return float
     */
    public function getProgressPercentage()
    {
        // Get the original total quantity (before any tokens were sold)
        $originalQuantity = $this->getOriginalQuantity();

        if (empty($originalQuantity) || $originalQuantity == 0) {
            return 0;
        }

        // Ensure quantity is a valid numeric value
        $quantity = (float)preg_replace('/[^0-9.]/', '', $this->quantity);

        // Calculate remaining percentage based on current quantity
        $remainingPercentage = ($quantity / $originalQuantity) * 100;
        $progress = 100 - $remainingPercentage;

        return min(100, max(0, $progress)); // Cap between 0-100%
    }

    /**
     * Get the original total quantity before any tokens were sold
     *
     * @return float
     */
    private function getOriginalQuantity()
    {
        // Ensure quantity is a valid numeric value
        $quantity = (float)preg_replace('/[^0-9.]/', '', $this->quantity);

        // If price is 0, return current quantity to avoid division by zero
        if ($this->price <= 0) {
            return $quantity;
        }

        // Calculate original quantity: current quantity + (sold amount in USD / token price)
        $soldTokens = $this->sold / $this->price;
        return $quantity + $soldTokens;
    }

    /**
     * Check if the presale is active
     *
     * @return bool
     */
    public function isActive()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now()->startOfDay();

        // If no start date is set, consider it as not active yet (Upcoming)
        if (!$this->start_date) {
            return false;
        }

        // If only start date is set, check if it's started
        if ($this->start_date && !$this->end_date) {
            return $now->gte($this->start_date);
        }

        // If only end date is set (this shouldn't happen with our changes), check if it hasn't ended
        if (!$this->start_date && $this->end_date) {
            return $now->lte($this->end_date);
        }

        // If both dates are set, check if it's within range
        return $now->gte($this->start_date) && $now->lte($this->end_date);
    }

    /**
     * Get the status of the presale
     *
     * @return string
     */
    public function getStatus()
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        $now = now()->startOfDay();

        // If start date is set and it's in the future, return Upcoming
        if ($this->start_date && $now->lt($this->start_date)) {
            return 'Upcoming';
        }

        // If no start date is set, consider it as Upcoming
        if (!$this->start_date) {
            return 'Upcoming';
        }

        if ($this->end_date && $now->gt($this->end_date)) {
            return 'Ended';
        }

        // Check if presale is 100% sold and quantity is 0
        $quantity = (float)preg_replace('/[^0-9.]/', '', $this->quantity);
        if ($this->getProgressPercentage() >= 100 && $quantity <= 0) {
            return 'Completed';
        }

        return 'Active';
    }
}
