@extends($activeTemplate . 'layouts.master')

@section('content')
    <div class="card custom--card card-deposit">
        <div class="card-header">
            <h5 class="card-title">@lang('Payment Preview')</h5>
        </div>
        <div class="card-body card-body-deposit text-center">
            <h4 class="my-2"> @lang('PLEASE SEND EXACTLY') <span class="text-success"> {{ $data->amount }}</span> {{ __($data->currency) }}</h4>
            <h5 class="mb-2">@lang('TO')
                <div class="d-inline-flex align-items-center">
                    <span class="text-success me-2" id="addressText">{{ $data->sendto }}</span>
                    <button class="btn btn-sm btn-outline-warning copy-address-btn" data-clipboard-text="{{ $data->sendto }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Copy">
                        <i class="las la-copy"></i>
                    </button>
                </div>
            </h5>
            <img src="{{ $data->img }}" alt="@lang('Image')">
            <h4 class="bold mt-4 text-white">@lang('SCAN TO SEND')</h4>

            <div class="alert alert-warning mt-4">
                <i class="las la-exclamation-triangle me-2"></i> @lang('Please do not move away or refresh this page until you have made the payment.')
            </div>

            <div class="mt-4">
                <a href="{{ route('user.home') }}" class="btn btn--base">@lang('Dashboard')</a>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.8/dist/clipboard.min.js"></script>
<script>
    (function($) {
        "use strict";

        // Initialize clipboard.js
        var clipboard = new ClipboardJS('.copy-address-btn');

        clipboard.on('success', function(e) {
            // Show tooltip with "Copied!" text
            $(e.trigger).attr('title', 'Copied!').tooltip('_fixTitle').tooltip('show');

            // Reset tooltip after 1.5 seconds
            setTimeout(function() {
                $(e.trigger).attr('title', 'Copy').tooltip('_fixTitle');
            }, 1500);

            e.clearSelection();
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    })(jQuery);
</script>
@endpush
