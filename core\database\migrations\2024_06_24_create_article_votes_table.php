<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('article_votes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('article_id')->comment('Frontend ID of the article');
            $table->unsignedBigInteger('user_id')->nullable()->comment('User ID, null if guest vote');
            $table->string('ip_address', 40)->comment('IP address of the voter');
            $table->date('voted_at');
            $table->timestamps();
            
            // Unique constraint to prevent multiple votes from same user/IP for same article on same day
            $table->unique(['article_id', 'ip_address', 'voted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('article_votes');
    }
}; 