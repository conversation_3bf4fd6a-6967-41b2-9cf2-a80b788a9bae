<?php

namespace App\Console\Commands;

use App\Services\DexscreenerService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RefreshDexscreenerTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dexscreener:refresh';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh token data from the Dexscreener API';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(DexscreenerService $dexscreenerService)
    {
        $this->info('Refreshing Dexscreener token data...');

        try {
            // Clear all token-related caches
            \Illuminate\Support\Facades\Cache::forget('dexscreener_tokens');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens_active');
            \Illuminate\Support\Facades\Cache::forget('admin_tokens_all');

            // First, check for presale tokens with real contract addresses that might be live now
            $this->info('Checking for presale tokens with real contract addresses that might be live now...');

            // Use the DexscreenerService method to update presale tokens
            $updatedCount = $dexscreenerService->updatePresaleTokensWithPriceData();

            $this->info('Updated ' . $updatedCount . ' presale tokens with live data');

            // Now run the regular token refresh
            $success = $dexscreenerService->refreshTokenData();

            if ($success) {
                $this->info('Token data refreshed successfully.');
                Log::info('Dexscreener token data refreshed successfully.');
                return Command::SUCCESS;
            } else {
                $this->error('Failed to refresh token data.');
                Log::error('Failed to refresh Dexscreener token data.');
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            $this->error('An error occurred: ' . $e->getMessage());
            Log::error('Exception while refreshing Dexscreener token data: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}