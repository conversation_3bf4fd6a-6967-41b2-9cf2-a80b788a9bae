<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>{{ gs('site_name') }} - PayPal Express Checkout</title>
    <link rel="stylesheet" href="{{ asset('assets/global/css/bootstrap.min.css') }}">
</head>

<body>
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6 text-center">
                <div class="card">
                    <div class="card-header">
                        <h4>{{ __('Redirecting to PayPal') }}</h4>
                    </div>
                    <div class="card-body">
                        @if(isset($data) && isset($data->url) && isset($data->method) && isset($data->val) && !empty($data->val))
                            <form id="auto_submit" action="{{ $data->url }}" method="{{ $data->method }}">
                                @foreach ((array)$data->val as $k => $v)
                                    <input name="{{ $k }}" type="hidden" value="{{ $v }}" />
                                @endforeach
                            </form>
                            <p>{{ __('Please wait while you are redirecting to PayPal for secure payment...') }}</p>
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p><small>{{ __('Do not close or refresh this page') }}</small></p>
                            <button class="btn btn-primary" onclick="document.getElementById('auto_submit').submit();">{{ __('Click here if you are not redirected automatically') }}</button>
                        @else
                            <div class="alert alert-danger">
                                <p>{{ __('Error: Payment data is not properly configured.') }}</p>
                                <a href="{{ route('user.payment.history') }}" class="btn btn-primary mt-3">{{ __('Return to Payment History') }}</a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        "use strict";
        document.addEventListener("DOMContentLoaded", function() {
            @if(isset($data) && isset($data->url) && isset($data->method) && isset($data->val) && !empty($data->val))
                setTimeout(function() {
                    document.getElementById("auto_submit").submit();
                }, 1000);
            @endif
        });
    </script>
</body>

</html> 