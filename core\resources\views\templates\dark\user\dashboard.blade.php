@extends($activeTemplate . 'layouts.master')
@section('content')
    @if (gs('kv') && auth()->user()->kv != Status::KYC_VERIFIED)
        @php
            $kyc = getContent('kyc.content', true);
        @endphp
        <div class="row mb-3">
            <div class="container">
                <div class="row">
                    @if (auth()->user()->kv == Status::KYC_UNVERIFIED && auth()->user()->kyc_rejection_reason)
                        <div class="alert alert-danger" role="alert">
                            <div class="d-flex justify-content-between align-items-center">
                                <h4 class="alert-heading mb-0">@lang('KYC Documents Rejected')</h4>
                                <button class="btn btn--danger btn-sm" data-bs-toggle="modal"
                                    data-bs-target="#kycRejectionReason">@lang('Show Reason')</button>
                            </div>
                            <hr class="my-3">
                            <p class="mb-2">{{ __(@$kyc->data_values->reject) }} <a class="text--base"
                                    href="{{ route('user.kyc.form') }}">@lang('Click Here to Re-submit Documents')</a>.</p>
                            <a class="text--base" href="{{ route('user.kyc.data') }}">@lang('See KYC Data')</a>
                        </div>
                    @elseif(auth()->user()->kv == Status::KYC_UNVERIFIED)
                        <div class="alert alert-info" role="alert">
                            <h4 class="alert-heading">@lang('KYC Verification required')</h4>
                            <hr>
                            <p class="mb-0">{{ __(@$kyc->data_values->required) }} <a class="text--base"
                                    href="{{ route('user.kyc.form') }}">@lang('Click Here to Submit Documents')</a></p>
                        </div>
                    @elseif(auth()->user()->kv == Status::KYC_PENDING)
                        <div class="alert alert-warning" role="alert">
                            <h4 class="alert-heading">@lang('KYC Verification pending')</h4>
                            <hr>
                            <p class="mb-0">{{ __(@$kyc->data_values->pending) }} <a class="text--base"
                                    href="{{ route('user.kyc.data') }}">@lang('See KYC Data')</a></p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Info alert about depositing funds -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="alert alert-info deposit-alert" style="background-color: rgba(49, 215, 169, 0.1); border-color: #31D7A9; color: #B9BABB;">
                <div class="deposit-alert-content">
                    <p class="mb-0">@lang('You need to deposit funds to your account balance before using paid services.')</p>
                    <a href="{{ route('user.deposit.funds') }}" class="btn btn--base btn-sm deposit-btn">@lang('Deposit Funds')</a>
                </div>
            </div>
        </div>
    </div>

    <!-- dashboard-section start -->
    <div class="row gy-4 dashboard-card-wrapper">
        <div class="col-xl-20-percent col-md-6 col-sm-6">
            <div class="dashboard-card border-bottom-info">
                <div class="dashboard-card__thumb-title">
                    <div class="dashboard-card__thumb rounded-0 border-0">
                        <i class="las la-money-bill fa-2x"></i>
                    </div>
                    <h5 class="dashboard-card__title"> @lang('Balance')</h5>
                </div>
                <div class="dashboard-card__content">
                    <h4 class="dashboard-card__Status">{{ showAmount(auth()->user()->balance) }}</h4>
                </div>
            </div>
        </div>

        <div class="col-xl-20-percent col-md-6 col-sm-6">
            <div class="dashboard-card border-bottom-violet">
                <div class="dashboard-card__thumb-title">
                    <div class="dashboard-card__thumb rounded-0 border-0">
                        <i class="las la-wallet fa-2x"></i>
                    </div>
                    <h5 class="dashboard-card__title"> @lang('Referral')</h5>
                </div>
                <div class="dashboard-card__content">
                    <h4 class="dashboard-card__Status">{{ showAmount($referralBonus) }}</h4>
                </div>
            </div>
        </div>

        <div class="col-xl-20-percent col-md-6 col-sm-6">
            <div class="dashboard-card border-bottom-warning">
                <div class="dashboard-card__thumb-title">
                    <div class="dashboard-card__thumb rounded-0 border-0">
                        <i class="las la-fire fa-2x"></i>
                    </div>
                    <h5 class="dashboard-card__title"> @lang('Trend Votes')</h5>
                </div>
                <div class="dashboard-card__content">
                    <h4 class="dashboard-card__Status">{{ auth()->user()->trend_votes }}</h4>
                </div>
            </div>
        </div>

        <div class="col-xl-20-percent col-md-6 col-sm-6">
            <div class="dashboard-card border-bottom-primary">
                <div class="dashboard-card__thumb-title">
                    <div class="dashboard-card__thumb rounded-0 border-0">
                        <i class="las la-bolt fa-2x"></i>
                    </div>
                    <h5 class="dashboard-card__title"> @lang('Promote Credits')</h5>
                </div>
                <div class="dashboard-card__content">
                    <h4 class="dashboard-card__Status">{{ auth()->user()->promote_credits ?? 0 }}</h4>
                </div>
            </div>
        </div>

        <div class="col-xl-20-percent col-md-6 col-sm-6">
            <div class="dashboard-card border-bottom-success">
                <div class="dashboard-card__thumb-title">
                    <div class="dashboard-card__thumb rounded-0 border-0">
                        <i class="las la-ad fa-2x"></i>
                    </div>
                    <h5 class="dashboard-card__title"> @lang('Ad Credits')</h5>
                </div>
                <div class="dashboard-card__content">
                    <h4 class="dashboard-card__Status">{{ auth()->user()->ad_credits ?? 0 }}</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- dashboard-section end -->

    <div class="pt-40">
        <h5>@lang('Latest Transactions')</h5>
        <div class="dashboard-table">
            @include($activeTemplate . 'partials.transaction_table', ['transactions' => $transactions])
        </div>
        @if($transactions->hasPages())
        <div class="mt-4 pagination-container">
            {{ paginateLinks($transactions) }}
        </div>
        @endif
    </div>

    @if (auth()->user()->kv == Status::KYC_UNVERIFIED && auth()->user()->kyc_rejection_reason)
        <div class="modal fade" id="kycRejectionReason">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">@lang('KYC Document Rejection Reason')</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                            <i class="las la-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>{{ auth()->user()->kyc_rejection_reason }}</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@push('style')
    <style>
        .custom--modal .btn-close {
            background-image: none !important;
            color: #fff !important;
            font-size: 20px
        }

        .custom--modal .btn-close:focus {
            box-shadow: none !important;
        }

        /* Dashboard responsive styles */
        .dashboard-card {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            padding: 15px;
            height: 100%;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .dashboard-card.border-bottom-info {
            border-bottom-color: #17a2b8;
        }

        .dashboard-card.border-bottom-violet {
            border-bottom-color: #6f42c1;
        }

        .dashboard-card.border-bottom-warning {
            border-bottom-color: #BE8400;
        }

        .dashboard-card.border-bottom-primary {
            border-bottom-color: #007bff;
        }

        .dashboard-card.border-bottom-success {
            border-bottom-color: #28c76f;
        }

        /* Special adjustment for Promote Credits card */
        .dashboard-card.border-bottom-primary .dashboard-card__title {
            padding-left: 2px;
        }

        .dashboard-card.border-bottom-primary {
            padding-left: 10px;
        }

        /* Custom 20% width column for 5 equal cards */
        .col-xl-20-percent {
            width: 20%;
        }

        .dashboard-card__thumb-title {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }

        .dashboard-card__thumb {
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
        }

        .dashboard-card__title {
            margin-bottom: 0;
            width: calc(100% - 30px);
            padding-left: 8px;
            font-size: 16px;
            white-space: nowrap;
        }

        .dashboard-card__content {
            margin-top: 10px;
            text-align: right;
        }

        .dashboard-card__Status {
            margin-bottom: 0;
            font-size: 20px;
            white-space: nowrap;
        }

        /* Responsive adjustments */
        @media (max-width: 1199px) {
            .dashboard-card {
                padding: 15px;
            }

            .dashboard-card__thumb {
                width: 28px;
                height: 28px;
            }

            .dashboard-card__title {
                width: calc(100% - 28px);
                padding-left: 6px;
                font-size: 15px;
            }

            .dashboard-card__Status {
                font-size: 18px;
                white-space: nowrap;
            }

            .dashboard-card__thumb i {
                font-size: 1.8em !important;
            }

            /* Reset 20% width to default Bootstrap grid on smaller screens */
            .col-xl-20-percent {
                width: 100%;
            }
        }

        @media (max-width: 1024px) {
            .dashboard-card-wrapper {
                margin-left: -8px;
                margin-right: -8px;
            }

            .dashboard-card-wrapper > div {
                padding-left: 8px;
                padding-right: 8px;
            }

            .dashboard-card {
                padding: 12px;
                margin-bottom: 15px;
            }
        }

        @media (max-width: 991px) {
            .dashboard-card {
                padding: 12px;
            }

            .dashboard-card__thumb {
                width: 25px;
                height: 25px;
            }

            .dashboard-card__title {
                width: calc(100% - 25px);
                padding-left: 6px;
                font-size: 14px;
            }

            .dashboard-card__Status {
                font-size: 16px;
                white-space: nowrap;
            }

            .dashboard-card__thumb i {
                font-size: 1.6em !important;
            }
        }

        @media (max-width: 767px) {
            .dashboard-card {
                padding: 12px;
            }

            .dashboard-card__thumb {
                width: 40px;
                height: 40px;
            }

            .dashboard-card__title {
                width: calc(100% - 40px);
                padding-left: 10px;
                font-size: 14px;
            }

            .dashboard-card__Status {
                font-size: 16px;
                white-space: nowrap;
            }

            .dashboard-card__thumb i {
                font-size: 1.5em !important;
            }
        }

        @media (max-width: 575px) {
            .dashboard-card-wrapper {
                margin-left: -5px;
                margin-right: -5px;
            }

            .dashboard-card-wrapper > div {
                padding-left: 5px;
                padding-right: 5px;
            }

            .dashboard-card {
                padding: 10px;
            }

            .dashboard-card__thumb-title {
                flex-direction: row;
                align-items: center;
            }

            .dashboard-card__thumb {
                width: 35px;
                height: 35px;
                margin-bottom: 0;
                justify-content: center;
            }

            .dashboard-card__title {
                width: calc(100% - 35px);
                padding-left: 8px;
                font-size: 13px;
            }

            .dashboard-card__content {
                text-align: right;
                margin-top: 8px;
            }

            .dashboard-card__Status {
                font-size: 15px;
                white-space: nowrap;
            }

            .dashboard-card__thumb i {
                font-size: 1.4em !important;
            }
        }

        @media (max-width: 375px) {
            .dashboard-card-wrapper {
                margin-left: -3px;
                margin-right: -3px;
            }

            .dashboard-card-wrapper > div {
                padding-left: 3px;
                padding-right: 3px;
            }

            .dashboard-card {
                padding: 8px;
            }

            .dashboard-card__thumb {
                width: 30px;
                height: 30px;
            }

            .dashboard-card__title {
                width: calc(100% - 30px);
                padding-left: 6px;
                font-size: 12px;
            }

            .dashboard-card__Status {
                font-size: 14px;
                white-space: nowrap;
            }

            .dashboard-card__thumb i {
                font-size: 1.3em !important;
            }
        }

        /* Specific iPad adjustments */
        @media (width: 1024px) and (height: 1366px) {
            .dashboard-card {
                padding: 15px;
            }

            .dashboard-card__thumb {
                width: 28px;
                height: 28px;
            }

            .dashboard-card__title {
                width: calc(100% - 28px);
                padding-left: 6px;
            }
        }

        @media (width: 1024px) and (height: 600px) {
            .dashboard-card {
                padding: 12px;
            }

            .dashboard-card__thumb {
                width: 25px;
                height: 25px;
            }

            .dashboard-card__title {
                width: calc(100% - 25px);
                padding-left: 6px;
            }
        }

        /* Transaction table responsive styles */
        .dashboard-table {
            overflow-x: auto;
            width: 100%;
            max-width: 100%;
            -webkit-overflow-scrolling: touch;
            border-radius: 5px;
        }

        @media (max-width: 1024px) {
            .pt-40 {
                padding-top: 30px;
            }

            .dashboard-table {
                margin: 0 -10px;
                padding: 0 10px;
                width: calc(100% + 20px);
            }

            .dashboard-table h5 {
                padding-left: 10px;
            }
        }

        @media (max-width: 991px) {
            .pt-40 {
                padding-top: 20px;
            }

            .dashboard-table {
                margin: 0 -8px;
                padding: 0 8px;
            }
        }

        @media (max-width: 767px) {
            .pt-40 {
                padding-top: 18px;
            }

            .dashboard-table {
                margin: 0 -6px;
                padding: 0 6px;
            }
        }

        @media (max-width: 575px) {
            .pt-40 {
                padding-top: 15px;
            }

            .dashboard-table {
                margin: 0 -5px;
                padding: 0 5px;
                width: calc(100% + 10px);
            }
        }

        @media (max-width: 375px) {
            .pt-40 {
                padding-top: 12px;
            }

            .dashboard-table {
                margin: 0 -3px;
                padding: 0 3px;
            }

            .dashboard-table h5 {
                font-size: 16px;
                margin-bottom: 10px;
            }
        }

        /* Fix for horizontal scrolling */
        html, body {
            overflow-x: hidden;
            width: 100%;
            position: relative;
        }

        /* Specific iPad adjustments */
        @media (width: 1024px) and (height: 1366px),
               (width: 1024px) and (height: 600px),
               (width: 768px) and (height: 1024px),
               (width: 820px) and (height: 1180px),
               (width: 912px) and (height: 1368px),
               (width: 853px) and (height: 1280px) {
            .dashboard-table {
                margin: 0 -15px;
                padding: 0 15px;
                width: calc(100% + 30px);
                border-radius: 5px;
            }

            .dashboard-table h5 {
                padding-left: 15px;
                margin-bottom: 15px;
                font-size: 18px;
            }
        }

        /* General tablet adjustments for all orientations */
        @media only screen and (min-width: 768px) and (max-width: 1024px) {
            .dashboard-table {
                border-radius: 5px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }
        }

        /* Pagination responsive styles */
        /* Hide unnecessary elements */
        .pagination-wrapper .d-flex.justify-content-between.flex-fill,
        .pagination-container .d-flex.justify-content-between.flex-fill {
            display: none !important;
        }

        /* Main container for pagination */
        .pagination-wrapper,
        .pagination-container {
            width: 100%;
            overflow-x: auto;
            padding: 5px 0;
            margin: 10px 0;
            scrollbar-width: thin;
        }

        .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
        .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
            display: block !important;
            width: 100%;
        }

        /* Left align the "Showing X to Y of Z results" text */
        .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child,
        .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child {
            text-align: left !important;
            margin-bottom: 10px;
        }

        /* Center the pagination buttons */
        .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
        .pagination-wrapper nav,
        .pagination-wrapper .pagination,
        .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
        .pagination-container nav,
        .pagination-container .pagination,
        .pagination-container nav > ul {
            display: flex;
            justify-content: center !important;
            width: 100%;
        }

        /* Improved pagination styling for all devices */
        .pagination {
            flex-wrap: wrap;
            gap: 5px;
            margin: 0;
            padding: 0;
            justify-content: center;
            max-width: 100%;
        }

        /* Handle large number of pagination items - general fallback */
        .pagination.pagination-sm {
            justify-content: center;
            padding: 5px 0;
        }

        /* Ensure consistent spacing between pagination items */
        .pagination .page-item {
            margin: 3px;
        }

        /* Style for pagination links */
        .pagination .page-item .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
            padding: 0 10px;
            border-radius: 4px !important;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        /* Style for forward and back buttons */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            font-size: 14px;
            padding: 0 12px;
            min-width: 40px; /* Slightly wider for navigation buttons */
        }

        /* Ensure arrow icons are visible */
        .pagination .page-item .page-link span[aria-hidden="true"] {
            display: inline-block;
            line-height: 1;
        }

        /* Style for disabled pagination items */
        .pagination .page-item.disabled .page-link {
            background-color: hsl(0deg 0% 100% / 40%);
            opacity: 0.7;
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 575px) {
            .pagination-wrapper,
            .pagination-container {
                padding: 3px 0;
                margin: 8px 0;
            }

            .pagination {
                gap: 3px;
            }

            .pagination .page-item {
                margin: 2px;
            }

            .pagination .page-item .page-link {
                min-width: 32px;
                height: 32px;
                font-size: 13px;
            }

            /* Adjust forward/back buttons on small screens */
            .pagination .page-item:first-child .page-link,
            .pagination .page-item:last-child .page-link,
            .pagination .page-item.previous .page-link,
            .pagination .page-item.next .page-link {
                min-width: 36px;
                padding: 0 10px;
            }
        }

        /* Responsive adjustments for very small screens */
        @media (max-width: 375px) {
            .pagination .page-item .page-link {
                min-width: 30px;
                height: 30px;
                font-size: 12px;
                padding: 0 8px;
            }
        }

        /* Tablet-specific adjustments */
        @media (min-width: 768px) and (max-width: 1024px) {
            .pagination-wrapper,
            .pagination-container {
                padding: 8px 0;
                margin: 12px 0;
            }

            .pagination {
                gap: 6px;
            }

            .pagination .page-item {
                margin: 3px;
            }

            .pagination .page-item .page-link {
                min-width: 38px;
                height: 38px;
                font-size: 15px;
            }

            /* Adjust forward/back buttons on tablets */
            .pagination .page-item:first-child .page-link,
            .pagination .page-item:last-child .page-link,
            .pagination .page-item.previous .page-link,
            .pagination .page-item.next .page-link {
                min-width: 42px;
                padding: 0 12px;
                font-size: 15px;
            }
        }


        /* Deposit alert responsive styles */
        .deposit-alert {
            padding: 15px;
        }

        .deposit-alert-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .deposit-alert-content p {
            flex: 1;
            min-width: 200px;
        }

        .deposit-btn {
            white-space: nowrap;
        }

        @media (max-width: 767px) {
            .deposit-alert {
                padding: 12px;
            }

            .deposit-alert-content {
                flex-direction: column;
                align-items: flex-start;
            }

            .deposit-alert-content p {
                margin-bottom: 10px !important;
                width: 100%;
            }

            .deposit-btn {
                align-self: flex-start;
            }
        }

        @media (max-width: 575px) {
            .deposit-alert {
                padding: 10px;
            }

            .deposit-alert-content p {
                font-size: 14px;
                margin-bottom: 8px !important;
            }
        }

        @media (max-width: 375px) {
            .deposit-alert {
                padding: 8px;
            }

            .deposit-alert-content p {
                font-size: 13px;
                margin-bottom: 6px !important;
            }

            .deposit-btn {
                padding: 5px 10px;
                font-size: 12px;
            }
        }
    </style>
@endpush
