<?php

use Illuminate\Support\Facades\Route;

Route::get('/clear', function () {
    \Illuminate\Support\Facades\Artisan::call('optimize:clear');
});


Route::get('cron', 'CronController@cron')->name('cron');
Route::get('fetch-crypto', function() {
    return app()->make('App\Http\Controllers\CronController')->fetchCryptoData();
});

// Token Details
Route::get('token/{chainId}/{tokenAddress}', 'App\Http\Controllers\TokenController@details')->name('token.details');
Route::post('token/security', 'App\Http\Controllers\TokenController@getTokenSecurity')->name('token.security');

// User Support Ticket
Route::controller('TicketController')->prefix('ticket')->name('ticket.')->group(function () {
    Route::get('/', 'supportTicket')->name('index');
    Route::get('new', 'openSupportTicket')->name('open');
    Route::post('create', 'storeSupportTicket')->name('store');
    Route::get('view/{ticket}', 'viewTicket')->name('view');
    Route::post('reply/{id}', 'replyTicket')->name('reply');
    Route::post('close/{id}', 'closeTicket')->name('close');
    Route::get('download/{attachment_id}', 'ticketDownload')->name('download');
});

Route::get('app/deposit/confirm/{hash}', 'Gateway\PaymentController@appDepositConfirm')->name('deposit.app.confirm');

// Payment Gateway IPN Routes
Route::name('ipn.')->prefix('ipn')->namespace('Gateway')->group(function () {
    Route::post('paypalexpress', 'PaypalExpress\ProcessController@ipn')->name('paypalexpress');
    Route::post('paypal', 'Paypal\ProcessController@ipn')->name('Paypal');
    Route::get('paypal-sdk', 'PaypalSdk\ProcessController@ipn')->name('PaypalSdk');
    Route::post('perfect-money', 'PerfectMoney\ProcessController@ipn')->name('PerfectMoney');
    Route::post('stripe', 'Stripe\ProcessController@ipn')->name('Stripe');
    Route::post('stripe-js', 'StripeJs\ProcessController@ipn')->name('StripeJs');
    Route::post('stripe-v3', 'StripeV3\ProcessController@ipn')->name('StripeV3');
    Route::post('skrill', 'Skrill\ProcessController@ipn')->name('Skrill');
    Route::post('paytm', 'Paytm\ProcessController@ipn')->name('Paytm');
    Route::post('payeer', 'Payeer\ProcessController@ipn')->name('Payeer');
    Route::post('paystack', 'Paystack\ProcessController@ipn')->name('Paystack');
    Route::get('flutterwave/{trx}/{type}', 'Flutterwave\ProcessController@ipn')->name('Flutterwave');
    Route::post('razorpay', 'Razorpay\ProcessController@ipn')->name('Razorpay');
    Route::post('instamojo', 'Instamojo\ProcessController@ipn')->name('Instamojo');
    Route::get('blockchain', 'Blockchain\ProcessController@ipn')->name('Blockchain');
    Route::post('coinpayments', 'Coinpayments\ProcessController@ipn')->name('Coinpayments');
    Route::post('coinpayments-fiat', 'CoinpaymentsFiat\ProcessController@ipn')->name('CoinpaymentsFiat');
    Route::post('coingate', 'Coingate\ProcessController@ipn')->name('Coingate');
    Route::post('coinbase-commerce', 'CoinbaseCommerce\ProcessController@ipn')->name('CoinbaseCommerce');
    Route::get('mollie', 'Mollie\ProcessController@ipn')->name('Mollie');
    Route::post('cashmaal', 'Cashmaal\ProcessController@ipn')->name('Cashmaal');
    Route::post('mercado-pago', 'MercadoPago\ProcessController@ipn')->name('MercadoPago');
    Route::post('authorize', 'Authorize\ProcessController@ipn')->name('Authorize');
    Route::get('nmi', 'NMI\ProcessController@ipn')->name('NMI');
    Route::any('btc-pay', 'BTCPay\ProcessController@ipn')->name('BTCPay');
    Route::post('now-payments-hosted', 'NowPaymentsHosted\ProcessController@ipn')->name('NowPaymentsHosted');
    Route::post('now-payments-checkout', 'NowPaymentsCheckout\ProcessController@ipn')->name('NowPaymentsCheckout');
    Route::post('2checkout', 'TwoCheckout\ProcessController@ipn')->name('TwoCheckout');
    Route::any('checkout', 'Checkout\ProcessController@ipn')->name('Checkout');
    Route::post('sslcommerz', 'SslCommerz\ProcessController@ipn')->name('SslCommerz');
    Route::post('aamarpay', 'Aamarpay\ProcessController@ipn')->name('Aamarpay');
    Route::get('binance', 'Binance\ProcessController@ipn')->name('Binance');
});

Route::controller('SiteController')->group(function () {
    Route::get('/contact', 'contact')->name('contact');
    Route::get('/contact2', 'contact2')->name('contact2');
    Route::post('/contact', 'contactSubmit');
    Route::post('/contact2', 'contactSubmit2');
    Route::get('/change/{lang?}', 'changeLanguage')->name('lang');

    Route::get('cookie-policy', 'cookiePolicy')->name('cookie.policy');
    Route::get('/cookie/accept', 'cookieAccept')->name('cookie.accept');

    Route::get('/blogs', 'blogs')->name('blog');
    Route::get('blog/{slug}', 'blogDetails')->name('blog.details');
    Route::post('article/vote', 'ArticleVoteController@vote')->name('article.vote')->middleware('auth');
    Route::post('article/trend-vote', 'ArticleVoteController@useTrendVote')->name('article.trend-vote')->middleware('auth');

    // Launchpad page (formerly CCL Token)
    Route::get('/launchpad', 'cclToken')->name('ccl.token');

    // Token voting routes
    Route::post('token/vote', 'TokenVoteController@vote')->name('token.vote')->middleware('auth');
    Route::post('token/negative-vote', 'TokenVoteController@negativeVote')->name('token.negative-vote')->middleware('auth');
    Route::post('token/trend-vote', 'TokenVoteController@useTrendVote')->name('token.trend-vote')->middleware('auth');
    Route::post('token/check-vote', 'TokenVoteController@checkVote')->name('check.token.vote')->middleware('auth');
    Route::post('token/batch-check-vote', 'TokenVoteController@batchCheckVote')->name('token.vote.batch.check')->middleware('auth');

    // Token watchlist routes
    Route::post('token/watchlist/toggle', 'User\TokenWatchlistController@toggleWatchlist')->name('token.watchlist.toggle')->middleware('auth');
    Route::post('token/watchlist/check', 'User\TokenWatchlistController@checkWatchlist')->name('token.watchlist.check')->middleware('auth');
    Route::post('token/watchlist/batch-check', 'User\TokenWatchlistController@batchCheckWatchlist')->name('token.watchlist.batch.check')->middleware('auth');

    // Token search route
    Route::get('search', 'App\Http\Controllers\SearchController@search')->name('search');

    Route::get('policy/{slug}', 'policyPages')->name('policy.pages');

    Route::get('placeholder-image/{size}', 'placeholderImage')->withoutMiddleware('maintenance')->name('placeholder.image');
    Route::get('maintenance-mode', 'maintenance')->withoutMiddleware('maintenance')->name('maintenance');

    Route::get('/{slug}', 'pages')->name('pages');
    Route::get('/', 'index')->name('home');
    Route::post('/subscribe', 'addSubscriber')->name('subscribe');
});
// Cron Routes with Password Protection
// Route::get('/cron/update-coinmarketcap', [App\Http\Controllers\CronController::class, 'updateCoinMarketCapData']);

// Route::get('/cron/update-presale-tokens', [App\Http\Controllers\CronController::class, 'updatePresaleTokensData']);

// Route::get('/cron/update-popular-tokens', [App\Http\Controllers\CronController::class, 'updatePopularTokensData']);

// Route::get('/cron/update-unpopular-tokens', [App\Http\Controllers\CronController::class, 'updateUnpopularTokensData']);

// Migration Route
Route::get('/migrate/token-tables', function() {
    $migrations = [
        'database/migrations/2025_05_26_022659_add_performance_indexes_to_token_tables.php',
        'database/migrations/2025_05_30_003652_create_presale_cron_updates_table.php',
        'database/migrations/2025_05_30_072504_create_popular_cron_updates_table.php',
        'database/migrations/2025_05_30_072510_create_unpopular_cron_updates_table.php'
    ];

    $results = [];
    foreach ($migrations as $migration) {
        $results[$migration] = \Illuminate\Support\Facades\Artisan::call('migrate', [
            '--path' => $migration,
            '--force' => true
        ]);
    }

    return response()->json([
        'status' => 'success',
        'message' => 'Migrations completed',
        'results' => $results
    ]);
})->name('migrate.token.tables');