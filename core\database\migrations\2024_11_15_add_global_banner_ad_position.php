<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\AdPosition;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create new ad position for global banner
        $adPosition = [
            'name' => 'Global Banner',
            'key' => 'global_banner',
            'size' => 'Full Width (Responsive)',
            'description' => 'Banner displayed below menu on all pages except home page',
            'status' => 1,
        ];

        // Check if position already exists
        $exists = AdPosition::where('key', $adPosition['key'])->exists();
        if (!$exists) {
            AdPosition::create($adPosition);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the added ad position
        AdPosition::where('key', 'global_banner')->delete();
    }
};
