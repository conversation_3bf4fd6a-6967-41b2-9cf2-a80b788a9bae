@php
    use App\Models\Frontend;
    use App\Models\ArticleVote;
    $general = gs();
    $content = getContent('blog.content', true);

    // Get all articles
    $allBlogs = Frontend::activeTemplate()
              ->where('data_keys', 'blog.element')
              ->get();

    // Compute votes for each article and add to collection
    foreach ($allBlogs as $blog) {
        // Calculate regular vote count
        $regularVotes = ArticleVote::getVoteCount($blog->id);

        // Get admin trend votes (default to 0 if not set)
        $adminTrendVotes = 0;
        if (isset($blog->data_values->trend_votes) && is_numeric($blog->data_values->trend_votes)) {
            $adminTrendVotes = (int)$blog->data_values->trend_votes;
        }

        // Total vote count is the sum of regular votes and admin-specified trend votes
        $blog->vote_count = $regularVotes + $adminTrendVotes;
    }

    // Filter to only include articles with at least 1 vote
    $blogsWithVotes = $allBlogs->filter(function($item) {
        return $item->vote_count > 0;
    });

    // Sort all blogs by total vote count (highest first)
    $blogsWithVotes = $blogsWithVotes->sortByDesc('vote_count');

    // Add global rank to each article based on vote count
    $rank = 1;
    $prevVotes = null;
    $prevRank = 1;

    foreach ($blogsWithVotes as $blog) {
        if ($prevVotes !== null && $blog->vote_count < $prevVotes) {
            // Only increment rank when vote count decreases
            $prevRank = $rank;
        } else if ($prevVotes !== null && $blog->vote_count == $prevVotes) {
            // Keep same rank for equal vote counts
            $rank = $prevRank;
        }

        $blog->global_rank = $rank;
        $prevVotes = $blog->vote_count;
        $rank++;
    }

    // Take the top N articles based on settings
    $blogs = $blogsWithVotes->take($general->homepage_trending_articles_count);
@endphp

@if($blogs->count() > 0)
<section class="trending-articles py-3">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="trending-articles-header d-flex align-items-center">
                    <h4 class="mb-0 d-inline-flex align-items-center justify-content-center section-title"><span class="fire-emoji me-2">🔥</span>Trending Articles</h4>
                </div>

                <div class="trending-articles-wrapper mt-1">
                    <div class="articles-ticker">
                        @foreach($blogs as $index => $blog)
                            <div class="ticker-item">
                                <div class="trending-article-item">
                                    <a href="{{ route('blog.details', $blog->slug) }}" class="d-block">
                                        <div class="trending-article-thumb position-relative">
                                            <img src="{{ frontendImage('blog', 'thumb_' . @$blog->data_values->blog_image, '200x130') }}" alt="{{ __($blog->data_values->title) }}">
                                            <div class="trending-position">#{{ $blog->global_rank }}</div>
                                        </div>
                                        <div class="trending-article-content mt-2">
                                            <h5 class="trending-article-title">{{ __(substr($blog->data_values->title, 0, 22)) }}...</h5>
                                            <div class="d-flex justify-content-center align-items-center mt-2">
                                                <span class="vote-badge">
                                                    <span class="fire-emoji-small">🔥</span>
                                                    <span class="vote-count-small">{{ ArticleVote::getTotalVoteCount($blog->id) }}</span>
                                                </span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endif

@push('style')
<style>
    .trending-articles {
        background-color: var(--section-bg);
    }
    .trending-articles-header {
        padding-bottom: 5px;
    }
    .fire-emoji {
        font-size: 24px;
    }
    .section-title {
        margin-left: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #243242;
        border-radius: 6px;
        padding: 6px 12px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        font-family: 'Chakra Petch', sans-serif;
        font-weight: 700;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        background: linear-gradient(to bottom, #ffffff, #BE8400);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .section-title:hover {
        transform: translateY(-2px);
        box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.5);
    }
    .trending-articles-wrapper {
        overflow: hidden;
        position: relative;
    }
    .articles-ticker {
        display: flex;
        white-space: nowrap;
        overflow: hidden;
    }
    .ticker-item {
        display: inline-block;
        margin-right: 20px;
        flex-shrink: 0;
    }
    .trending-article-item {
        transition: all 0.3s;
        width: 200px;
    }
    .trending-article-item:hover {
        transform: translateY(-5px);
    }
    .trending-article-thumb {
        position: relative;
    }
    .trending-article-thumb img {
        width: 200px;
        height: 130px;
        object-fit: cover;
        border-radius: 8px;
    }
    .trending-position {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: rgba(255, 87, 34, 0.9);
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    .trending-article-title {
        font-size: 14px;
        color: #fff;
        margin-top: 8px;
        line-height: 1.4;
        font-weight: 500;
        text-align: center;
        width: 200px;
    }

    .vote-badge {
        display: inline-flex;
        align-items: center;
        background-color: rgba(255, 87, 34, 0.1);
        border: 1px solid rgba(255, 87, 34, 0.3);
        border-radius: 12px;
        padding: 2px 8px;
        font-size: 12px;
    }

    .fire-emoji-small {
        font-size: 12px;
        margin-right: 4px;
    }

    .vote-count-small {
        color: #FF5722;
        font-weight: 500;
    }
</style>
@endpush

@push('script')
<script>
    $(document).ready(function(){
        var $wrapper = $('.trending-articles-wrapper');
        var $ticker = $('.articles-ticker');
        var $items = $('.ticker-item');

        // If we have items to display
        if ($items.length > 0) {
            // Calculate the total width of all items
            var totalItemWidth = 0;
            $items.each(function() {
                totalItemWidth += $(this).outerWidth(true);
            });

            // Clone enough items to fill the viewport at least twice
            var itemsToClone = Math.ceil(($wrapper.width() * 2) / totalItemWidth) * $items.length;

            for (var i = 0; i < itemsToClone; i++) {
                $ticker.append($items.eq(i % $items.length).clone());
            }

            // Set up the animation
            function animateTicker() {
                var firstItem = $('.ticker-item').first();
                var width = firstItem.outerWidth(true);

                $ticker.animate({marginLeft: -width}, 3000, 'linear', function() {
                    // Move the first item to the end
                    $ticker.append(firstItem);
                    // Reset margin for continuous effect
                    $ticker.css({marginLeft: 0});
                    // Continue the animation
                    animateTicker();
                });
            }

            // Start the animation
            animateTicker();

            // Pause animation on hover
            $('.trending-article-item').hover(
                function() {
                    // Pause the animation by stopping all animations on the ticker
                    $ticker.stop(true, false);
                },
                function() {
                    // Resume the animation
                    animateTicker();
                }
            );
        }
    });
</script>
@endpush