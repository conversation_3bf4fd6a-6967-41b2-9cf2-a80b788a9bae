<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DexscreenerToken;
use App\Models\Page;
use App\Models\SocialLink;
use App\Models\SubmitCoin;
use App\Models\TokenPromotion;
use App\Models\TokenVote;
use App\Models\TokenWatchlist;
use App\Services\DexscreenerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class DexscreenerController extends Controller
{
    protected $dexscreenerService;

    public function __construct(DexscreenerService $dexscreenerService)
    {
        $this->dexscreenerService = $dexscreenerService;
    }

    /**
     * Display a list of all Dexscreener tokens
     */
    public function index(Request $request)
    {
        $pageTitle = ''; // Empty page title to hide the breadcrumb title
        $search = $request->search;

        $query = DexscreenerToken::orderBy('updated_at', 'desc');

        // Apply search filter if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('token_name', 'like', "%{$search}%")
                  ->orWhere('token_symbol', 'like', "%{$search}%")
                  ->orWhere('token_address', 'like', "%{$search}%");
            });
        }

        $tokens = $query->paginate(10);

        return view('admin.dexscreener.index', compact('pageTitle', 'tokens', 'search'));
    }

    /**
     * Refresh token data from the Dexscreener API
     */
    public function refresh()
    {
        // Clear all token-related caches
        Cache::forget('dexscreener_tokens');
        Cache::forget('admin_tokens');
        Cache::forget('admin_tokens_active');
        Cache::forget('admin_tokens_all');

        $success = $this->dexscreenerService->refreshTokenData();

        // Make sure the promoted_coins section is enabled on the homepage
        $this->ensurePromotedCoinsSection();

        if ($success) {
            $notify[] = ['success', 'Token data has been refreshed successfully'];
        } else {
            $notify[] = ['error', 'Failed to refresh token data'];
        }

        return back()->withNotify($notify);
    }

    /**
     * Delete a token
     */
    public function delete($id)
    {
        $token = DexscreenerToken::findOrFail($id);

        // Also delete the submit_coins record if it exists
        // For presale tokens, the contract_address might be a generated ID
        $submitCoin = SubmitCoin::where('contract_address', $token->token_address)
            ->first();

        if ($submitCoin) {
            $submitCoin->delete();
        }

        // Delete associated social links
        SocialLink::where('token_chain_id', $token->chain_id)
            ->where('token_address', $token->token_address)
            ->delete();

        $token->delete();

        // Clear the cache
        Cache::forget('dexscreener_tokens');

        $notify[] = ['success', 'Token has been deleted successfully'];
        return back()->withNotify($notify);
    }

    /**
     * Update presale status for a token
     */
    public function updatePresaleStatus($id)
    {
        $token = DexscreenerToken::findOrFail($id);

        // Find the corresponding SubmitCoin record
        $submitCoin = SubmitCoin::where('contract_address', $token->token_address)
            ->first();

        if (!$submitCoin) {
            $notify[] = ['error', 'Could not find the corresponding submitted coin record'];
            return back()->withNotify($notify);
        }

        // Check if this is a presale or fair launch token with a real contract address
        $isPresale = $submitCoin->is_presale == 1;
        $isFairLaunch = $submitCoin->is_fair_launch == 1;
        $hasRealContract = !empty($submitCoin->contract_address) &&
                          strpos($submitCoin->contract_address, 'presale_') !== 0 &&
                          strpos($submitCoin->contract_address, 'fairlaunch_') !== 0;

        if ((!$isPresale && !$isFairLaunch) || !$hasRealContract) {
            $notify[] = ['error', 'This token is not a presale or fair launch token with a real contract address'];
            return back()->withNotify($notify);
        }

        try {
            // Fetch token details from DexScreener
            $tokenData = $this->dexscreenerService->fetchTokenDetails($token->chain_id, $token->token_address);

            // If we have price data, update the token
            if ($tokenData && isset($tokenData['price_usd']) && $tokenData['price_usd'] > 0) {
                // Update the token with the latest price data
                if (is_object($token->metadata)) {
                    // If metadata is already an object, convert it to array
                    $metadata = json_decode(json_encode($token->metadata), true);
                } elseif (is_string($token->metadata)) {
                    // If metadata is a string, decode it
                    $metadata = json_decode($token->metadata ?? '{}', true);
                } else {
                    // Default to empty array
                    $metadata = [];
                }

                // Remove the force_presale_display flag if it exists
                if (isset($metadata['force_presale_display'])) {
                    unset($metadata['force_presale_display']);
                }

                // Update the token data
                $token->price_usd = $tokenData['price_usd'];
                $token->price_change_24h = $tokenData['price_change_24h'] ?? $token->price_change_24h;
                $token->price_change_5m = $tokenData['price_change_5m'] ?? $token->price_change_5m;
                $token->price_change_1h = $tokenData['price_change_1h'] ?? $token->price_change_1h;
                $token->price_change_6h = $tokenData['price_change_6h'] ?? $token->price_change_6h;
                $token->volume_24h = $tokenData['volume_24h'] ?? $token->volume_24h;
                $token->market_cap = $tokenData['market_cap'] ?? $token->market_cap;
                $token->liquidity_usd = $tokenData['liquidity_usd'] ?? $token->liquidity_usd;
                $token->txn_24h = $tokenData['txn_24h'] ?? $token->txn_24h;
                $token->buy_count = $tokenData['buy_count'] ?? $token->buy_count;
                $token->sell_count = $tokenData['sell_count'] ?? $token->sell_count;
                $token->token_age = $tokenData['token_age'] ?? null;
                $token->metadata = json_encode($metadata);
                $token->pair_address = $tokenData['pair_address'] ?? $token->pair_address;
                $token->dex_id = $tokenData['dex_id'] ?? $token->dex_id;
                $token->image_url = $tokenData['image_url'] ?? $token->image_url;
                $token->updated_at = now();
                $token->save();

                // Update the SubmitCoin record to mark it as no longer presale or fair launch
                $submitCoin->is_presale = 0;
                $submitCoin->is_fair_launch = 0;
                $submitCoin->save();

                // Clear all token-related caches
                Cache::forget('dexscreener_tokens');
                Cache::forget('admin_tokens');
                Cache::forget('admin_tokens_active');
                Cache::forget('admin_tokens_all');

                $notify[] = ['success', 'Token updated successfully and is no longer marked as presale or fair launch'];
            } else {
                // Force update the presale and fair launch status even if no price data is available
                $submitCoin->is_presale = 0;
                $submitCoin->is_fair_launch = 0;
                $submitCoin->save();

                // Clear all token-related caches
                Cache::forget('dexscreener_tokens');
                Cache::forget('admin_tokens');
                Cache::forget('admin_tokens_active');
                Cache::forget('admin_tokens_all');

                $notify[] = ['warning', 'Token is now marked as not presale or fair launch, but no price data was found'];
            }
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error updating token status: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }

        return back()->withNotify($notify);
    }

    /**
     * Ensure the promoted_coins section is enabled on the homepage
     */
    private function ensurePromotedCoinsSection()
    {
        $homepage = Page::where('tempname', activeTemplate())->where('slug', '/')->first();

        if ($homepage) {
            $secs = json_decode($homepage->secs);

            // Check if promoted_coins section is already included
            if (!in_array('promoted_coins', $secs)) {
                // Add promoted_coins section to the homepage
                $secs[] = 'promoted_coins';
                $homepage->secs = json_encode($secs);
                $homepage->save();
            }
        }
    }

    /**
     * Show form to add a new token manually
     */
    public function add()
    {
        $pageTitle = 'Add New Token';

        // Comprehensive list of blockchains to choose from (matching user submission options)
        $blockchains = [
            'ethereum' => 'Ethereum (ETH)',
            'bsc' => 'Binance Smart Chain (BSC)',
            'polygon' => 'Polygon (MATIC)',
            'solana' => 'Solana (SOL)',
            'avalanche' => 'Avalanche (AVAX)',
            'tron' => 'Tron (TRX)',
            'cardano' => 'Cardano (ADA)',
            'polkadot' => 'Polkadot (DOT)',
            'arbitrum' => 'Arbitrum (ARB)',
            'optimism' => 'Optimism (OP)',
            'base' => 'Base (ETH on Base)',
            'fantom' => 'Fantom (FTM)',
            'cronos' => 'Cronos (CRO)',
            'algorand' => 'Algorand (ALGO)',
            'cosmos' => 'Cosmos (ATOM)',
            'near' => 'Near Protocol (NEAR)',
            'aptos' => 'Aptos (APT)',
            'sui' => 'Sui (SUI)',
            'hedera' => 'Hedera (HBAR)',
            'xrp' => 'XRP Ledger (XRP)',
            'stellar' => 'Stellar (XLM)',
            'harmony' => 'Harmony (ONE)',
            'moonbeam' => 'Moonbeam (GLMR)',
            'gnosis' => 'Gnosis Chain (GNO)',
            'zilliqa' => 'Zilliqa (ZIL)',
            'zksync' => 'zkSync Era (ZKS)',
            'linea' => 'Linea Mainnet (LINEA)',
            'scroll' => 'Scroll (SCROLL)',
            'heco' => 'HECO (HT)',
            'ethw' => 'ETHW (ETHW)',
            'kcc' => 'KCC (KCS)',
            'fon' => 'FON (FON)',
            'mantle' => 'Mantle (MNT)',
            'opbnb' => 'opBNB (OBNB)',
            'zkfair' => 'ZKFair (ZKF)',
            'blast' => 'Blast (BLAST)',
            'manta' => 'Manta Pacific (MANTA)',
            'berachain' => 'Berachain (BERA)',
            'abstract' => 'Abstract (Arcblock)',
            'hashkey' => 'Hashkey Chain (HSK)',
            'sonic' => 'Sonic (SONIC)',
            'story' => 'Story (STORY)',
            'own' => 'Own Blockchain',
            'other' => 'Other',
        ];

        return view('admin.dexscreener.add', compact('pageTitle', 'blockchains'));
    }

    /**
     * Store a new token and fetch its data from DexScreener
     */
    public function store(Request $request)
    {
        $request->validate([
            'chain_id' => 'required|string',
            'token_address' => 'required|string',
            'token_name' => 'required|string|max:255',
            'token_symbol' => 'required|string|max:20',
            'image' => 'nullable|image|mimes:jpg,jpeg,png',
        ]);

        // Check if token already exists
        $existingToken = DexscreenerToken::where('chain_id', $request->chain_id)
            ->where('token_address', $request->token_address)
            ->first();

        if ($existingToken) {
            $notify[] = ['error', 'This token already exists in the system'];
            return back()->withNotify($notify)->withInput();
        }

        try {
            // First, fetch the token details from DexScreener
            $tokenData = $this->dexscreenerService->fetchTokenDetails($request->chain_id, $request->token_address);

            // Handle image upload if provided
            $imageUrl = null;
            if ($request->hasFile('image')) {
                try {
                    $imageUrl = fileUploader($request->image, getFilePath('coinLogos'), getFileSize('coinLogos'));
                } catch (\Exception $exp) {
                    $notify[] = ['error', 'Image could not be uploaded'];
                    return back()->withNotify($notify)->withInput();
                }
            }

            // Get current timestamp
            $now = now();

            if ($tokenData) {
                // Create the token with all data
                $token = DexscreenerToken::create([
                    'chain_id' => $request->chain_id,
                    'token_address' => $request->token_address,
                    'token_name' => $request->token_name,
                    'token_symbol' => $request->token_symbol,
                    'price_usd' => (string)($tokenData['price_usd'] ?? null),
                    'price_change_24h' => (string)($tokenData['price_change_24h'] ?? null),
                    'price_change_5m' => (string)($tokenData['price_change_5m'] ?? null),
                    'price_change_1h' => (string)($tokenData['price_change_1h'] ?? null),
                    'price_change_6h' => (string)($tokenData['price_change_6h'] ?? null),
                    'volume_24h' => $tokenData['volume_24h'] ?? null,
                    'market_cap' => $tokenData['market_cap'] ?? null,
                    'liquidity_usd' => $tokenData['liquidity_usd'] ?? null,
                    'txn_24h' => $tokenData['txn_24h'] ?? null,
                    'token_age' => $tokenData['token_age'] ?? null,
                    'metadata' => $tokenData['metadata'] ?? null,
                    'pair_address' => $tokenData['pair_address'] ?? null,
                    'dex_id' => $tokenData['dex_id'] ?? null,
                    'image_url' => $imageUrl ?? $tokenData['image_url'] ?? null,
                    'created_at' => $now,
                    'updated_at' => $now,
                ]);

                $notify[] = ['success', 'Token added successfully with market data'];
            } else {
                // Create token with just the basic info
                $token = DexscreenerToken::create([
                    'chain_id' => $request->chain_id,
                    'token_address' => $request->token_address,
                    'token_name' => $request->token_name,
                    'token_symbol' => $request->token_symbol,
                    'image_url' => $imageUrl,
                    'created_at' => $now,
                    'updated_at' => $now,
                ]);

                $notify[] = ['warning', 'Token added successfully but could not fetch market data. Please try refreshing data later.'];
            }

            // Clear all token-related caches
            Cache::forget('dexscreener_tokens');
            Cache::forget('admin_tokens');
            Cache::forget('admin_tokens_active');
            Cache::forget('admin_tokens_all');

        } catch (\Exception $e) {
            $notify[] = ['error', 'Error adding token: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }

        return redirect()->route('admin.dexscreener.index')->withNotify($notify);
    }

    /**
     * Show form to edit an existing token
     */
    public function edit($id)
    {
        $token = DexscreenerToken::findOrFail($id);
        $pageTitle = 'Edit Token: ' . $token->token_name;

        // Comprehensive list of blockchains to choose from (matching user submission options)
        $blockchains = [
            'ethereum' => 'Ethereum (ETH)',
            'bsc' => 'Binance Smart Chain (BSC)',
            'polygon' => 'Polygon (MATIC)',
            'solana' => 'Solana (SOL)',
            'avalanche' => 'Avalanche (AVAX)',
            'tron' => 'Tron (TRX)',
            'cardano' => 'Cardano (ADA)',
            'polkadot' => 'Polkadot (DOT)',
            'arbitrum' => 'Arbitrum (ARB)',
            'optimism' => 'Optimism (OP)',
            'base' => 'Base (ETH on Base)',
            'fantom' => 'Fantom (FTM)',
            'cronos' => 'Cronos (CRO)',
            'algorand' => 'Algorand (ALGO)',
            'cosmos' => 'Cosmos (ATOM)',
            'near' => 'Near Protocol (NEAR)',
            'aptos' => 'Aptos (APT)',
            'sui' => 'Sui (SUI)',
            'hedera' => 'Hedera (HBAR)',
            'xrp' => 'XRP Ledger (XRP)',
            'stellar' => 'Stellar (XLM)',
            'harmony' => 'Harmony (ONE)',
            'moonbeam' => 'Moonbeam (GLMR)',
            'gnosis' => 'Gnosis Chain (GNO)',
            'zilliqa' => 'Zilliqa (ZIL)',
            'zksync' => 'zkSync Era (ZKS)',
            'linea' => 'Linea Mainnet (LINEA)',
            'scroll' => 'Scroll (SCROLL)',
            'heco' => 'HECO (HT)',
            'ethw' => 'ETHW (ETHW)',
            'kcc' => 'KCC (KCS)',
            'fon' => 'FON (FON)',
            'mantle' => 'Mantle (MNT)',
            'opbnb' => 'opBNB (OBNB)',
            'zkfair' => 'ZKFair (ZKF)',
            'blast' => 'Blast (BLAST)',
            'manta' => 'Manta Pacific (MANTA)',
            'berachain' => 'Berachain (BERA)',
            'abstract' => 'Abstract (Arcblock)',
            'hashkey' => 'Hashkey Chain (HSK)',
            'sonic' => 'Sonic (SONIC)',
            'story' => 'Story (STORY)',
            'own' => 'Own Blockchain',
            'other' => 'Other',
        ];

        // Check if this is a presale token
        $isPresale = strpos($token->token_address, 'presale_') === 0;

        // Get the corresponding SubmitCoin record for presale tokens
        $submitCoin = null;
        if ($isPresale) {
            $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token->token_address)
                ->orderBy('created_at', 'desc')
                ->first();
        }

        return view('admin.dexscreener.edit', compact('pageTitle', 'token', 'blockchains', 'isPresale', 'submitCoin'));
    }

    /**
     * Update an existing token and fetch its data from DexScreener if needed
     */
    public function update(Request $request, $id)
    {
        $token = DexscreenerToken::findOrFail($id);

        $request->validate([
            'chain_id' => 'required|string',
            'token_address' => 'required|string',
            'token_name' => 'required|string|max:255',
            'token_symbol' => 'required|string|max:20',
        ]);

        // Check if this is a presale or fair launch token being updated with a real contract address
        $isPresale = strpos($token->token_address, 'presale_') === 0;
        $isFairLaunch = strpos($token->token_address, 'fairlaunch_') === 0;
        $isUpdatingToken = ($isPresale || $isFairLaunch) && $request->token_address !== $token->token_address;

        // If updating a presale or fair launch token to a real token, check if the new address already exists
        if ($isUpdatingToken) {
            $existingToken = DexscreenerToken::where('chain_id', $request->chain_id)
                ->where('token_address', $request->token_address)
                ->where('id', '!=', $token->id)
                ->first();

            if ($existingToken) {
                $notify[] = ['error', 'This token address already exists in the system'];
                return back()->withNotify($notify)->withInput();
            }
        }

        try {
            // Start a database transaction to ensure all updates are atomic
            DB::beginTransaction();

            // If updating a presale token with a real contract address, fetch data from DexScreener
            if ($isUpdatingToken) {
                // First, fetch the token details from DexScreener
                $tokenData = $this->dexscreenerService->fetchTokenDetails($request->chain_id, $request->token_address);

                // Store the old token address and chain ID before updating
                $oldTokenAddress = $token->token_address;
                $oldChainId = $token->chain_id;

                // Prepare token data for update but don't save yet
                $newTokenData = [
                    'chain_id' => $request->chain_id,
                    'token_address' => $request->token_address,
                    'token_name' => $request->token_name,
                    'token_symbol' => $request->token_symbol
                ];

                // Add market data if available
                if ($tokenData) {
                    $newTokenData['price_usd'] = (string)($tokenData['price_usd'] ?? null);
                    $newTokenData['price_change_24h'] = (string)($tokenData['price_change_24h'] ?? null);
                    $newTokenData['price_change_5m'] = (string)($tokenData['price_change_5m'] ?? null);
                    $newTokenData['price_change_1h'] = (string)($tokenData['price_change_1h'] ?? null);
                    $newTokenData['price_change_6h'] = (string)($tokenData['price_change_6h'] ?? null);
                    $newTokenData['volume_24h'] = $tokenData['volume_24h'] ?? null;
                    $newTokenData['market_cap'] = $tokenData['market_cap'] ?? null;
                    $newTokenData['liquidity_usd'] = $tokenData['liquidity_usd'] ?? null;
                    $newTokenData['txn_24h'] = $tokenData['txn_24h'] ?? null;
                    $newTokenData['token_age'] = $tokenData['token_age'] ?? null;
                    $newTokenData['metadata'] = $tokenData['metadata'] ?? null;
                    $newTokenData['pair_address'] = $tokenData['pair_address'] ?? null;
                    $newTokenData['dex_id'] = $tokenData['dex_id'] ?? null;
                    $newTokenData['image_url'] = $tokenData['image_url'] ?? $token->image_url;
                }

                // First update all related records using the old token address

                // Update any token promotions to use the new token address
                $tokenPromotions = TokenPromotion::where('chain_id', $oldChainId)
                    ->where('token_address', $oldTokenAddress)
                    ->get();

                foreach ($tokenPromotions as $promotion) {
                    $promotion->token_address = $request->token_address;
                    $promotion->chain_id = $request->chain_id;
                    $promotion->save();
                }

                // Update any token votes to use the new token address
                $tokenVotes = TokenVote::where('chain_id', $oldChainId)
                    ->where('token_address', $oldTokenAddress)
                    ->get();

                foreach ($tokenVotes as $vote) {
                    $vote->token_address = $request->token_address;
                    $vote->chain_id = $request->chain_id;
                    $vote->save();
                }

                // Update any token watchlist entries to use the new token address
                $watchlistEntries = TokenWatchlist::where('chain_id', $oldChainId)
                    ->where('token_address', $oldTokenAddress)
                    ->get();

                foreach ($watchlistEntries as $entry) {
                    $entry->token_address = $request->token_address;
                    $entry->chain_id = $request->chain_id;
                    $entry->save();
                }

                // Update any social links to use the new token address
                $socialLink = SocialLink::where('token_chain_id', $oldChainId)
                    ->where('token_address', $oldTokenAddress)
                    ->first();

                if ($socialLink) {
                    $socialLink->token_address = $request->token_address;
                    $socialLink->token_chain_id = $request->chain_id;
                    $socialLink->save();
                }

                // Also update the submit_coins record if it exists
                $submitCoin = SubmitCoin::where('contract_address', $oldTokenAddress)
                    ->first();

                if ($submitCoin) {
                    $submitCoin->contract_address = $request->token_address;
                    $submitCoin->blockchain = $request->chain_id;
                    $submitCoin->name = $request->token_name;
                    $submitCoin->symbol = $request->token_symbol;

                    // If we have price data from DexScreener, update the presale and fair launch flags
                    if ($tokenData && isset($tokenData['price_usd']) && $tokenData['price_usd'] > 0) {
                        $submitCoin->is_presale = 0; // No longer a presale token
                        $submitCoin->is_fair_launch = 0; // No longer a fair launch token
                    }

                    $submitCoin->save();
                }

                // Finally, update the token itself
                foreach ($newTokenData as $key => $value) {
                    $token->$key = $value;
                }
                $token->save();

                $notify[] = ['success', 'Presale token updated with contract address and market data'];
            } else {
                // Regular token update
                // Store the old token address and chain ID before updating
                $oldTokenAddress = $token->token_address;
                $oldChainId = $token->chain_id;

                // Update any token promotions to use the new token address
                $tokenPromotions = TokenPromotion::where('chain_id', $oldChainId)
                    ->where('token_address', $oldTokenAddress)
                    ->get();

                foreach ($tokenPromotions as $promotion) {
                    $promotion->token_address = $request->token_address;
                    $promotion->chain_id = $request->chain_id;
                    $promotion->save();
                }

                // Update any token votes to use the new token address
                $tokenVotes = TokenVote::where('chain_id', $oldChainId)
                    ->where('token_address', $oldTokenAddress)
                    ->get();

                foreach ($tokenVotes as $vote) {
                    $vote->token_address = $request->token_address;
                    $vote->chain_id = $request->chain_id;
                    $vote->save();
                }

                // Update any token watchlist entries to use the new token address
                $watchlistEntries = TokenWatchlist::where('chain_id', $oldChainId)
                    ->where('token_address', $oldTokenAddress)
                    ->get();

                foreach ($watchlistEntries as $entry) {
                    $entry->token_address = $request->token_address;
                    $entry->chain_id = $request->chain_id;
                    $entry->save();
                }

                // Update any social links to use the new token address
                $socialLink = SocialLink::where('token_chain_id', $oldChainId)
                    ->where('token_address', $oldTokenAddress)
                    ->first();

                if ($socialLink) {
                    $socialLink->token_address = $request->token_address;
                    $socialLink->token_chain_id = $request->chain_id;
                    $socialLink->save();
                }

                // Finally, update the token itself
                $token->chain_id = $request->chain_id;
                $token->token_address = $request->token_address;
                $token->token_name = $request->token_name;
                $token->token_symbol = $request->token_symbol;
                $token->save();

                $notify[] = ['success', 'Token updated successfully'];
            }

            // Clear all token-related caches
            Cache::forget('dexscreener_tokens');
            Cache::forget('admin_tokens');
            Cache::forget('admin_tokens_active');
            Cache::forget('admin_tokens_all');

            // Commit the transaction
            DB::commit();

        } catch (\Exception $e) {
            // Rollback the transaction in case of error
            DB::rollBack();

            $notify[] = ['error', 'Error updating token: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }

        return redirect()->route('admin.dexscreener.index')->withNotify($notify);
    }
}