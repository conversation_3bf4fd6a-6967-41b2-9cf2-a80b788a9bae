/**
 * Token Table Sorting Functionality
 *
 * This script adds the ability to sort token tables by clicking on column headers.
 * It supports both ascending and descending sorting for various data types.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sorting for all token tables
    initTokenTableSorting('.trending-tokens-table');
    initTokenTableSorting('.promoted-coins-table');
});

/**
 * Initialize sorting functionality for a specific table
 * @param {string} tableSelector - CSS selector for the table
 */
function initTokenTableSorting(tableSelector) {
    const table = document.querySelector(tableSelector);
    if (!table) return;

    const headers = table.querySelectorAll('thead th');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Store original order of rows for reset
    const originalOrder = [...rows];

    // Track current sort state
    const sortState = {
        column: null,
        ascending: true
    };

    // Add click event listeners to headers
    headers.forEach((header, index) => {
        // Skip the first column (#), the Coin column, and the Votes column as they shouldn't be sortable
        if (index === 0 || index === 1 || header.textContent.trim() === 'Votes') return;

        // Add sortable class and cursor style
        header.classList.add('sortable');

        // Add data attribute for column name
        const columnName = header.textContent.trim().toLowerCase();
        header.setAttribute('data-column', columnName);

        // Add sort indicators
        const sortIndicator = document.createElement('span');
        sortIndicator.className = 'sort-indicator';
        sortIndicator.innerHTML = ' ';
        header.appendChild(sortIndicator);

        // Add click event
        header.addEventListener('click', () => {
            // Update sort indicators for all headers
            headers.forEach(h => {
                if (h.classList.contains('sortable')) {
                    h.querySelector('.sort-indicator').innerHTML = ' ';
                    h.classList.remove('sorting-asc', 'sorting-desc');
                }
            });

            // Toggle sort direction if clicking the same column
            if (sortState.column === columnName) {
                sortState.ascending = !sortState.ascending;
            } else {
                sortState.column = columnName;
                sortState.ascending = true;
            }

            // Update the clicked header's sort indicator
            const indicator = sortState.ascending ? '↑' : '↓';
            header.querySelector('.sort-indicator').innerHTML = indicator;
            header.classList.add(sortState.ascending ? 'sorting-asc' : 'sorting-desc');

            // Sort the rows
            sortTableByColumn(table, index, sortState.ascending);
        });
    });
}

/**
 * Sort table rows by a specific column
 * @param {HTMLElement} table - The table element
 * @param {number} columnIndex - Index of the column to sort by
 * @param {boolean} ascending - Sort direction (true for ascending, false for descending)
 */
function sortTableByColumn(table, columnIndex, ascending) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Sort the rows
    const sortedRows = rows.sort((rowA, rowB) => {
        // Get cell values
        const cellA = getCellValue(rowA, columnIndex);
        const cellB = getCellValue(rowB, columnIndex);

        // Compare based on data type
        let comparison = 0;

        // Handle special cases
        if (cellA === 'N/A' && cellB !== 'N/A') return ascending ? 1 : -1;
        if (cellA !== 'N/A' && cellB === 'N/A') return ascending ? -1 : 1;
        if (cellA === 'N/A' && cellB === 'N/A') return 0;
        if (cellA === '-' && cellB !== '-') return ascending ? 1 : -1;
        if (cellA !== '-' && cellB === '-') return ascending ? -1 : 1;
        if (cellA === '-' && cellB === '-') return 0;

        // Handle presale case
        if (cellA === 'Presale' && cellB !== 'Presale') return ascending ? -1 : 1;
        if (cellA !== 'Presale' && cellB === 'Presale') return ascending ? 1 : -1;
        if (cellA === 'Presale' && cellB === 'Presale') return 0;

        // Handle different data types
        if (columnIndex === 3) { // Price column
            // Extract numeric value from price string (remove $ and commas)
            const priceA = parseFloat(cellA.replace(/[$,]/g, '')) || 0;
            const priceB = parseFloat(cellB.replace(/[$,]/g, '')) || 0;
            comparison = priceA - priceB;
        } else if (columnIndex === 4) { // Age column
            // Parse age values (e.g., "15m", "2h", "3d", "10 days")
            comparison = compareAgeValues(cellA, cellB);
        } else if ([6, 12, 13].includes(columnIndex)) { // Volume, LP, MCap columns
            // Extract numeric value from formatted amounts (e.g., $2.54M)
            const valueA = parseFormattedAmount(cellA);
            const valueB = parseFormattedAmount(cellB);
            comparison = valueA - valueB;
        } else if ([7, 8, 9, 10].includes(columnIndex)) { // Percentage columns (5m, 1h, 6h, 24h)
            // Extract percentage values
            const percentA = parsePercentage(cellA);
            const percentB = parsePercentage(cellB);
            comparison = percentA - percentB;
        } else if (columnIndex === 5) { // TXN column
            // Parse transaction counts
            const txnA = parseInt(cellA.replace(/,/g, '')) || 0;
            const txnB = parseInt(cellB.replace(/,/g, '')) || 0;
            comparison = txnA - txnB;
        } else if (columnIndex === 13) { // Votes column
            // Extract vote count
            const voteA = parseInt(cellA.replace(/[^0-9]/g, '')) || 0;
            const voteB = parseInt(cellB.replace(/[^0-9]/g, '')) || 0;
            comparison = voteA - voteB;
        } else {
            // Default string comparison
            comparison = cellA.localeCompare(cellB);
        }

        return ascending ? comparison : -comparison;
    });

    // Remove existing rows
    rows.forEach(row => row.remove());

    // Append sorted rows
    sortedRows.forEach(row => tbody.appendChild(row));
}

/**
 * Get the text value of a cell
 * @param {HTMLElement} row - The table row
 * @param {number} columnIndex - Index of the column
 * @returns {string} - The text content of the cell
 */
function getCellValue(row, columnIndex) {
    const cell = row.querySelector(`td:nth-child(${columnIndex + 1})`);
    return cell ? cell.textContent.trim() : '';
}

/**
 * Compare age values like "15m", "2h", "3d", "10 days"
 * @param {string} ageA - First age value
 * @param {string} ageB - Second age value
 * @returns {number} - Comparison result
 */
function compareAgeValues(ageA, ageB) {
    // Convert age to seconds for comparison
    const secondsA = ageToSeconds(ageA);
    const secondsB = ageToSeconds(ageB);
    return secondsA - secondsB;
}

/**
 * Convert age string to seconds
 * @param {string} age - Age string (e.g., "15m", "2h", "3d", "10 days")
 * @returns {number} - Age in seconds
 */
function ageToSeconds(age) {
    if (age === '-' || age === 'N/A') return 0;

    // Handle "X days" format
    if (age.includes('days') || age.includes('day')) {
        return parseInt(age) * 86400;
    }

    // Handle short formats (e.g., 15m, 2h, 3d)
    const value = parseInt(age);
    if (isNaN(value)) return 0;

    if (age.includes('s')) return value;
    if (age.includes('m')) return value * 60;
    if (age.includes('h')) return value * 3600;
    if (age.includes('d')) return value * 86400;

    return 0;
}

/**
 * Parse formatted amount strings like "$2.54M", "$10.5K"
 * @param {string} amount - Formatted amount string
 * @returns {number} - Numeric value
 */
function parseFormattedAmount(amount) {
    if (amount === '-' || amount === 'N/A') return 0;

    // Remove $ and commas
    amount = amount.replace(/[$,]/g, '');

    // Handle suffixes
    const value = parseFloat(amount);
    if (isNaN(value)) return 0;

    if (amount.includes('K')) return value * 1000;
    if (amount.includes('M')) return value * 1000000;
    if (amount.includes('B')) return value * 1000000000;

    return value;
}

/**
 * Parse percentage values like "+2.54%", "-1.2%"
 * @param {string} percentage - Percentage string
 * @returns {number} - Numeric value
 */
function parsePercentage(percentage) {
    if (percentage === '-' || percentage === 'N/A') return 0;

    // Remove % and + signs
    percentage = percentage.replace(/[%+]/g, '');

    return parseFloat(percentage) || 0;
}
