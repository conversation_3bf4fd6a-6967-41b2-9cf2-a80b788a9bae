@php
    $content = getContent('login.content', true);
@endphp
@extends($activeTemplate . 'layouts.app')
@section('panel')
    @include($activeTemplate . 'partials.header')

    <section class="account section-bg login-account-custom" style="padding-top: 30px !important; padding-bottom: 30px !important; margin-top: 0 !important; margin-bottom: 0 !important;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-7 mx-auto">
                    <div class="contact-form">
                        <!-- Global Banner (inside card) -->
                        <div class="login-banner-container mb-3">
                            <x-ad position="global_banner" />
                        </div>

                        <h3 class="account-title pb-3 text-center"> {{ __(@$content->data_values->title) }} </h3>
                        <form class="verify-gcaptcha" method="POST" action="{{ route('user.login') }}" autocomplete="off">
                            @csrf
                            <div class="form-group">
                                <label for="username" class="form-label">@lang('Username') <span class="text-danger">*</span></label>
                                <input class="form--control" name="username" type="text" value="{{ old('username') }}" placeholder="@lang('Username')">
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">@lang('Password') <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input class="form--control" id="your-password" name="password" type="password" placeholder="@lang('Password')" required>
                                    <div class="password-show-hide fas fa-eye toggle-password" id="#your-password"></div>
                                </div>
                            </div>

                            <x-captcha />

                            <div class="form-group">
                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                    <div class="form--check mb-0">
                                        <input class="form-check-input" id="rem-me" name="remember" type="checkbox">
                                        <label class="form-check-label mb-0" for="rem-me">@lang('Remember Me')</label>
                                    </div>
                                    <div>
                                        <a class="checkbox__forgot-pass text--base" href="{{ route('user.password.request') }}">@lang('Forgot Password')</a>
                                    </div>
                                </div>
                            </div>

                            <button class="btn--base w-100" type="submit"> @lang('Login') </button>
                        </form>

                        @if (gs('registration'))
                            <div class="mt-3">
                                <p>@lang('Don\'t Have An Account?') <a class="text--base" href="{{ route('user.register') }}">@lang('Register')</a></p>
                            </div>
                        @endif
                        @if (@gs('socialite_credentials')->linkedin->status || @gs('socialite_credentials')->facebook->status == Status::ENABLE || @gs('socialite_credentials')->google->status == Status::ENABLE)
                            @include($activeTemplate . 'partials.social_login')
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    @include($activeTemplate . 'partials.footer')
@endsection

@push('style')
    <style>
        /* Global Banner Styles */
        .global-banner-container {
            background-color: var(--section-bg);
            padding: 15px 0;
        }

        /* Login Banner Styles */
        .login-banner-container {
            width: 100%;
            text-align: center;
            overflow: hidden;
        }

        .account {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 85vh;
        }

        .login-account-custom {
            padding: 30px 0 !important;
            margin: 0 !important;
            min-height: auto !important;
        }
    </style>
@endpush

@push('script')
    <script>
        "use strict";

        function submitUserForm() {
            var response = grecaptcha.getResponse();
            if (response.length == 0) {
                document.getElementById('g-recaptcha-error').innerHTML =
                    `<span style="color:red;">@lang('Captcha field is required.')</span>`;
                return false;
            }

            return true;
        }

        function verifyCaptcha() {
            document.getElementById('g-recaptcha-error').innerHTML = '';
        }
    </script>
@endpush
