@extends($activeTemplate . 'layouts.app')
@section('panel')
    @if(gs('notification_status'))
        <div class="sitewide-notification {{ gs('notification_url') ? 'has-link' : '' }}">
            <div class="container">
                <div class="notification-content">
                    <span>📢 {{ gs('notification_message') }}</span>
                    @if(gs('notification_url'))
                        <a href="{{ gs('notification_url') }}" target="_blank" class="notification-btn">{{ gs('notification_button_text') ?? 'Learn More' }}</a>
                    @endif
                </div>
            </div>
        </div>
    @endif
    @include($activeTemplate . 'partials.header')

    @if(!request()->routeIs('home'))
    <!-- Global Banner (in dashboard) -->
    <div class="global-banner-container">
        <div class="container">
            <x-ad position="global_banner" />
        </div>
    </div>
    @endif

    <div class="dashboard py-100 section-bg">
        <div class="container">
            <div class="row">
                <div class="col-xl-3 col-lg-4 pe-xl-4 mb-4 mb-lg-0">
                    @include($activeTemplate . 'partials.sidenav')
                </div>
                <div class="col-xl-9 col-lg-8 position-relative">
                    <div class="sidenav-bar d-lg-none d-block">
                        <span class="sidenav-bar__icon">
                            <i class="las la-sliders-h"></i>
                        </span>
                    </div>
                    <div class="dashboard-body">
                        @yield('content')
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include($activeTemplate . 'partials.footer')
@endsection

@push('style')
<style>
    /* Global Banner Styles */
    .global-banner-container {
        background-color: var(--section-bg);
        padding: 15px 0;
    }

    /* Sitewide Notification Styles */
    .sitewide-notification {
        background-color: #BE8400;
        color: #fff;
        padding: 10px 0;
        text-align: center;
        font-weight: 500;
    }
    .sitewide-notification .notification-content {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
    }
    .sitewide-notification a {
        color: #fff;
        text-decoration: none;
    }
    .sitewide-notification.has-link a:hover {
        text-decoration: none;
    }
    .notification-btn {
        background-color: #1C2631;
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.3s;
    }
    .notification-btn:hover {
        background-color: #2A3642;
    }

    /* Responsive styles for sitewide notification */
    @media (min-width: 768px) and (max-width: 1024px) {
        .sitewide-notification .notification-content {
            gap: 10px;
        }
        .notification-btn {
            padding: 5px 15px;
            font-size: 13px;
            min-width: 110px;
            text-align: center;
        }
    }

    @media (max-width: 767px) {
        .sitewide-notification .notification-content {
            flex-direction: column;
            gap: 10px;
            padding: 5px 0;
        }
        .notification-btn {
            padding: 5px 15px;
            font-size: 13px;
            display: inline-block;
            margin-bottom: 5px;
            min-width: 120px;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .sitewide-notification {
            padding: 8px 0;
        }
        .notification-btn {
            padding: 4px 12px;
            font-size: 12px;
            width: auto;
            min-width: 100px;
        }
    }

    /* Specific tablet device styles */
    @media (width: 768px) and (height: 1024px),
           (width: 820px) and (height: 1180px),
           (width: 912px) and (height: 1368px),
           (width: 853px) and (height: 1280px),
           (width: 1024px) and (height: 1366px) {
        .notification-btn {
            padding: 6px 16px;
            font-size: 14px;
            min-width: 120px;
            text-align: center;
        }
    }

    /* Responsive dashboard layout */
    @media (max-width: 1024px) {
        .dashboard.py-100 {
            padding-top: 60px;
            padding-bottom: 60px;
        }

        .dashboard-body {
            padding-top: 15px;
            width: 100%;
            overflow-x: hidden;
        }

        .dashboard .container {
            width: 100%;
            max-width: 100%;
        }

        .dashboard .row {
            margin-left: 0;
            margin-right: 0;
        }
    }

    @media (max-width: 991px) {
        .dashboard.py-100 {
            padding-top: 50px;
            padding-bottom: 50px;
        }

        .dashboard-body {
            padding-left: 10px;
            padding-right: 10px;
        }
    }

    @media (max-width: 767px) {
        .dashboard.py-100 {
            padding-top: 40px;
            padding-bottom: 40px;
        }

        .dashboard .container {
            padding-left: 10px;
            padding-right: 10px;
        }
    }

    @media (max-width: 575px) {
        .dashboard.py-100 {
            padding-top: 30px;
            padding-bottom: 30px;
        }

        .dashboard .container {
            padding-left: 5px;
            padding-right: 5px;
        }

        .dashboard-body {
            padding-left: 5px;
            padding-right: 5px;
        }
    }

    /* Specific iPad adjustments */
    @media (width: 1024px) and (height: 1366px),
           (width: 1024px) and (height: 600px),
           (width: 768px) and (height: 1024px) {
        .dashboard.py-100 {
            padding-top: 50px;
            padding-bottom: 50px;
        }

        .dashboard-body {
            width: 100%;
            overflow-x: hidden;
        }
    }

    /* Fix for horizontal scrolling */
    .dashboard-body {
        overflow-x: hidden;
    }

    /* Ensure the sidebar toggle is visible and properly positioned */
    .sidenav-bar {
        position: absolute;
        top: -37px;
        left: 15px;
        z-index: 10;
    }

    @media (max-width: 575px) {
        .sidenav-bar {
            top: -30px;
            left: 10px;
        }
    }
</style>
@endpush
