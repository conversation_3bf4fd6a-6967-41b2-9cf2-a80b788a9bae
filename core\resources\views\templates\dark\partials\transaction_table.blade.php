<table class="table--responsive--md table--responsive--lg table--responsive--xl table">
    <thead>
        <tr>
            <th scope="col">@lang('Transaction No.')</th>
            <th scope="col">@lang('Transacted')</th>
            <th scope="col">@lang('Amount')</th>
            <th scope="col">@lang('Post Balance')</th>
            <th scope="col">@lang('Detail')</th>
        </tr>
    </thead>
    <tbody>
        @forelse($transactions as $trx)
            <tr>
                <td data-label="@lang('Transaction No.')">
                    <strong>{{ $trx->trx }}</strong>
                </td>

                <td data-label="@lang('Transacted')">
                    {{ showDateTime($trx->created_at) }}<br>{{ diffForHumans($trx->created_at) }}
                </td>

                <td data-label="@lang('Amount')" class="budget">
                    <span class="fw-bold @if ($trx->trx_type == '+') text--success @else text-danger @endif">
                        {{ $trx->trx_type }} {{ showAmount($trx->amount, 8, exceptZeros: true, currencyFormat: false) }} {{ strtoupper($trx->currency) }}
                    </span>
                </td>

                <td data-label="@lang('Post Balance')" class="budget">
                    {{ showAmount($trx->post_balance, 8, exceptZeros: true, currencyFormat: false) }} {{ __(strtoupper($trx->currency)) }}
                </td>

                <td data-label="@lang('Detail')">{{ __($trx->details) }}</td>
            </tr>
        @empty
            <tr>
                <td class="text-muted text-center" colspan="100%" data-label="">{{ __($emptyMessage) }}</td>
            </tr>
        @endforelse
    </tbody>
</table>

<style>
    @media (max-width: 1024px) {
        .table--responsive--md tbody tr td::before,
        .table--responsive--lg tbody tr td::before,
        .table--responsive--xl tbody tr td::before {
            content: attr(data-label);
            font-weight: 500;
            margin-right: 10px;
            display: inline-block;
            min-width: 120px;
            text-align: left;
        }

        .table--responsive--md tbody tr td,
        .table--responsive--lg tbody tr td,
        .table--responsive--xl tbody tr td {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            padding: 10px 15px;
        }

        .table--responsive--md tbody tr td.text-center,
        .table--responsive--lg tbody tr td.text-center,
        .table--responsive--xl tbody tr td.text-center {
            justify-content: center;
            text-align: center !important;
        }

        /* Fix for transaction date display */
        .table--responsive--md tbody tr td[data-label="@lang('Transacted')"],
        .table--responsive--lg tbody tr td[data-label="@lang('Transacted')"],
        .table--responsive--xl tbody tr td[data-label="@lang('Transacted')"] {
            white-space: normal;
        }

        /* Fix for transaction amount display */
        .table--responsive--md tbody tr td[data-label="@lang('Amount')"],
        .table--responsive--lg tbody tr td[data-label="@lang('Amount')"],
        .table--responsive--xl tbody tr td[data-label="@lang('Amount')"],
        .table--responsive--md tbody tr td[data-label="@lang('Post Balance')"],
        .table--responsive--lg tbody tr td[data-label="@lang('Post Balance')"],
        .table--responsive--xl tbody tr td[data-label="@lang('Post Balance')"] {
            word-break: break-word;
        }

        /* Fix for transaction details display */
        .table--responsive--md tbody tr td[data-label="@lang('Detail')"],
        .table--responsive--lg tbody tr td[data-label="@lang('Detail')"],
        .table--responsive--xl tbody tr td[data-label="@lang('Detail')"] {
            word-break: break-word;
        }
    }

    @media (max-width: 767px) {
        .table--responsive--md tbody tr td::before,
        .table--responsive--lg tbody tr td::before,
        .table--responsive--xl tbody tr td::before {
            min-width: 100px;
        }
    }

    @media (max-width: 575px) {
        .table--responsive--md tbody tr td,
        .table--responsive--lg tbody tr td,
        .table--responsive--xl tbody tr td {
            padding: 8px 10px;
            flex-direction: column;
            align-items: flex-start;
        }

        .table--responsive--md tbody tr td::before,
        .table--responsive--lg tbody tr td::before,
        .table--responsive--xl tbody tr td::before {
            margin-bottom: 5px;
            margin-right: 0;
            min-width: auto;
            font-size: 13px;
        }

        .table--responsive--md tbody tr td,
        .table--responsive--lg tbody tr td,
        .table--responsive--xl tbody tr td {
            font-size: 13px;
        }
    }

    @media (max-width: 375px) {
        .table--responsive--md tbody tr td,
        .table--responsive--lg tbody tr td,
        .table--responsive--xl tbody tr td {
            padding: 6px 8px;
            font-size: 12px;
        }

        .table--responsive--md tbody tr td::before,
        .table--responsive--lg tbody tr td::before,
        .table--responsive--xl tbody tr td::before {
            font-size: 12px;
            margin-bottom: 4px;
        }
    }

    /* Specific iPad adjustments */
    @media (width: 1024px) and (height: 1366px),
           (width: 1024px) and (height: 600px),
           (width: 768px) and (height: 1024px) {
        .table--responsive--md tbody tr td,
        .table--responsive--lg tbody tr td,
        .table--responsive--xl tbody tr td {
            padding: 12px 15px;
        }

        .table--responsive--md tbody tr td::before,
        .table--responsive--lg tbody tr td::before,
        .table--responsive--xl tbody tr td::before {
            min-width: 130px;
        }
    }

    /* Specific tablet device styles for all orientations */
    @media only screen and (min-width: 768px) and (max-width: 1024px) {
        .table--responsive--md tbody tr td,
        .table--responsive--lg tbody tr td,
        .table--responsive--xl tbody tr td {
            padding: 12px 15px;
        }

        .table--responsive--md tbody tr td::before,
        .table--responsive--lg tbody tr td::before,
        .table--responsive--xl tbody tr td::before {
            min-width: 120px;
        }
    }
</style>
