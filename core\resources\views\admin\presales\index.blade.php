@extends('admin.layouts.app')
@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('Title')</th>
                                <th>@lang('Token')</th>
                                <th>@lang('Start Date')</th>
                                <th>@lang('End Date')</th>
                                <th>@lang('Price')</th>
                                <th>@lang('Next Price')</th>
                                <th>@lang('Quantity')</th>
                                <th>@lang('Sold')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Position')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($presales as $presale)
                                <tr>
                                    <td>{{ $presale->title }}</td>
                                    <td>{{ $presale->token_name }} ({{ $presale->token_symbol }})</td>
                                    <td>{{ $presale->start_date ? $presale->start_date->format('Y-m-d') : 'N/A' }}</td>
                                    <td>{{ $presale->end_date ? $presale->end_date->format('Y-m-d') : 'N/A' }}</td>
                                    <td>{{ $presale->price ? showAmount($presale->price) : 'N/A' }}</td>
                                    <td>{{ $presale->next_price ? showAmount($presale->next_price) : 'N/A' }}</td>
                                    <td>{{ $presale->quantity ?: 'N/A' }}</td>
                                    <td>{{ $presale->sold ? showAmount($presale->sold) : '0' }}</td>
                                    <td>
                                        @if(!$presale->is_active)
                                            <span class="badge badge--danger">@lang('Inactive')</span>
                                        @else
                                            @php
                                                $status = $presale->getStatus();
                                                $badgeClass = 'badge--success';

                                                if ($status == 'Upcoming') {
                                                    $badgeClass = 'badge--warning';
                                                } elseif ($status == 'Ended') {
                                                    $badgeClass = 'badge--dark';
                                                }
                                            @endphp
                                            <span class="badge {{ $badgeClass }}">@lang($status)</span>
                                        @endif
                                    </td>
                                    <td>{{ $presale->position }}</td>
                                    <td>
                                        <div class="button--group">
                                            <a href="{{ route('admin.presales.edit', $presale->id) }}" class="btn btn-sm btn--primary">
                                                <i class="las la-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn--danger confirmationBtn" data-action="{{ route('admin.presales.delete', $presale->id) }}" data-question="@lang('Are you sure you want to delete this presale?')">
                                                <i class="las la-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage ?? 'No presales found') }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($presales->hasPages())
                <div class="card-footer py-4">
                    {{ paginateLinks($presales) }}
                </div>
            @endif
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-lg-12">
        <div class="d-flex justify-content-end">
            <a href="{{ route('admin.presales.create') }}" class="btn btn--primary">@lang('Add New Presale')</a>
        </div>
    </div>
</div>

<x-confirmation-modal />
@endsection

@push('breadcrumb-plugins')
<a href="{{ route('admin.presales.create') }}" class="btn btn-sm btn--primary">
    <i class="las la-plus"></i> @lang('Add New')
</a>
@endpush
