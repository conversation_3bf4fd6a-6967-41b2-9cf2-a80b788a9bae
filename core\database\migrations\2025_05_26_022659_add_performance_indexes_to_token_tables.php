<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to token_votes table for performance optimization
        Schema::table('token_votes', function (Blueprint $table) {
            // Composite index for vote counting by chain_id and token_address
            $table->index(['chain_id', 'token_address', 'is_negative'], 'idx_token_votes_chain_address_negative');
            
            // Index for filtering by vote date
            $table->index(['voted_at'], 'idx_token_votes_voted_at');
            
            // Composite index for user vote checking
            $table->index(['chain_id', 'token_address', 'ip_address', 'voted_at'], 'idx_token_votes_user_check');
        });

        // Add indexes to dexscreener_tokens table for performance optimization
        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            // Index for filtering verified tokens
            $table->index(['is_verified'], 'idx_dexscreener_tokens_verified');
            
            // Index for filtering by submitted_by_user_id (admin tokens)
            $table->index(['submitted_by_user_id'], 'idx_dexscreener_tokens_submitted_by');
            
            // Composite index for chain_id and token_address lookups
            $table->index(['chain_id', 'token_address'], 'idx_dexscreener_tokens_chain_address');
            
            // Index for ordering by creation date
            $table->index(['created_at'], 'idx_dexscreener_tokens_created_at');
            
            // Index for trend_votes for sorting
            $table->index(['trend_votes'], 'idx_dexscreener_tokens_trend_votes');
        });

        // Add indexes to token_promotions table for performance optimization
        Schema::table('token_promotions', function (Blueprint $table) {
            // Index for filtering active promotions
            $table->index(['end_date'], 'idx_token_promotions_end_date');
            
            // Composite index for joining with tokens
            $table->index(['chain_id', 'token_address'], 'idx_token_promotions_chain_address');
            
            // Index for ordering by end_date
            $table->index(['end_date', 'created_at'], 'idx_token_promotions_end_created');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('token_votes', function (Blueprint $table) {
            $table->dropIndex('idx_token_votes_chain_address_negative');
            $table->dropIndex('idx_token_votes_voted_at');
            $table->dropIndex('idx_token_votes_user_check');
        });

        Schema::table('dexscreener_tokens', function (Blueprint $table) {
            $table->dropIndex('idx_dexscreener_tokens_verified');
            $table->dropIndex('idx_dexscreener_tokens_submitted_by');
            $table->dropIndex('idx_dexscreener_tokens_chain_address');
            $table->dropIndex('idx_dexscreener_tokens_created_at');
            $table->dropIndex('idx_dexscreener_tokens_trend_votes');
        });

        Schema::table('token_promotions', function (Blueprint $table) {
            $table->dropIndex('idx_token_promotions_end_date');
            $table->dropIndex('idx_token_promotions_chain_address');
            $table->dropIndex('idx_token_promotions_end_created');
        });
    }
};
