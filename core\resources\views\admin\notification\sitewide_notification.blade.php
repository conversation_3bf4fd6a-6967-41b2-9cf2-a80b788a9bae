@extends('admin.layouts.app')
@section('panel')
@push('topBar')
  @include('admin.notification.top_bar')
@endpush
<div class="row">

    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">@lang('Sitewide Notification')</h5>
            </div>
            <form action="{{ route('admin.setting.notification.sitewide.update') }}" method="POST">
                @csrf
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>@lang('Notification Message')</label>
                                <textarea name="notification_message" rows="3" class="form-control">{{ gs('notification_message') }}</textarea>
                                <small class="text-muted">@lang('This message will be displayed at the top of all pages when activated.')</small>
                            </div>
                        </div>
                        <div class="col-md-12 mt-3">
                            <div class="form-group">
                                <label>@lang('Notification URL') <small class="text-muted">(@lang('Optional'))</small></label>
                                <input type="url" class="form-control" name="notification_url" value="{{ gs('notification_url') }}" placeholder="https://example.com">
                                <small class="text-muted">@lang('If provided, the notification will be clickable and redirect to this URL.')</small>
                            </div>
                        </div>
                        <div class="col-md-12 mt-3">
                            <div class="form-group">
                                <label>@lang('Button Text') <small class="text-muted">(@lang('Optional'))</small></label>
                                <input type="text" class="form-control" name="notification_button_text" value="{{ gs('notification_button_text') ?? 'Learn More' }}" placeholder="Learn More">
                                <small class="text-muted">@lang('Custom text for the notification button. Default is "Learn More".')</small>
                            </div>
                        </div>
                        <div class="col-md-12 mt-3">
                            <div class="form-group text-center">
                                <label>@lang('Status')</label>
                                <div class="d-flex justify-content-center">
                                    <input type="checkbox" data-width="20%" data-size="mini" data-onstyle="-success" data-offstyle="-danger" data-bs-toggle="toggle" data-height="15" data-on="@lang('Enable')" data-off="@lang('Disable')" name="notification_status" @if(gs('notification_status')) checked @endif>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <button type="submit" class="btn btn--primary w-20 h-25 mx-auto" style="width: 20%;">@lang('Submit')</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
