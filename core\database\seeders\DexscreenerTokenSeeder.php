<?php

namespace Database\Seeders;

use App\Models\DexscreenerToken;
use Illuminate\Database\Seeder;

class DexscreenerTokenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Sample tokens data
        $tokens = [
            [
                'chain_id' => 'ethereum',
                'token_address' => '******************************************',
                'token_name' => 'Wrapped Ether',
                'token_symbol' => 'WETH',
                'price_usd' => '3450.25',
                'price_change_24h' => '2.5',
                'volume_24h' => 1234567.89,
                'market_cap' => 45678901234.56,
                'liquidity_usd' => 3456789012.34,
                'metadata' => json_encode([
                    'boosts' => ['active' => 4550],
                    'dexId' => 'uniswap',
                    'url' => 'https://dexscreener.com/ethereum/******************************************',
                ]),
                'pair_address' => '******************************************',
                'dex_id' => 'uniswap',
                'image_url' => 'https://assets.coingecko.com/coins/images/2518/small/weth.png',
            ],
            [
                'chain_id' => 'ethereum',
                'token_address' => '******************************************',
                'token_name' => 'USD Coin',
                'token_symbol' => 'USDC',
                'price_usd' => '1.00',
                'price_change_24h' => '0.01',
                'volume_24h' => 987654.32,
                'market_cap' => 43210987654.32,
                'liquidity_usd' => 2109876543.21,
                'metadata' => json_encode([
                    'boosts' => ['active' => 3495],
                    'dexId' => 'uniswap',
                    'url' => 'https://dexscreener.com/ethereum/******************************************',
                ]),
                'pair_address' => '******************************************',
                'dex_id' => 'uniswap',
                'image_url' => 'https://assets.coingecko.com/coins/images/6319/small/usdc.png',
            ],
            [
                'chain_id' => 'binance-smart-chain',
                'token_address' => '******************************************',
                'token_name' => 'Wrapped BNB',
                'token_symbol' => 'WBNB',
                'price_usd' => '570.12',
                'price_change_24h' => '-1.2',
                'volume_24h' => 7654321.98,
                'market_cap' => 9876543210.12,
                'liquidity_usd' => 1234567890.12,
                'metadata' => json_encode([
                    'boosts' => ['active' => 1898],
                    'dexId' => 'pancakeswap',
                    'url' => 'https://dexscreener.com/bsc/******************************************',
                ]),
                'pair_address' => '0x16b9a82891338f9ba80e2d6970fdda79d1eb0dae',
                'dex_id' => 'pancakeswap',
                'image_url' => 'https://assets.coingecko.com/coins/images/12591/small/binance-coin-logo.png',
            ],
        ];

        // Insert tokens
        foreach ($tokens as $token) {
            DexscreenerToken::updateOrCreate(
                [
                    'chain_id' => $token['chain_id'],
                    'token_address' => $token['token_address'],
                ],
                $token
            );
        }
        
        $this->command->info('Dexscreener tokens seeded successfully.');
    }
} 