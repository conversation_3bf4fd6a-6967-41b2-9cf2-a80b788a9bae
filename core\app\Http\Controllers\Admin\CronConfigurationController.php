<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\CronJob;
use App\Models\CronJobLog;
use App\Models\CronSchedule;
use Carbon\Carbon;
use Illuminate\Http\Request;

class CronConfigurationController extends Controller
{
    public function cronJobs()
    {
        $pageTitle = 'Cron Jobs';
        $crons     = CronJob::with('schedule', 'logs')->get();
        $schedules = CronSchedule::active()->orderBy('interval')->get();
        return view('admin.cron.index', compact('pageTitle', 'crons', 'schedules'));
    }

    public function cronJobStore(Request $request)
    {
        $request->validate([
            'name'             => 'required',
            'next_run'         => 'required',
            'interval'         => 'required|integer|min:60',
            'url'              => 'nullable|url',
            'is_crypto_fetch'  => 'nullable',
        ]);

        // Find or create a schedule with the given interval
        $schedule = CronSchedule::where('interval', $request->interval)->first();
        
        if (!$schedule) {
            // Create a new schedule with the given interval
            $schedule = new CronSchedule();
            $schedule->name = $request->interval . ' seconds';
            $schedule->interval = $request->interval;
            $schedule->status = Status::ENABLE;
            $schedule->save();
        }

        $cronJob                   = new CronJob();
        $cronJob->name             = $request->name;
        $cronJob->alias            = titleToKey($request->name);
        $cronJob->next_run         = Carbon::parse($request->next_run)->toDateTimeString();
        $cronJob->cron_schedule_id = $schedule->id;
        
        // Special handling for crypto fetch job
        if ($request->is_crypto_fetch) {
            $cronJob->is_default = Status::YES;
            $cronJob->action = ['App\Http\Controllers\CronController', 'fetchCryptoData'];
            $cronJob->alias = 'fetch_crypto_data'; // Force the correct alias
        } else {
            $request->validate(['url' => 'required|url']); // URL is required for non-crypto jobs
            $cronJob->url = $request->url;
            $cronJob->is_default = Status::NO;
        }
        
        $cronJob->is_running = Status::YES;
        $cronJob->save();

        $notify[] = ['success', 'Cron job created successfully'];
        return to_route('admin.cron.index')->withNotify($notify);
    }

    public function cronJobUpdate(Request $request)
    {
        $request->validate([
            'id'               => 'required|integer',
            'name'             => 'required',
            'next_run'         => 'required',
            'interval'         => 'required|integer|min:60',
        ]);

        $cronJob = CronJob::findOrFail($request->id);
        $cronJob->name = $request->name;

        // Find or create a schedule with the given interval
        $schedule = CronSchedule::where('interval', $request->interval)->first();
        
        if (!$schedule) {
            // Create a new schedule with the given interval
            $schedule = new CronSchedule();
            $schedule->name = $request->interval . ' seconds';
            $schedule->interval = $request->interval;
            $schedule->status = Status::ENABLE;
            $schedule->save();
        }

        if (!$cronJob->is_default) {
            $request->validate(['url' => 'required|url']);
            $cronJob->url = $request->url;
            $cronJob->alias = titleToKey($request->name);
        }

        $cronJob->next_run = Carbon::parse($request->next_run)->toDateTimeString();
        $cronJob->cron_schedule_id = $schedule->id;
        $cronJob->save();

        $notify[] = ['success', 'Cron job update successfully'];
        return back()->withNotify($notify);
    }

    public function CronJobDelete($id)
    {
        $cronJob = CronJob::where('is_default', 0)->where('id', $id)->firstOrFail();
        $cronJob->delete();

        CronJobLog::where('cron_job_id', $id)->delete();

        $notify[] = ['success', 'Cron job deleted successfully'];
        return back()->withNotify($notify);
    }

    public function schedule()
    {
        $pageTitle = 'Cron Schedules';
        $schedules = CronSchedule::paginate(getPaginate());
        return view('admin.cron.schedule', compact('pageTitle', 'schedules'));
    }

    public function scheduleStore(Request $request)
    {
        $request->validate([
            'name'     => 'required',
            'interval' => 'required|integer|gt:0',
        ]);

        $id = $request->id ?? 0;

        if ($id) {
            $schedule = CronSchedule::findOrFail($id);
            $message  = "Cron schedule updated successfully";
        } else {
            $schedule = new CronSchedule();
            $message  = "Cron schedule added successfully";
        }
        $schedule->name     = $request->name;
        $schedule->interval = $request->interval;
        $schedule->status   = Status::ENABLE;
        $schedule->save();

        $notify[] = ['success', $message];
        return back()->withNotify($notify);
    }

    public function scheduleStatus($id)
    {
        return CronSchedule::changeStatus($id);
    }

    public function schedulePause($id)
    {

        return CronJob::changeStatus($id, 'is_running');
    }

    public function scheduleLogs($id)
    {
        $cronJob   = CronJob::findOrFail($id);
        $pageTitle = $cronJob->name . " Cron Schedule Logs";
        $logs      = CronJobLog::where('cron_job_id', $cronJob->id)->orderBy('id', 'DESC')->paginate(getPaginate());
        return view('admin.cron.logs', compact('pageTitle', 'logs', 'cronJob'));
    }

    public function scheduleLogResolved($id)
    {
        $log        = CronJobLog::findOrFail($id);
        $log->error = null;
        $log->save();

        $notify[] = ['success', 'Cron log resolved successfully'];
        return back()->withNotify($notify);
    }

    public function logFlush($id)
    {
        $cronJob = CronJob::findOrFail($id);
        CronJobLog::where('cron_job_id', $cronJob->id)->delete();

        $notify[] = ['success', 'All logs flushed successfully'];
        return back()->withNotify($notify);
    }

    /**
     * Run a specific cron job immediately from the cron job manager
     */
    public function runNow($id)
    {
        \Log::info('Running cron job from admin panel, ID: ' . $id);
        $cronJob = CronJob::findOrFail($id);
        
        // Create a log entry
        $cronLog = new CronJobLog();
        $cronLog->cron_job_id = $cronJob->id;
        $cronLog->start_at = now();
        
        $success = false;
        
        // Special handling for fetch_crypto_data
        if ($cronJob->alias == 'fetch_crypto_data') {
            try {
                \Log::info('Direct execution of fetch_crypto_data from admin panel');
                // Directly execute the method to avoid issues with controller call
                $controller = new \App\Http\Controllers\CronController();
                $result = $controller->fetchCryptoData();
                
                $success = $result !== false;
                if (!$success) {
                    $cronLog->error = 'Method returned false';
                }
            } catch (\Exception $e) {
                $cronLog->error = $e->getMessage();
                \Log::error('Exception when running fetch_crypto_data: ' . $e->getMessage());
                \Log::error($e->getTraceAsString());
            }
        }
        // Standard execution for other cron jobs
        else if ($cronJob->is_default) {
            try {
                $controller = new $cronJob->action[0];
                $method = $cronJob->action[1];
                $controller->$method();
                $success = true;
            } catch (\Exception $e) {
                $cronLog->error = $e->getMessage();
            }
        } else {
            try {
                \App\Lib\CurlRequest::curlContent($cronJob->url);
                $success = true;
            } catch (\Exception $e) {
                $cronLog->error = $e->getMessage();
            }
        }
        
        // Update cron job information
        $cronJob->last_run = now();
        $cronJob->next_run = now()->addSeconds($cronJob->schedule->interval);
        $cronJob->save();
        
        // Update log information
        $cronLog->end_at = now();
        $startTime = Carbon::parse($cronLog->start_at);
        $endTime = Carbon::parse($cronLog->end_at);
        $diffInSeconds = $startTime->diffInSeconds($endTime);
        $cronLog->duration = $diffInSeconds;
        $cronLog->save();
        
        // Update general settings last_cron time
        $general = gs();
        $general->last_cron = now();
        $general->save();
        
        if ($success) {
            $notify[] = ['success', $cronJob->name . ' executed successfully'];
        } else {
            $notify[] = ['error', $cronJob->name . ' execution failed. Check logs for details.'];
        }
        
        return back()->withNotify($notify);
    }
}
