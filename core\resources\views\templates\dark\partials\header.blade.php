@php
    use Illuminate\Support\Facades\Schema;

    $contactCaption = getContent('contact_us.content', true);

    // Check if column exists before filtering
    if (Schema::hasColumn('pages', 'show_in_header')) {
        $pages = App\Models\Page::where('is_default', Status::NO)
            ->where('tempname', $activeTemplate)
            ->where('show_in_header', 1)
            ->get();
    } else {
        $pages = App\Models\Page::where('is_default', Status::NO)
            ->where('tempname', $activeTemplate)
            ->get();
    }

    if (gs('multi_language')) {
        $language = getLanguages();
        $default = getLanguages(true);
    }
@endphp

<!-- Header Ads -->
<div class="header-ads-container">
    <div class="container">
        <div class="row">
            <div class="col-6 pe-2">
                <x-ad position="header_left" />
            </div>
            <div class="col-6 ps-2">
                <x-ad position="header_right" />
            </div>
        </div>
    </div>
</div>

<header class="header-bottom" id="header">
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light">
            <a class="navbar-brand logo" href="{{ route('home') }}">
                <img src="{{ siteLogo() }}" alt="Logo">
            </a>
            <button class="navbar-toggler header-button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" type="button" aria-controls="navbarSupportedContent"
                aria-expanded="false" aria-label="Toggle navigation">
                <span id="hiddenNav"><i class="las la-bars"></i></span>
            </button>
            <div class="navbar-collapse collapse" id="navbarSupportedContent">
                <ul class="navbar-nav nav-menu m-auto">
                    <li class="nav-item"><a class="nav-link {{ menuActive('home') }}"
                            href="{{ route('home') }}">@lang('Home')</a></li>
                    @foreach ($pages as $item)
                        <li class="nav-item"><a class="nav-link {{ menuActive('pages', $item->slug) }}"
                                href="{{ route('pages', ['slug' => $item->slug]) }}">{{ __($item->name) }}</a></li>
                    @endforeach

                    <li class="nav-item"><a class="nav-link {{ menuActive('blog') }}"
                            href="{{ route('blog') }}">@lang('Articles')</a></li>
                    @auth
                    <li class="nav-item"><a class="nav-link {{ menuActive('user.submit.coin.form') }}"
                            href="{{ route('user.submit.coin.form') }}">@lang('Submit Coin')</a></li>
                    @endauth
                    @guest
                    <li class="nav-item"><a class="nav-link {{ menuActive('user.submit.coin.form') }}"
                            href="{{ route('user.login') }}?redirect={{ urlencode(route('user.submit.coin.form')) }}">@lang('Submit Coin')</a></li>
                    @endguest
                    @php $cclToken = \App\Models\CclToken::getData(); @endphp
                    @if($cclToken->is_enabled)
                    <li class="nav-item"><a class="nav-link {{ menuActive('ccl.token') }}"
                            href="{{ route('ccl.token') }}">Launchpad</a></li>
                    @endif
                    @auth
                    <li class="nav-item"><a class="nav-link {{ menuActive('contact') }}"
                            href="{{ route('contact') }}">@lang('Create Ticket')</a></li>
                    @endauth
                    @guest
                    <li class="nav-item"><a class="nav-link {{ menuActive('contact2') }}"
                            href="{{ route('contact2') }}">@lang('Contact')</a></li>
                    @endguest
                </ul>

                <div
                    class="langauge-registration d-flex flex-wrap flex-lg-nowrap align-items-center justify-content-between">
                    @if (gs('multi_language'))
                        <div class="language dropdown">
                            <button class="language-wrapper" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="language-content">
                                    <div class="language_flag">
                                        <img src="{{ getImage(getFilePath('language') . '/' . $default->image, getFileSize('language')) }}"
                                            alt="">
                                    </div>
                                    <p class="language_text_select">{{ __($default->name) }}</p>
                                </div>
                                <span class="collapse-icon"><i class="las la-angle-down"></i></span>
                            </button>
                            <div class="dropdown-menu langList_dropdow py-2" style="">
                                <ul class="langList">
                                    @foreach ($language->where('code', '!=', $default->code) as $lang)
                                        <li>
                                            <a href="{{ route('lang', $lang->code) }}" class="language-list">
                                                <div class="language_flag">
                                                    <img src="{{ getImage(getFilePath('language') . '/' . $lang->image, getFileSize('language')) }}"
                                                        alt="flag">
                                                </div>
                                                <p class="language_text">{{ __($lang->name) }}</p>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif

                    <div class="d-flex align-items-center gap-sm-0 user-btn-group flex-wrap gap-1">
                        @guest
                            @if (gs('registration'))
                                <a class="btn--base btn--sm ms-sm-3 register-btn ms-0 outline"
                                    href="{{ route('user.register') }}">@lang('Register')</a>
                            @endif
                            <a class="btn--base btn--sm ms-sm-3 ms-0"
                                href="{{ route('user.login') }}">@lang('Login')</a>
                        @else
                            @if (!request()->routeIs('user*') && !request()->routeIs('ticket*'))
                                <a class="btn--base btn--sm ms-sm-3 ms-0"
                                    href="{{ route('user.home') }}">@lang('Dashboard')</a>
                            @endif
                            <a class="btn btn--danger btn--sm ms-sm-3 ms-0"
                                href="{{ route('user.logout') }}">@lang('Logout')</a>
                        @endguest
                    </div>
                </div>
            </div>
        </nav>
    </div>
</header>

@push('style')
<style>
    .header-ads-container {
        padding: 10px 0;
    }

    .header-ads-container .ad-container {
        margin: 0;
        min-height: 100px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .header-ads-container .responsive-ad {
        height: auto;
        width: 100%;
        object-fit: contain;
    }

    @media (max-width: 1199px) {
        .header-ads-container .responsive-ad {
            max-width: 100%;
        }
    }

    @media (max-width: 767px) {
        .header-ads-container .col-6 {
            padding-left: 5px;
            padding-right: 5px;
        }
    }
</style>
@endpush
