<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\AdPosition;
use App\Rules\FileTypeValidate;
use Illuminate\Http\Request;

class AdPositionController extends Controller
{
    /**
     * Display a listing of ad positions.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $pageTitle = 'Manage Ad Positions';
        $adPositions = AdPosition::orderByRaw("CASE
            WHEN id = 1 THEN 1
            WHEN id = 2 THEN 2
            WHEN id = 6 THEN 3
            WHEN id = 7 THEN 4
            WHEN id = 3 THEN 5
            WHEN id = 4 THEN 6
            WHEN id = 5 THEN 7
            ELSE id + 100 END")->paginate(getPaginate());
        return view('admin.advertise.positions', compact('pageTitle', 'adPositions'));
    }

    /**
     * Store a newly created ad position.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:ad_positions,key' . ($request->id ? ',' . $request->id : ''),
            'size' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:255',
            'image' => ['nullable', 'image', new FileTypeValidate(['jpg', 'jpeg', 'png', 'gif'])],
            'status' => 'required|in:0,1',
        ]);

        if ($request->id) {
            $adPosition = AdPosition::findOrFail($request->id);
            $message = 'Ad position updated successfully';
        } else {
            $adPosition = new AdPosition();
            $message = 'Ad position created successfully';
        }

        $adPosition->name = $request->name;
        $adPosition->key = $request->key;
        $adPosition->size = $request->size;
        $adPosition->description = $request->description;
        $adPosition->status = $request->status;

        if ($request->hasFile('image')) {
            try {
                $old = $adPosition->image ?? null;
                $adPosition->image = fileUploader($request->image, getFilePath('ads_images'), null, $old);
            } catch (\Exception $exp) {
                $notify[] = ['error', 'Image could not be uploaded'];
                return back()->withNotify($notify);
            }
        }

        $adPosition->save();

        $notify[] = ['success', $message];
        return back()->withNotify($notify);
    }

    /**
     * Remove the ad image from the specified ad position.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function delete($id)
    {
        $adPosition = AdPosition::findOrFail($id);

        if ($adPosition->image) {
            fileManager()->removeFile(getFilePath('ads_images') . '/' . $adPosition->image);
            $adPosition->image = null;
            $adPosition->save();

            $notify[] = ['success', 'Ad image deleted successfully'];
        } else {
            $notify[] = ['info', 'No image to delete for this ad position'];
        }

        return back()->withNotify($notify);
    }
}
