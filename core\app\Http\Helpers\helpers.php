<?php

use App\Constants\Status;
use App\Lib\GoogleAuthenticator;
use App\Models\Extension;
use App\Models\Frontend;
use App\Models\GeneralSetting;
use Carbon\Carbon;
use App\Lib\Captcha;
use App\Lib\ClientInfo;
use App\Lib\CurlRequest;
use App\Lib\FileManager;
use App\Models\Language;
use App\Models\Referral;
use App\Models\ReferralLog;
use App\Models\Transaction;
use App\Notify\Notify;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Laramin\Utility\VugiChugi;

function systemDetails()
{
    // Modified to prevent update checking
    $system['name'] = 'cryptocoinlister';
    $system['version'] = '999.999';
    $system['build_version'] = '999.999';
    return $system;
}

function slug($string)
{
    return Str::slug($string);
}

function verificationCode($length)
{
    if ($length == 0) return 0;
    $min = pow(10, $length - 1);
    $max = (int) ($min - 1) . '9';
    return random_int($min, $max);
}

function getNumber($length = 8)
{
    $characters = '1234567890';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}


function activeTemplate($asset = false)
{
    $template = session('template') ?? gs('active_template');
    if ($asset) return 'assets/templates/' . $template . '/';
    return 'templates.' . $template . '.';
}

function activeTemplateName()
{
    $template = session('template') ?? gs('active_template');
    return $template;
}

function siteLogo($type = null)
{
    $name = $type ? "/logo_$type.png" : '/logo.png';
    return getImage(getFilePath('logo_icon') . $name);
}
function siteFavicon()
{
    return getImage(getFilePath('logo_icon') . '/favicon.png');
}

function loadReCaptcha()
{
    return Captcha::reCaptcha();
}

function loadCustomCaptcha($width = '100%', $height = 46, $bgColor = '#003')
{
    return Captcha::customCaptcha($width, $height, $bgColor);
}

function verifyCaptcha()
{
    return Captcha::verify();
}

function loadExtension($key)
{
    $extension = Extension::where('act', $key)->where('status', Status::ENABLE)->first();
    return $extension ? $extension->generateScript() : '';
}

function getTrx($length = 12)
{
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

function getAmount($amount, $length = 2)
{
    $amount = round($amount ?? 0, $length);
    return $amount + 0;
}

function showAmount($amount, $decimal = 2, $separate = true, $exceptZeros = false, $currencyFormat = true)
{
    $separator = '';
    if ($separate) {
        $separator = ',';
    }
    $printAmount = number_format($amount, $decimal, '.', $separator);
    if ($exceptZeros) {
        $exp = explode('.', $printAmount);
        if ($exp[1] * 1 == 0) {
            $printAmount = $exp[0];
        } else {
            $printAmount = rtrim($printAmount, '0');
        }
    }
    if ($currencyFormat) {
        if (gs('currency_format') == Status::CUR_BOTH) {
            return gs('cur_sym') . $printAmount . ' ' . __(gs('cur_text'));
        } elseif (gs('currency_format') == Status::CUR_TEXT) {
            return $printAmount . ' ' . __(gs('cur_text'));
        } else {
            return gs('cur_sym') . $printAmount;
        }
    }
    return $printAmount;
}


function removeElement($array, $value)
{
    return array_diff($array, (is_array($value) ? $value : array($value)));
}

function cryptoQR($wallet)
{
    return "https://api.qrserver.com/v1/create-qr-code/?data=$wallet&size=300x300&ecc=m";
}

function keyToTitle($text)
{
    return ucfirst(preg_replace("/[^A-Za-z0-9 ]/", ' ', $text));
}


function titleToKey($text)
{
    return strtolower(str_replace(' ', '_', $text));
}


function strLimit($title = null, $length = 10)
{
    return Str::limit($title, $length);
}


function getIpInfo()
{
    $ipInfo = ClientInfo::ipInfo();
    return $ipInfo;
}


function osBrowser()
{
    $osBrowser = ClientInfo::osBrowser();
    return $osBrowser;
}


function getTemplates()
{
    // Return empty JSON string
    return '{}';
}


function getPageSections($arr = false)
{
    $jsonUrl = resource_path('views/') . str_replace('.', '/', activeTemplate()) . 'sections.json';
    $sections = json_decode(file_get_contents($jsonUrl));
    if ($arr) {
        $sections = json_decode(file_get_contents($jsonUrl), true);
        ksort($sections);
    }
    return $sections;
}


function getImage($image, $size = null)
{
    $clean = '';
    if (file_exists($image) && is_file($image)) {
        return asset($image) . $clean;
    }
    if ($size) {
        return route('placeholder.image', $size);
    }
    return asset('assets/images/default.png');
}


function notify($user, $templateName, $shortCodes = null, $sendVia = null, $createLog = true, $pushImage = null)
{
    $globalShortCodes = [
        'site_name' => gs('site_name'),
        'site_currency' => gs('cur_text'),
        'currency_symbol' => gs('cur_sym'),
    ];

    if (gettype($user) == 'array') {
        $user = (object) $user;
    }

    $shortCodes = array_merge($shortCodes ?? [], $globalShortCodes);

    $notify = new Notify($sendVia);
    $notify->templateName = $templateName;
    $notify->shortCodes = $shortCodes;
    $notify->user = $user;
    $notify->createLog = $createLog;
    $notify->pushImage = $pushImage;
    $notify->userColumn = isset($user->id) ? $user->getForeignKey() : 'user_id';
    $notify->send();
}

function getPaginate($paginate = null)
{
    if (!$paginate) {
        $paginate = gs('paginate_number');
    }
    return $paginate;
}

function paginateLinks($data)
{
    return $data->appends(request()->all())->links();
}


function menuActive($routeName, $type = null, $param = null)
{
    if ($type == 3) $class = 'side-menu--open';
    elseif ($type == 2) $class = 'sidebar-submenu__open';
    else $class = 'active';

    if (is_array($routeName)) {
        foreach ($routeName as $key => $value) {
            if (request()->routeIs($value)) return $class;
        }
    } elseif (request()->routeIs($routeName)) {
        if ($param) {
            $routeParam = array_values(@request()->route()->parameters ?? []);
            if (strtolower(@$routeParam[0]) == strtolower($param)) return $class;
            else return;
        }
        return $class;
    }
}


function fileUploader($file, $location, $size = null, $old = null, $thumb = null, $filename = null)
{
    $fileManager = new FileManager($file);
    $fileManager->path = $location;
    $fileManager->size = $size;
    $fileManager->old = $old;
    $fileManager->thumb = $thumb;
    $fileManager->filename = $filename;
    $fileManager->upload();
    return $fileManager->filename;
}

function fileManager()
{
    return new FileManager();
}

function getFilePath($key)
{
    return fileManager()->$key()->path;
}

function getFileSize($key)
{
    return fileManager()->$key()->size;
}

function getFileExt($key)
{
    return fileManager()->$key()->extensions;
}

function diffForHumans($date)
{
    $lang = session()->get('lang');
    Carbon::setlocale($lang);
    return Carbon::parse($date)->diffForHumans();
}


function showDateTime($date, $format = 'Y-m-d h:i A')
{
    if (!$date) {
        return '-';
    }
    $lang = session()->get('lang');
    Carbon::setlocale($lang);
    return Carbon::parse($date)->translatedFormat($format);
}


function getContent($dataKeys, $singleQuery = false, $limit = null, $orderById = false)
{

    $templateName = activeTemplateName();
    if ($singleQuery) {
        $content = Frontend::where('tempname', $templateName)->where('data_keys', $dataKeys)->orderBy('id', 'desc')->first();
    } else {
        $article = Frontend::where('tempname', $templateName);
        $article->when($limit != null, function ($q) use ($limit) {
            return $q->limit($limit);
        });
        if ($orderById) {
            $content = $article->where('data_keys', $dataKeys)->orderBy('id')->get();
        } else {
            $content = $article->where('data_keys', $dataKeys)->orderBy('id', 'desc')->get();
        }
    }
    return $content;
}

function verifyG2fa($user, $code, $secret = null)
{
    $authenticator = new GoogleAuthenticator();
    if (!$secret) {
        $secret = $user->tsc;
    }
    $oneCode = $authenticator->getCode($secret);
    $userCode = $code;
    if ($oneCode == $userCode) {
        $user->tv = Status::YES;
        $user->save();
        return true;
    } else {
        return false;
    }
}


function urlPath($routeName, $routeParam = null)
{
    if ($routeParam == null) {
        $url = route($routeName);
    } else {
        $url = route($routeName, $routeParam);
    }
    $basePath = route('home');
    $path = str_replace($basePath, '', $url);
    return $path;
}


function showMobileNumber($number)
{
    $length = strlen($number);
    return substr_replace($number, '***', 2, $length - 4);
}

function showEmailAddress($email)
{
    $endPosition = strpos($email, '@') - 1;
    return substr_replace($email, '***', 1, $endPosition);
}


function getRealIP()
{
    $ip = $_SERVER["REMOTE_ADDR"];
    //Deep detect ip
    if (filter_var(@$_SERVER['HTTP_FORWARDED'], FILTER_VALIDATE_IP)) {
        $ip = $_SERVER['HTTP_FORWARDED'];
    }
    if (filter_var(@$_SERVER['HTTP_FORWARDED_FOR'], FILTER_VALIDATE_IP)) {
        $ip = $_SERVER['HTTP_FORWARDED_FOR'];
    }
    if (filter_var(@$_SERVER['HTTP_X_FORWARDED_FOR'], FILTER_VALIDATE_IP)) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    }
    if (filter_var(@$_SERVER['HTTP_CLIENT_IP'], FILTER_VALIDATE_IP)) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    }
    if (filter_var(@$_SERVER['HTTP_X_REAL_IP'], FILTER_VALIDATE_IP)) {
        $ip = $_SERVER['HTTP_X_REAL_IP'];
    }
    if (filter_var(@$_SERVER['HTTP_CF_CONNECTING_IP'], FILTER_VALIDATE_IP)) {
        $ip = $_SERVER['HTTP_CF_CONNECTING_IP'];
    }
    if ($ip == '::1') {
        $ip = '127.0.0.1';
    }

    return $ip;
}


function appendQuery($key, $value)
{
    return request()->fullUrlWithQuery([$key => $value]);
}

function dateSort($a, $b)
{
    return strtotime($a) - strtotime($b);
}

function dateSorting($arr)
{
    usort($arr, "dateSort");
    return $arr;
}

function gs($key = null)
{
    $general = Cache::get('GeneralSetting');
    if (!$general) {
        $general = GeneralSetting::first();
        Cache::put('GeneralSetting', $general);
    }
    if ($key) return @$general->$key;
    return $general;
}
function isImage($string)
{
    $allowedExtensions = array('jpg', 'jpeg', 'png', 'gif');
    $fileExtension = pathinfo($string, PATHINFO_EXTENSION);
    if (in_array($fileExtension, $allowedExtensions)) {
        return true;
    } else {
        return false;
    }
}

function isHtml($string)
{
    if (preg_match('/<.*?>/', $string)) {
        return true;
    } else {
        return false;
    }
}


function convertToReadableSize($size)
{
    preg_match('/^(\d+)([KMG])$/', $size, $matches);
    $size = (int)$matches[1];
    $unit = $matches[2];

    if ($unit == 'G') {
        return $size . 'GB';
    }

    if ($unit == 'M') {
        return $size . 'MB';
    }

    if ($unit == 'K') {
        return $size . 'KB';
    }

    return $size . $unit;
}


function frontendImage($sectionName, $image, $size = null, $seo = false)
{
    if ($seo) {
        return getImage('assets/images/frontend/' . $sectionName . '/seo/' . $image, $size);
    }

    return getImage('assets/images/frontend/' . $sectionName . '/' . $image, $size);
}

function totalPeriodInDay($time_limit, $type)
{
    if ($type == 0) {
        return $time_limit;
    } elseif ($type == 1) {
        return $time_limit * 30;
    } elseif ($type == 2) {
        return $time_limit * 365;
    }
}

function levelCommission($user, $amount, $trx)
{
    try {
        // More comprehensive logging for debugging
        \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION START', [
            'user_id' => $user->id,
            'username' => $user->username,
            'ref_by' => $user->ref_by,
            'amount' => $amount,
            'trx' => $trx,
            'referral_system_enabled' => gs()->referral_system,
            'memory_usage' => memory_get_usage(true),
            'time' => date('Y-m-d H:i:s')
        ]);

        // Early checks to prevent issues
        if (!$user || !$amount || !$trx) {
            \Illuminate\Support\Facades\Log::channel('daily')->error('REFERRAL COMMISSION ERROR: Missing required parameters', [
                'user' => $user ? true : false,
                'amount' => $amount,
                'trx' => $trx
            ]);
            return;
        }

        if (!$user->ref_by) {
            \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION SKIPPED: User has no referrer', [
                'user_id' => $user->id
            ]);
            return;
        }

        // Check if a commission has already been given for this user's purchase
        $existingCommission = ReferralLog::where('user_id', $user->id)
            ->first();

        if ($existingCommission) {
            \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION SKIPPED: Commission already given for this user', [
                'user_id' => $user->id
            ]);
            return;
        }

        $meUser       = $user;
        $i            = 1;
        $transactions = [];
        $refLog       = [];
        $referrals    = Referral::get();
        $level        = $referrals?->count() ?? 0;

        if ($level == 0) {
            \Illuminate\Support\Facades\Log::channel('daily')->error('REFERRAL COMMISSION ERROR: No referral levels defined', []);

            // Auto-create a default level if none exists
            $defaultReferral = new Referral();
            $defaultReferral->level = 1;
            $defaultReferral->percent = 10;
            $defaultReferral->save();

            // Refresh the referrals
            $referrals = Referral::get();
            $level = 1;

            \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION: Created default level', []);
        }

        \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION LEVELS', [
            'level_count' => $level,
            'referrals' => $referrals->toArray()
        ]);

        while ($i <= $level) {
            $me    = $meUser;
            $refer = $me->referrer;

            \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION LEVEL ' . $i, [
                'referrer_exists' => !is_null($refer),
                'referrer_id' => $refer ? $refer->id : null,
                'referrer_username' => $refer ? $refer->username : null
            ]);

            if (!$refer) {
                \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION: No referrer found for level ' . $i);
                break;
            }

            $commission = (clone $referrals)->where('level', $i)->first();

            \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION CHECK', [
                'level' => $i,
                'commission_exists' => !is_null($commission),
                'commission_percent' => $commission ? $commission->percent : null
            ]);

            if (!$commission) {
                \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION: No commission config for level ' . $i);
                break;
            }

            try {
                $commissionValue = ($amount * $commission->percent) / 100;
                // Do NOT add to user balance - referral commissions are tracked via referral logs
                // and can only be withdrawn through the referral withdrawal system

                \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION ADDED', [
                    'commission_value' => $commissionValue,
                    'referrer_id' => $refer->id,
                    'referrer_balance_unchanged' => true
                ]);

                $refLog[] = [
                    'user_id'    => $user->id,
                    'referee_id' => $refer->id,
                    'amount'     => $commissionValue,
                    'level'      => $i,
                    'percent'    => $commission->percent,
                    'trx'        => $trx,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // Create a transaction record for tracking purposes only
                // This doesn't affect the user's main balance
                $transactions[] = [
                    'user_id'      => $refer->id,
                    'amount'       => $commissionValue,
                    'post_balance' => $refer->balance, // Balance remains unchanged
                    'charge'       => 0,
                    'trx_type'     => '+',
                    'details'      => 'You have received referral commission from ' . $user->username,
                    'trx'          => $trx,
                    'remark'       => 'referral_commission',
                    'currency'     => gs()->cur_text,
                    'created_at'   => now(),
                    'updated_at'   => now(),
                ];

                notify($refer, 'REFERRAL_COMMISSION', [
                    'amount'       => showAmount($commissionValue, currencyFormat: false),
                    'post_balance' => showAmount($refer->balance, currencyFormat: false),
                    'trx'          => $trx,
                    'level'        => ordinal($i),
                ]);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::channel('daily')->error('REFERRAL COMMISSION ERROR in level ' . $i, [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }

            $meUser = $refer;
            $i++;
        }

        \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION RECORDS', [
            'transactions_count' => count($transactions),
            'reflog_count' => count($refLog)
        ]);

        if ($transactions) {
            try {
                Transaction::insert($transactions);
                \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION: Transactions inserted successfully');
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::channel('daily')->error('REFERRAL COMMISSION ERROR: Failed to insert transactions', [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
        }

        if ($refLog) {
            try {
                ReferralLog::insert($refLog);
                \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION: Referral logs inserted successfully');
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::channel('daily')->error('REFERRAL COMMISSION ERROR: Failed to insert referral logs', [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
        }

        \Illuminate\Support\Facades\Log::channel('daily')->info('REFERRAL COMMISSION END', [
            'user_id' => $user->id,
            'username' => $user->username,
            'memory_usage' => memory_get_usage(true),
            'time' => date('Y-m-d H:i:s')
        ]);
    } catch (\Exception $e) {
        \Illuminate\Support\Facades\Log::channel('daily')->error('REFERRAL COMMISSION FATAL ERROR', [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}

function ordinal($number)
{
    $ends = array('th', 'st', 'nd', 'rd', 'th', 'th', 'th', 'th', 'th', 'th');
    if ((($number % 100) >= 11) && (($number % 100) <= 13)) {
        return $number . 'th';
    } else {
        return $number . $ends[$number % 10];
    }
}

function getReferees($user, $maxLevel, $data = [], $depth = 1, $layer = 0)
{
    if ($user->allReferrals->count() > 0 && $maxLevel > 0) {
        foreach ($user->allReferrals as $under) {
            $i = 0;
            if ($i == 0) {
                $layer++;
            }
            $i++;

            $userData['id']       = $under->id;
            $userData['username'] = $under->username;
            $userData['fullname'] = $under->fullname;
            $userData['level']    = $depth;
            $data[]               = $userData;
            if ($under->allReferrals->count() > 0 && $layer < $maxLevel) {
                $data = getReferees($under, $maxLevel, $data, $depth + 1, $layer);
            }
        }
    }
    return $data;
}

function shortAddress($address, $length = 10)
{
    if (strlen($address) > $length * 2) {
        $maskedAddress = substr_replace($address, '****', $length, -$length);
        return $maskedAddress;
    } else {
        return $address;
    }
}


function getLanguages($local = false)
{
    $language = Language::all();
    if (!$local) return $language;

    $default = $language->where('code', session('lang'))->first();
    if ($default) return $default;

    return $language->where('is_default', Status::YES)->first();
}

/**
 * Format large numbers specifically for cryptocurrency display (K, M, B)
 *
 * @param float $amount Amount to format
 * @param int $decimals Number of decimal places
 * @return string Formatted amount
 */
function formatAmount($amount, $decimals = 2)
{
    if (!$amount) {
        return '0';
    }

    if ($amount >= 1000000000) {
        return round($amount / 1000000000, $decimals) . 'B';
    } elseif ($amount >= 1000000) {
        return round($amount / 1000000, $decimals) . 'M';
    } elseif ($amount >= 1000) {
        return round($amount / 1000, $decimals) . 'K';
    }
    return round($amount, $decimals);
}

/**
 * Get the block explorer URL for a given blockchain
 *
 * @param string $chainId The chain identifier
 * @param string|null $tokenAddress The token address (optional)
 * @return string|null The explorer URL or null if not found
 */
function getBlockExplorerUrl($chainId, $tokenAddress = null)
{
    // If token address is provided, check if there's a custom explorer URL for this token
    if ($tokenAddress) {
        // Try to find the token in the database
        $token = \App\Models\DexscreenerToken::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->first();

        if ($token) {
            // Check if there's a custom explorer URL in the metadata
            $metadata = $token->metadata;
            if ($metadata && isset($metadata->blockchain_explorer_url) && !empty($metadata->blockchain_explorer_url)) {
                $customUrl = $metadata->blockchain_explorer_url;

                // If the URL contains {CONTRACT_ADDRESS}, replace it with the actual token address
                if (strpos($customUrl, '{CONTRACT_ADDRESS}') !== false) {
                    return str_replace('{CONTRACT_ADDRESS}', $tokenAddress, $customUrl);
                }

                return $customUrl;
            }

            // Check if this token was submitted through the submit form and has a custom explorer URL
            $submitCoin = \App\Models\SubmitCoin::where('blockchain_explorer_url', '!=', '')
                ->where('blockchain_explorer_url', '!=', null)
                ->where('contract_address', $tokenAddress)
                ->first();

            if ($submitCoin && $submitCoin->blockchain_explorer_url) {
                $customUrl = $submitCoin->blockchain_explorer_url;

                // If the URL contains {CONTRACT_ADDRESS}, replace it with the actual token address
                if (strpos($customUrl, '{CONTRACT_ADDRESS}') !== false) {
                    return str_replace('{CONTRACT_ADDRESS}', $tokenAddress, $customUrl);
                }

                return $customUrl;
            }
        }
    }

    // Default explorers if no custom URL is found
    $explorers = [
        'ethereum' => 'https://etherscan.io',
        'bsc' => 'https://bscscan.com',
        'polygon' => 'https://polygonscan.com',
        'solana' => 'https://solscan.io',
        'avalanche' => 'https://snowtrace.io',
        'tron' => 'https://tronscan.org/#/searchResult',  // Special case handled in view
        'cardano' => 'https://cardanoscan.io',
        'polkadot' => 'https://polkadot.subscan.io',
        'arbitrum' => 'https://arbiscan.io',
        'optimism' => 'https://optimistic.etherscan.io',
        'base' => 'https://basescan.org',
        'fantom' => 'https://ftmscan.com',
        'cronos' => 'https://cronoscan.com',
        'algorand' => 'https://algoexplorer.io',
        'cosmos' => 'https://atomscan.com',
        'near' => 'https://explorer.near.org',
        'aptos' => 'https://explorer.aptoslabs.com',
        'sui' => 'https://explorer.sui.io',
        'hedera' => 'https://hashscan.io',
        'xrp' => 'https://xrpscan.com',
        'stellar' => 'https://stellarscan.io',
        'harmony' => 'https://explorer.harmony.one',
        'moonbeam' => 'https://moonscan.io',
        'gnosis' => 'https://gnosisscan.io',
        'zilliqa' => 'https://viewblock.io/zilliqa',
        'zksync' => 'https://explorer.zksync.io',
        'linea' => 'https://lineascan.build',
        'scroll' => 'https://scrollscan.com',
        'heco' => 'https://hecoinfo.com',
        'ethw' => 'https://mainnet.ethwscan.com',
        'kcc' => 'https://explorer.kcc.io',
        'mantle' => 'https://explorer.mantle.xyz',
        'opbnb' => 'https://opbnbscan.com',
        'zkfair' => 'https://scan.zkfair.io',
        'blast' => 'https://blastscan.io',
        'manta' => 'https://pacific-explorer.manta.network',
        'hashkey' => 'https://www.hashkeyscan.io',
    ];

    return $explorers[$chainId] ?? null;
}

/**
 * Get the chart URL for a given blockchain and token
 *
 * @param string $chainId The chain identifier
 * @param string $tokenAddress The token address
 * @return string The chart URL
 */
function getChartUrl($chainId, $tokenAddress)
{
    $charts = [
        'ethereum' => "https://dextools.io/app/ether/pair-explorer/",
        'bsc' => "https://poocoin.app/tokens/",
        'polygon' => "https://dextools.io/app/polygon/pair-explorer/",
        'arbitrum' => "https://dextools.io/app/arbitrum/pair-explorer/",
        'optimism' => "https://dextools.io/app/optimism/pair-explorer/",
        'avalanche' => "https://dextools.io/app/avalanche/pair-explorer/",
        'fantom' => "https://dextools.io/app/fantom/pair-explorer/",
        'cronos' => "https://dextools.io/app/cronos/pair-explorer/",
        'base' => "https://dextools.io/app/base/pair-explorer/",
        'solana' => "https://dexscreener.com/solana/",
    ];

    $baseUrl = $charts[$chainId] ?? "https://dexscreener.com/{$chainId}/";
    return $baseUrl . $tokenAddress;
}

/**
 * Get the DEX URL for a given blockchain and token
 *
 * @param string $chainId The chain identifier
 * @param string $tokenAddress The token address
 * @return string The DEX URL
 */
function getDexUrl($chainId, $tokenAddress)
{
    $dexes = [
        'ethereum' => "https://app.uniswap.org/#/swap?outputCurrency=",
        'bsc' => "https://pancakeswap.finance/swap?outputCurrency=",
        'polygon' => "https://quickswap.exchange/#/swap?outputCurrency=",
        'arbitrum' => "https://app.uniswap.org/#/swap?chain=arbitrum&outputCurrency=",
        'optimism' => "https://app.uniswap.org/#/swap?chain=optimism&outputCurrency=",
        'avalanche' => "https://traderjoexyz.com/trade?outputCurrency=",
        'fantom' => "https://spookyswap.finance/swap?outputCurrency=",
        'cronos' => "https://mm.finance/swap?outputCurrency=",
        'base' => "https://app.uniswap.org/#/swap?chain=base&outputCurrency=",
        'solana' => "https://raydium.io/swap/?inputCurrency=sol&outputCurrency=",
    ];

    $baseUrl = $dexes[$chainId] ?? "https://dexscreener.com/{$chainId}/";
    return $baseUrl . $tokenAddress;
}

/**
 * Format price with appropriate decimal places for cryptocurrency
 *
 * @param float $price The price to format
 * @param int $minDecimals Minimum decimal places
 * @param int $maxDecimals Maximum decimal places for small values
 * @return string Formatted price
 */
function formatPrice($price, $minDecimals = 2, $maxDecimals = 8)
{
    if (!$price) {
        return '0';
    }

    $price = (float) $price;

    if ($price < 0.01) {
        return number_format($price, $maxDecimals);
    }

    return number_format($price, $minDecimals);
}

/**
 * Format blockchain name for display
 *
 * @param string $chainId The chain identifier
 * @return string Formatted blockchain name
 */
function formatBlockchainName($chainId)
{
    if ($chainId === 'own') {
        return 'Own Blockchain';
    }

    return ucfirst($chainId);
}

/**
 * Format cap value (softcap/hardcap) for display
 * Converts values like "1000USDT" to "1K USDT"
 *
 * @param string $capValue The cap value to format
 * @return string Formatted cap value
 */
function formatCapValue($capValue)
{
    if (!$capValue || $capValue === '-') {
        return '-';
    }

    // Extract numeric part and currency part
    if (preg_match('/^(\d+)([A-Za-z]+)$/', $capValue, $matches)) {
        $number = (int)$matches[1];
        $currency = $matches[2];

        if ($number >= 1000000000) {
            return round($number / 1000000000, 2) . 'B ' . $currency;
        } elseif ($number >= 1000000) {
            return round($number / 1000000, 2) . 'M ' . $currency;
        } elseif ($number >= 1000) {
            return round($number / 1000, 2) . 'K ' . $currency;
        }

        return $number . ' ' . $currency;
    }

    // Try to extract number with commas or spaces and currency
    if (preg_match('/^([\d,\s]+)([A-Za-z]+)$/', $capValue, $matches)) {
        $numberStr = str_replace([',', ' '], '', $matches[1]);
        $number = (int)$numberStr;
        $currency = $matches[2];

        if ($number >= 1000000000) {
            return round($number / 1000000000, 2) . 'B ' . $currency;
        } elseif ($number >= 1000000) {
            return round($number / 1000000, 2) . 'M ' . $currency;
        } elseif ($number >= 1000) {
            return round($number / 1000, 2) . 'K ' . $currency;
        }

        return $number . ' ' . $currency;
    }

    // Try to extract just the number (for cases where there's no currency)
    if (is_numeric($capValue)) {
        $number = (int)$capValue;

        if ($number >= 1000000000) {
            return round($number / 1000000000, 2) . 'B';
        } elseif ($number >= 1000000) {
            return round($number / 1000000, 2) . 'M';
        } elseif ($number >= 1000) {
            return round($number / 1000, 2) . 'K';
        }

        return $number;
    }

    // If the format doesn't match any of the above, return the original value
    return $capValue;
}

/**
 * Get ad by position key
 *
 * @param string $key The position key
 * @return \App\Models\AdPosition|null The ad position or null if not found
 */
function getAd($key)
{
    return \App\Models\AdPosition::where('key', $key)
        ->where('status', \App\Constants\Status::ENABLE)
        ->first();
}
