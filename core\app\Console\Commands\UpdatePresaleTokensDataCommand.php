<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\DexscreenerService;

class UpdatePresaleTokensDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-presale-tokens-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates presale tokens with live price data from Dexscreener if applicable.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(DexscreenerService $dexscreenerService)
    {
        Log::info('==== UpdatePresaleTokensDataCommand: DEXSCREENER PRESALE TOKEN UPDATE STARTED ====');

        try {
            // Clear relevant token-related caches
            Cache::forget('dexscreener_tokens');
            Cache::forget('admin_tokens');
            Cache::forget('admin_tokens_active');
            Cache::forget('admin_tokens_all');
            Log::info('UpdatePresaleTokensDataCommand: Cleared token caches.');

            Log::info('UpdatePresaleTokensDataCommand: Checking for presale tokens with real contract addresses that might be live now...');
            
            $updatedCount = $dexscreenerService->updatePresaleTokensWithPriceData();

            Log::info('UpdatePresaleTokensDataCommand: Updated ' . $updatedCount . ' presale tokens with live data.');
            $this->info('Updated ' . $updatedCount . ' presale tokens with live data.');
            
            Log::info('==== UpdatePresaleTokensDataCommand: DEXSCREENER PRESALE TOKEN UPDATE COMPLETED ====');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            Log::error('UpdatePresaleTokensDataCommand: Error: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            $this->error('Error updating presale tokens: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
} 