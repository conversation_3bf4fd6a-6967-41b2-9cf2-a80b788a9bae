@extends($activeTemplate . 'layouts.master')
@section('content')
<div class="card custom--card">
    <div class="card-header">
        <h5 class="card-title">{{ $presale->title }}</h5>
    </div>
    <div class="card-body">
        <div class="gateway-card">
            <div class="row justify-content-center gy-sm-2 gy-2 deposit-row">
                <div class="col-lg-12">
                    <div class="token-sale-container">
                        <div class="token-sale-header mb-0">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="token-info-card compact-card">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="token-info-labels">
                                                    <div class="row">
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0">@lang('Start Date')</p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0">@lang('End Date')</p>
                                                        </div>
                                                        <div class="col-3 text-center">
                                                            <p class="label-text mb-0">@lang('Quantity')</p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="label-text mb-0">@lang('Price')</p>
                                                        </div>
                                                        <div class="col-3 text-center">
                                                            <p class="label-text mb-0">@lang('Status')</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="token-info-values mt-1">
                                                    <div class="row">
                                                        <div class="col-2 text-center">
                                                            <p class="value-text mb-0">{{ $presale->start_date ? $presale->start_date->format('F d, Y') : 'TBD' }}</p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="value-text mb-0">{{ $presale->end_date ? $presale->end_date->format('F d, Y') : 'TBD' }}</p>
                                                        </div>
                                                        <div class="col-3 text-center">
                                                            <p class="value-text mb-0">{{ $presale->quantity ?? '0' }}</p>
                                                        </div>
                                                        <div class="col-2 text-center">
                                                            <p class="value-text mb-0">{{ number_format($presale->price ?? 0, 8) }} USD</p>
                                                        </div>
                                                        <div class="col-3 text-center">
                                                            <span class="badge {{ $presale->getStatus() == 'Active' ? 'badge--primary' : ($presale->getStatus() == 'Ended' ? 'badge--success' : 'badge--warning') }}">
                                                                {{ $presale->getStatus() }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="token-info-sold mt-1">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <div class="deposit-info compact-info mb-1">
                                                                <div class="deposit-info__title">
                                                                    <p class="text mb-0">@lang('Progress')</p>
                                                                </div>
                                                                <div class="deposit-info__input">
                                                                    <p class="text mb-0">{{ number_format($presale->getProgressPercentage(), 2) }}%</p>
                                                                </div>
                                                            </div>
                                                            <div class="progress">
                                                                <div class="progress-bar" role="progressbar" style="width: {{ $presale->getProgressPercentage() }}%;" aria-valuenow="{{ $presale->getProgressPercentage() }}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card custom--card mt-4">
    <div class="card-header">
        <h5 class="card-title">@lang('Buy') {{ $presale->token_symbol }} @lang('Tokens')</h5>
    </div>
    <div class="card-body">
        <div class="gateway-card">
            <div class="row justify-content-center gy-sm-2 gy-2 deposit-row">
                <div class="col-lg-12">
                    <div class="token-purchase-form">
                        <div class="row">
                            <div class="col-md-12">
                                <form action="#" method="POST">
                                    @csrf
                                    <input type="hidden" name="presale_id" value="{{ $presale->id }}">
                                    <div class="deposit-info mb-2">
                                        <div class="deposit-info__title">
                                            <p class="text mb-0">@lang('Amount (USD)')<span class="text-danger">*</span></p>
                                        </div>
                                        <div class="deposit-info__input">
                                            <div class="input-group amount-input-group">
                                                <input type="number" class="form-control amount" id="amount" name="amount" placeholder="0.00" min="1" step="0.01" required>
                                                <span class="input-group-text">USD</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="deposit-info mb-2">
                                        <div class="deposit-info__title">
                                            <p class="text mb-0">@lang('Tokens to Receive')</p>
                                        </div>
                                        <div class="deposit-info__input">
                                            <div class="input-group amount-input-group">
                                                <input type="text" class="form-control" id="tokens" name="tokens" placeholder="0" readonly>
                                                <span class="input-group-text">{{ $presale->token_symbol }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="deposit-info total-amount">
                                        <div class="deposit-info__title">
                                            <p class="text mb-0">@lang('Total Amount')</p>
                                        </div>
                                        <div class="deposit-info__input">
                                            <p class="text mb-0 final-amount">$0.00</p>
                                        </div>
                                    </div>

                                    <div class="mt-2 text-end">
                                        <button type="submit" class="btn btn--base">@lang('Buy Tokens')</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
    /* Custom card styles for first card */
    .card.custom--card:first-of-type .card-header {
        padding: 10px 20px;
    }

    .card.custom--card:first-of-type .card-body {
        padding: 5px;
    }

    /* Custom card styles for second card */
    .card.custom--card:nth-of-type(2) .card-header {
        padding: 10px 20px;
    }

    .card.custom--card:nth-of-type(2) .card-body {
        padding: 5px;
    }

    /* Gateway card styles */
    .gateway-card {
        background-color: transparent;
        padding: 10px;
    }

    /* Token info card styles */
    .token-info-card {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    /* Compact card styles */
    .compact-card {
        padding: 8px;
        margin-bottom: 5px;
    }

    /* Token info sold styles */
    .token-info-sold {
        margin-top: 5px !important;
    }

    .label-text {
        color: #7c97bb;
        font-size: 13px;
        font-weight: 500;
    }

    .value-text {
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Deposit info styles */
    .deposit-info {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-bottom: 12px;
    }

    /* Compact info styles */
    .compact-info {
        margin-bottom: 6px;
    }

    .deposit-info__title {
        max-width: 50%;
        margin-bottom: 0px;
        text-align: left;
    }

    .deposit-info__input {
        max-width: 50%;
        text-align: right;
        width: 100%;
    }

    .deposit-info__title .text {
        margin-bottom: 0;
    }

    .deposit-info__input .text {
        margin-bottom: 0;
    }

    /* Progress bar styles */
    .progress {
        height: 10px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 5px;
    }

    /* Compact progress bar for first card */
    .card.custom--card:first-of-type .progress {
        height: 6px;
        margin-bottom: 0;
    }

    .progress-bar {
        background-color: #BE8400;
    }

    /* Badge styles */
    .badge--success {
        background-color: rgba(40, 199, 111, 0.2);
        border: 1px solid #28c76f;
        color: #28c76f;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
    }
    
    .badge--primary {
        background-color: rgba(0, 123, 255, 0.2);
        border: 1px solid #007bff;
        color: #007bff;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
    }
    
    .badge--warning {
        background-color: rgba(255, 193, 7, 0.2);
        border: 1px solid #ffc107;
        color: #ffc107;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
    }

    /* Amount input styles */
    .amount-input-group {
        max-width: 200px;
        margin-left: auto;
    }

    .input-group-text {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    .form-control {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    .form-control:focus {
        background-color: rgba(255, 255, 255, 0.05);
        border-color: #BE8400;
        color: #fff;
        box-shadow: none;
    }

    /* Total amount styles */
    .total-amount {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 8px;
        margin-top: 8px;
        font-weight: 700;
    }

    /* Button styles */
    .btn--base {
        background-color: #BE8400;
        border-color: #BE8400;
        color: #fff;
        padding: 6px 20px;
        border-radius: 5px;
    }

    .btn--base:hover {
        background-color: #9e6e00;
        border-color: #9e6e00;
        color: #fff;
    }

    /* Text danger */
    .text-danger {
        color: #dc3545 !important;
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        .deposit-info__title,
        .deposit-info__input {
            max-width: 100%;
            width: 100%;
            text-align: left;
        }
        .deposit-info__input {
            margin-top: 5px;
            text-align: left;
        }
        .deposit-info {
            flex-direction: column;
            align-items: flex-start;
        }
        .input-group, .amount-input-group {
            max-width: 100% !important;
            margin-left: 0;
        }
    }

    @media (max-width: 767px) {
        .gateway-card {
            padding: 5px;
        }
        .token-info-card {
            padding: 15px;
        }
        .card-body {
            padding: 15px !important;
        }
        .label-text, .value-text {
            font-size: 13px;
        }
    }
</style>
@endpush

@push('script')
<script>
    (function ($) {
        "use strict";

        // Calculate tokens based on amount
        $('.amount').on('input', function() {
            const amount = parseFloat($(this).val()) || 0;

            // Get the token price from the server
            const tokenPrice = {{ $presale->price ?? 0 }};

            // Calculate tokens based on price (if price is 0, no tokens will be calculated)
            let tokens = 0;
            if (tokenPrice > 0) {
                tokens = amount / tokenPrice;
            }

            // Update tokens field
            $('#tokens').val(tokens.toLocaleString('en-US', {maximumFractionDigits: 2}));

            // Update total amount
            $('.final-amount').text('$' + amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ","));
        });

        // Disable submit button on page load
        $('form button[type=submit]').attr('disabled', true);

        // Enable/disable submit button based on amount
        $('.amount').on('input', function() {
            const amount = parseFloat($(this).val()) || 0;
            if (amount <= 0) {
                $('form button[type=submit]').attr('disabled', true);
            } else {
                $('form button[type=submit]').removeAttr('disabled');
            }
        });
    })(jQuery);
</script>
@endpush
