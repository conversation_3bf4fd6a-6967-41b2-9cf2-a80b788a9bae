<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if 'name' column doesn't exist in plans table
        if (!Schema::hasColumn('plans', 'name')) {
            Schema::table('plans', function (Blueprint $table) {
                $table->string('name')->nullable()->after('id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only drop if column exists
        if (Schema::hasColumn('plans', 'name')) {
            Schema::table('plans', function (Blueprint $table) {
                $table->dropColumn('name');
            });
        }
    }
}; 