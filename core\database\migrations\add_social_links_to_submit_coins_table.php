<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('submit_coins', function (Blueprint $table) {
            $table->string('website')->nullable()->after('is_presale');
            $table->string('telegram')->nullable()->after('website');
            $table->string('twitter')->nullable()->after('telegram');
            $table->string('discord')->nullable()->after('twitter');
            $table->string('facebook')->nullable()->after('discord');
            $table->string('reddit')->nullable()->after('facebook');
            $table->string('linktree')->nullable()->after('reddit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('submit_coins', function (Blueprint $table) {
            $table->dropColumn('website');
            $table->dropColumn('telegram');
            $table->dropColumn('twitter');
            $table->dropColumn('discord');
            $table->dropColumn('facebook');
            $table->dropColumn('reddit');
            $table->dropColumn('linktree');
        });
    }
}; 