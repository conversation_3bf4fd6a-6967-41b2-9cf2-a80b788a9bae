<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TokenVote extends Model
{
    use HasFactory;

    protected $fillable = [
        'token_id',
        'chain_id',
        'token_address',
        'user_id',
        'ip_address',
        'voted_at',
        'used_trend_vote',
        'is_negative',
    ];

    protected $casts = [
        'voted_at' => 'date',
        'used_trend_vote' => 'boolean',
        'is_negative' => 'boolean',
    ];

    public function token()
    {
        return $this->belongsTo(DexscreenerToken::class, 'token_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Check if a user with the given IP has already voted for the token today
    // Only checks for regular votes, not trend votes
    public static function hasVotedToday($tokenId, $ipAddress)
    {
        return self::where('token_id', $tokenId)
            ->where('ip_address', $ipAddress)
            ->where('voted_at', now()->format('Y-m-d'))
            ->where('used_trend_vote', false)
            ->exists();
    }

    // Check if a user with the given IP has already voted negatively for the token today
    public static function hasNegativeVotedToday($tokenId, $ipAddress)
    {
        return self::where('token_id', $tokenId)
            ->where('ip_address', $ipAddress)
            ->where('voted_at', now()->format('Y-m-d'))
            ->where('used_trend_vote', false)
            ->where('is_negative', true)
            ->exists();
    }

    // Get vote count for a token
    public static function getVoteCount($tokenId)
    {
        return self::where('token_id', $tokenId)
            ->where('is_negative', false)
            ->count();
    }

    // Get negative vote count for a token
    public static function getNegativeVoteCount($tokenId)
    {
        return self::where('token_id', $tokenId)
            ->where('is_negative', true)
            ->count();
    }

    // Get vote count for a token by chain_id and token_address
    public static function getVoteCountByAddress($chainId, $tokenAddress)
    {
        return self::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->where('is_negative', false)
            ->count();
    }

    // Get negative vote count for a token by chain_id and token_address
    public static function getNegativeVoteCountByAddress($chainId, $tokenAddress)
    {
        return self::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->where('is_negative', true)
            ->count();
    }

    // Get total positive vote count for a token by chain_id and token_address (no deduction)
    public static function getNetVoteCountByAddress($chainId, $tokenAddress)
    {
        // Just return positive votes without deducting negative votes
        return self::getVoteCountByAddress($chainId, $tokenAddress);
    }

    // Get total vote count including admin trend votes
    public static function getTotalVoteCount($tokenId)
    {
        // Get token
        $token = DexscreenerToken::where('id', $tokenId)->first();

        if (!$token) {
            return 0;
        }

        // Get regular user votes
        $userVotes = self::getVoteCount($tokenId);

        // Get admin trend votes
        $adminTrendVotes = 0;
        if (isset($token->trend_votes) && is_numeric($token->trend_votes)) {
            $adminTrendVotes = (int)$token->trend_votes;
        }

        // Return total votes
        return $userVotes + $adminTrendVotes;
    }

    // Get total vote count by chain_id and token_address
    public static function getTotalVoteCountByAddress($chainId, $tokenAddress)
    {
        // Get token
        $token = DexscreenerToken::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->first();

        if (!$token) {
            return 0;
        }

        // Get regular user votes
        $userVotes = self::getVoteCountByAddress($chainId, $tokenAddress);

        // Get admin trend votes
        $adminTrendVotes = 0;
        if (isset($token->trend_votes) && is_numeric($token->trend_votes)) {
            $adminTrendVotes = (int)$token->trend_votes;
        }

        // Return total votes
        return $userVotes + $adminTrendVotes;
    }

    // Get total positive vote count including admin trend votes by chain_id and token_address (no deduction)
    public static function getNetTotalVoteCountByAddress($chainId, $tokenAddress)
    {
        // Get token
        $token = DexscreenerToken::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->first();

        if (!$token) {
            return 0;
        }

        // Get regular user votes
        $userVotes = self::getVoteCountByAddress($chainId, $tokenAddress);

        // Get admin trend votes
        $adminTrendVotes = 0;
        if (isset($token->trend_votes) && is_numeric($token->trend_votes)) {
            $adminTrendVotes = (int)$token->trend_votes;
        }

        // Return total positive votes without deducting negative votes
        return $userVotes + $adminTrendVotes;
    }
}
