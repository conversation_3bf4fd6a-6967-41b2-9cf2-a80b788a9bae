<?php

namespace App\Console\Commands;

use App\Constants\Status;
use App\Models\CronJob;
use App\Models\CronJobLog;
use App\Lib\CurlRequest;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessCronJobs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process all due cron jobs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting cron job processing: ' . now());
        Log::info('Processing cron jobs via command');
        
        // Update general settings last_cron time
        $general = gs();
        $general->last_cron = now();
        $general->save();

        // Get all cron jobs that are due to run and are active
        $crons = CronJob::with('schedule')
            ->where('next_run', '<', now())
            ->where('is_running', Status::YES)
            ->get();
            
        if ($crons->isEmpty()) {
            $this->info('No cron jobs due to run');
            return 0;
        }
        
        $this->info('Found ' . $crons->count() . ' cron jobs to process');
        
        foreach ($crons as $cron) {
            $this->info('Processing cron job: ' . $cron->name);
            Log::info('Processing cron job: ' . $cron->name);
            
            $cronLog = new CronJobLog();
            $cronLog->cron_job_id = $cron->id;
            $cronLog->start_at = now();
            
            $success = false;
            
            if ($cron->is_default) {
                try {
                    $controller = new $cron->action[0];
                    $method = $cron->action[1];
                    
                    Log::info("Executing {$cron->action[0]}::{$method}");
                    $this->info("Executing {$cron->action[0]}::{$method}");
                    
                    $result = $controller->$method();
                    $success = $result !== false;
                    
                    if (!$success) {
                        $cronLog->error = "Method returned false";
                    }
                } catch (\Exception $e) {
                    $cronLog->error = $e->getMessage();
                    Log::error("Cron exception: {$e->getMessage()}");
                    Log::error($e->getTraceAsString());
                    $this->error("Error: {$e->getMessage()}");
                }
            } else {
                try {
                    CurlRequest::curlContent($cron->url);
                    $success = true;
                } catch (\Exception $e) {
                    $cronLog->error = $e->getMessage();
                    Log::error("URL cron exception: {$e->getMessage()}");
                    $this->error("URL Error: {$e->getMessage()}");
                }
            }
            
            // Update cron job information
            $cron->last_run = now();
            $cron->next_run = now()->addSeconds($cron->schedule->interval);
            $cron->save();

            // Update log information
            $cronLog->end_at = now();
            $startTime = Carbon::parse($cronLog->start_at);
            $endTime = Carbon::parse($cronLog->end_at);
            $diffInSeconds = $startTime->diffInSeconds($endTime);
            $cronLog->duration = $diffInSeconds;
            $cronLog->save();
            
            $this->info('Completed cron job: ' . $cron->name . ' - ' . ($success ? 'Success' : 'Failed'));
        }
        
        $this->info('Finished processing cron jobs at ' . now());
        
        return 0;
    }
} 