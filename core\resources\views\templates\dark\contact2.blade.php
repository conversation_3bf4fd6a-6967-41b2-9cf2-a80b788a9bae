@extends($activeTemplate . 'layouts.app')
@section('panel')
    @include($activeTemplate . 'partials.header')

    @php
        $content = getContent('contact_us.content', true);
        $socials = getContent('social_icon.element');
    @endphp

    <section class="contact section-bg contact-account-custom" style="padding-top: 30px !important; padding-bottom: 30px !important; margin-top: 0 !important; margin-bottom: 0 !important;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-7 col-md-8">
                    <div class="contact-form">
                        <!-- Global Banner (inside card) -->
                        <div class="contact-banner-container mb-3">
                            <x-ad position="global_banner" />
                        </div>

                        <h4 class="mb-4 text-center">Contact</h4>
                        <form action="{{ route('contact2') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="name" class="form-label">@lang('Your Name') <span class="text-danger">*</span></label>
                                        <input class="form--control" name="name" type="text"
                                            value="{{ auth()->user() ? auth()->user()->fullname : old('name') }}"
                                            @if (auth()->user() && auth()->user()->profile_complete) readonly @endif
                                            placeholder="@lang('Your Name')">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="email" class="form-label">@lang('Your Email') <span class="text-danger">*</span></label>
                                        <input class="form--control" name="email" type="text"
                                            value="{{ auth()->user() ? auth()->user()->email : old('email') }}"
                                            @if (auth()->user() && auth()->user()->profile_complete) readonly @endif
                                            placeholder="@lang('Your Email')">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="subject" class="form-label">@lang('Subject') <span class="text-danger">*</span></label>
                                        <input class="form--control" name="subject" type="text"
                                            value="{{ old('subject') }}" placeholder="@lang('Subject')">
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="message" class="form-label">@lang('Your Message') <span class="text-danger">*</span></label>
                                        <textarea class="form--control" name="message" placeholder="@lang('Your Message')">{{ old('message') }}</textarea>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <x-captcha />
                                </div>

                                <div class="col-12">
                                    <button class="btn--base w-100" id="recaptcha" type="submit">@lang('Submit')</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if ($sections->secs != null)
        @foreach (json_decode($sections->secs) as $sec)
            @include($activeTemplate . 'sections.' . $sec)
        @endforeach
    @endif

    @include($activeTemplate . 'partials.footer')
@endsection

@push('style')
<style>
    /* Global Banner Styles */
    .global-banner-container {
        background-color: var(--section-bg);
        padding: 15px 0;
    }

    /* Contact Banner Styles */
    .contact-banner-container {
        width: 100%;
        text-align: center;
        overflow: hidden;
    }

    .contact-account-custom {
        padding: 30px 0 !important;
        margin: 0 !important;
        min-height: auto !important;
    }
</style>
@endpush