<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdImpression extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'impression_date' => 'date',
    ];

    public function userBanner()
    {
        return $this->belongsTo(UserBanner::class);
    }

    /**
     * Increment impression count for a banner on a specific date
     *
     * @param int $userBannerId
     * @return void
     */
    public static function incrementImpression($userBannerId)
    {
        $today = now()->format('Y-m-d');

        self::updateOrCreate(
            [
                'user_banner_id' => $userBannerId,
                'impression_date' => $today
            ],
            [
                'count' => \DB::raw('count + 1')
            ]
        );
    }
}
