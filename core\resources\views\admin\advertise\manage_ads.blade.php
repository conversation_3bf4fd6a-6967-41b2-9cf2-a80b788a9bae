@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10 mb-4">
            <div class="card-body">
                <form action="{{ route('admin.advertise.manage') }}" method="GET">
                    <div class="d-flex flex-wrap gap-4">
                        <div class="flex-grow-1">
                            <label>@lang('Search')</label>
                            <input type="text" name="search" value="{{ request()->search }}" class="form-control" placeholder="@lang('Username or Ad Position')">
                        </div>
                        <div class="flex-grow-1">
                            <label>@lang('Status')</label>
                            <select name="status" class="form-control">
                                <option value="">@lang('All')</option>
                                <option value="1" {{ request()->status == '1' ? 'selected' : '' }}>@lang('Active')</option>
                                <option value="0" {{ request()->status == '0' ? 'selected' : '' }}>@lang('Pending')</option>
                                <option value="2" {{ request()->status == '2' ? 'selected' : '' }}>@lang('Rejected')</option>
                                <option value="3" {{ request()->status == '3' ? 'selected' : '' }}>@lang('Paused')</option>
                            </select>
                        </div>
                        <div class="flex-grow-0 align-self-end">
                            <button type="submit" class="btn btn--primary w-100 h-45"><i class="fas fa-search"></i> @lang('Search')</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('User')</th>
                                <th>@lang('Position')</th>
                                <th>@lang('Banner')</th>
                                <th>@lang('Redirect URL')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Impressions')</th>
                                <th>@lang('Submitted')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($ads as $ad)
                                <tr>
                                    <td>
                                        <span class="fw-bold">{{ $ad->user->username }}</span>
                                        <br>
                                        <span class="small">
                                            <a href="{{ route('admin.users.detail', $ad->user_id) }}">
                                                <span>@</span>{{ $ad->user->username }}
                                            </a>
                                        </span>
                                    </td>
                                    <td>{{ $ad->adPosition->name }}</td>
                                    <td>
                                        <a href="{{ getImage(getFilePath('ads_images').'/'.$ad->image) }}" target="_blank">
                                            <img src="{{ getImage(getFilePath('ads_images').'/'.$ad->image) }}" alt="@lang('Banner')" class="w-100" style="max-width: 100px;">
                                        </a>
                                    </td>
                                    <td>
                                        @if($ad->redirect_url)
                                            <a href="{{ $ad->redirect_url }}" target="_blank" class="text--primary">{{ Str::limit($ad->redirect_url, 30) }}</a>
                                        @else
                                            <span class="text-muted">@lang('No URL')</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($ad->status == 1)
                                            <span class="badge badge--success">@lang('Active')</span>
                                        @elseif($ad->status == 0)
                                            <span class="badge badge--warning">@lang('Pending')</span>
                                        @elseif($ad->status == 2)
                                            <span class="badge badge--danger">@lang('Rejected')</span>
                                            @if($ad->rejection_reason)
                                            <div class="mt-2">
                                                <small class="text-danger">@lang('Reason'): {{ $ad->rejection_reason }}</small>
                                            </div>
                                            @endif
                                        @elseif($ad->status == 3)
                                            <span class="badge badge--dark">@lang('Paused')</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $ad->getTotalImpressions() }}
                                    </td>
                                    <td>
                                        {{ showDateTime($ad->created_at) }}
                                        <br>
                                        {{ diffForHumans($ad->created_at) }}
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage ?? 'No ads found') }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($ads->hasPages())
                <div class="card-footer py-4">
                    {{ $ads->appends(['search' => request()->search, 'status' => request()->status])->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
