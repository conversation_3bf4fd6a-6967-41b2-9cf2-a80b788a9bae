<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\DexscreenerToken;
use App\Models\TokenPromotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TokenPromotionController extends Controller
{
    /**
     * Promote a token
     */
    public function promoteToken(Request $request)
    {
        $request->validate([
            'chain_id' => 'required|string',
            'token_address' => 'required|string',
            'days' => 'required|integer|min:1|max:100',
        ]);

        $user = Auth::user();

        // Check if user has enough promote credits
        if ($user->promote_credits < $request->days) {
            $notify[] = ['error', 'You do not have enough promote credits. Please purchase more credits.'];

            // If this is from the submit form, store the token data in session
            if ($request->has('from_submit_form')) {
                // Store the promotion details in session
                session(['pending_promotion' => [
                    'chain_id' => $request->chain_id,
                    'token_address' => $request->token_address,
                    'days' => $request->days,
                    'from_submit_form' => true
                ]]);

                return redirect()->route('user.plans.buy.promote.credits')->withNotify($notify);
            }

            return back()->withNotify($notify);
        }

        // Check if token exists
        $token = DexscreenerToken::where('chain_id', $request->chain_id)
            ->where('token_address', $request->token_address)
            ->first();

        if (!$token) {
            $notify[] = ['error', 'Token not found.'];
            return back()->withNotify($notify);
        }

        // Check if token is already being promoted by this user
        $existingPromotion = TokenPromotion::where('user_id', $user->id)
            ->where('chain_id', $request->chain_id)
            ->where('token_address', $request->token_address)
            ->where('is_active', true)
            ->where('end_date', '>', now())
            ->first();

        DB::beginTransaction();
        try {
            if ($existingPromotion) {
                // Extend the existing promotion
                $days = (int) $request->days;
                $existingPromotion->days += $days;
                $existingPromotion->end_date = Carbon::parse($existingPromotion->end_date)->addDays($days);
                $existingPromotion->save();
                $promotion = $existingPromotion;
            } else {
                // Create a new promotion
                $days = (int) $request->days;
                $promotion = new TokenPromotion();
                $promotion->user_id = $user->id;
                $promotion->chain_id = $request->chain_id;
                $promotion->token_address = $request->token_address;
                $promotion->days = $days;
                $promotion->start_date = now();
                $promotion->end_date = now()->addDays($days);
                $promotion->is_active = true;
                $promotion->save();
            }

            // Deduct promote credits from user
            $user->promote_credits -= (int) $request->days;
            $user->save();

            DB::commit();

            $notify[] = ['success', 'Token has been promoted successfully for ' . $request->days . ' days.'];

            // If this is from the submit form, continue with the form submission
            if ($request->has('from_submit_form')) {
                // Store a flag in session to indicate that the token should be promoted after submission
                session(['promote_after_submit' => [
                    'chain_id' => $request->chain_id,
                    'token_address' => $request->token_address,
                    'days' => $request->days,
                    'promotion_id' => $promotion->id
                ]]);

                // Clear any pending promotion data
                session()->forget('pending_promotion');

                // Redirect back to the submit form
                return redirect()->route('user.submit.coin.form', ['step' => 3])->withNotify($notify);
            }

            return redirect()->route('token.details', ['chainId' => $request->chain_id, 'tokenAddress' => $request->token_address])->withNotify($notify);
        } catch (\Exception $e) {
            DB::rollback();
            $notify[] = ['error', 'An error occurred: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Handle promotion for tokens that haven't been submitted yet
     */
    public function promoteTokenPresubmit(Request $request)
    {
        // Check if this is a pre-form promotion (before form fields are filled)
        $isPreForm = $request->has('pre_form') && $request->pre_form == '1';

        if ($isPreForm) {
            // For pre-form promotions, we only validate the days
            $request->validate([
                'days' => 'required|integer|min:1|max:100',
            ]);
        } else {
            // For regular promotions, validate all fields
            $request->validate([
                'chain_id' => 'required|string',
                'token_address' => 'required|string',
                'token_name' => 'required|string',
                'token_symbol' => 'required|string',
                'days' => 'required|integer|min:1|max:100',
            ]);
        }

        $user = Auth::user();

        // Check if there's already a pending promotion
        if (session()->has('promote_after_submit')) {
            $message = 'You already have a pending promotion. Please complete the token submission first.';

            // Check if it's an AJAX request
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $message
                ], 422);
            }

            $notify[] = ['error', $message];
            return back()->withNotify($notify);
        }

        // Ensure days is an integer
        $days = (int) $request->days;

        // Check if user has enough promote credits
        if ($user->promote_credits < $days) {
            $message = 'You do not have enough promote credits. Please purchase more credits.';

            // Store the promotion details in session
            session(['pending_promotion' => [
                'chain_id' => $request->chain_id,
                'token_address' => $request->token_address,
                'token_name' => $request->token_name,
                'token_symbol' => $request->token_symbol,
                'days' => $days, // Use the integer value
                'from_submit_form' => true,
                'expires_at' => now()->addHours(1)->timestamp // Add expiration
            ]]);

            $redirectUrl = route('user.plans.buy.promote.credits');

            // Check if it's an AJAX request
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'redirect' => $redirectUrl
                ]);
            }

            $notify[] = ['error', $message];
            return redirect($redirectUrl)->withNotify($notify);
        }

        // Store promotion details in session to be applied after token submission
        // Don't deduct credits yet - will be deducted after successful submission
        $sessionData = [
            'days' => $days, // Use the integer value
            'expires_at' => now()->addHours(1)->timestamp, // Add expiration
            'from_submit_form' => true, // Flag to indicate this is from the submit form
            'timestamp' => now()->timestamp, // Add a timestamp for tracking
            'is_pre_form' => $isPreForm // Flag to indicate if this was a pre-form promotion
        ];

        // Only add token details if this is not a pre-form promotion
        if (!$isPreForm) {
            $sessionData['chain_id'] = $request->chain_id;
            $sessionData['token_address'] = $request->token_address;
            $sessionData['token_name'] = $request->token_name;
            $sessionData['token_symbol'] = $request->token_symbol;
        }

        session(['promote_after_submit' => $sessionData]);

        // Log the session data for debugging
        \Illuminate\Support\Facades\Log::info('Promotion session data set', [
            'session_data' => $sessionData,
            'is_pre_form' => $isPreForm,
            'user_id' => auth()->id(),
            'user_credits' => auth()->user()->promote_credits
        ]);

        $message = 'Promotion confirmed! Your token will be automatically added to the Promoted Coins section after you complete and submit this form. Please continue filling out all required fields.';

        // Check if it's an AJAX request
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message
                // No redirect URL for success - stay on current page
            ]);
        }

        // For non-AJAX requests, redirect back to the current step with a notification
        $notify[] = ['success', $message];
        return back()->withNotify($notify);
    }

    /**
     * View user's promoted tokens
     */
    public function myPromotedTokens()
    {
        $pageTitle = 'My Promoted Tokens';

        // Get promotions only for tokens that still exist in the database
        $promotions = TokenPromotion::where('user_id', Auth::id())
            ->whereExists(function ($query) {
                $query->select(\DB::raw(1))
                    ->from('dexscreener_tokens')
                    ->whereColumn('dexscreener_tokens.chain_id', 'token_promotions.chain_id')
                    ->whereColumn('dexscreener_tokens.token_address', 'token_promotions.token_address');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(getPaginate(10)); // Set pagination to 10 items per page

        return view('Template::user.token.promotions', compact('pageTitle', 'promotions'));
    }
}
