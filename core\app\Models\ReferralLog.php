<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ReferralLog extends Model
{
    protected $guarded = ['id'];

    /**
     * The user who made the purchase (the referred user)
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * The user who receives the commission (the referrer)
     */
    public function referee()
    {
        return $this->belongsTo(User::class, 'referee_id');
    }
}
