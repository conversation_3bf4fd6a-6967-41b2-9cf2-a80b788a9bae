<?php

use Illuminate\Support\Facades\Route;

Route::namespace('User\Auth')->name('user.')->group(function () {

    Route::middleware('guest')->group(function () {
        Route::controller('LoginController')->group(function () {
            Route::get('/login', 'showLoginForm')->name('login');
            Route::post('/login', 'login');
            Route::get('logout', 'logout')->middleware('auth')->withoutMiddleware('guest')->name('logout');
        });

        Route::controller('RegisterController')->middleware(['guest'])->group(function () {
            Route::get('register', 'showRegistrationForm')->name('register');
            Route::post('register', 'register');
            Route::post('check-user', 'checkUser')->name('checkUser')->withoutMiddleware('guest');
        });

        Route::controller('ForgotPasswordController')->prefix('password')->name('password.')->group(function () {
            Route::get('reset', 'showLinkRequestForm')->name('request');
            Route::post('email', 'sendResetCodeEmail')->name('email');
            Route::get('code-verify', 'codeVerify')->name('code.verify');
            Route::post('verify-code', 'verifyCode')->name('verify.code');
        });

        Route::controller('ResetPasswordController')->group(function () {
            Route::post('password/reset', 'reset')->name('password.update');
            Route::get('password/reset/{token}', 'showResetForm')->name('password.reset');
        });

        Route::controller('SocialiteController')->group(function () {
            Route::get('social-login/{provider}', 'socialLogin')->name('social.login');
            Route::get('social-login/callback/{provider}', 'callback')->name('social.login.callback');
        });
    });
});

Route::middleware('auth')->name('user.')->group(function () {

    //authorization
    Route::middleware('registration.complete')->namespace('User')->controller('AuthorizationController')->group(function () {
        Route::get('authorization', 'authorizeForm')->name('authorization');
        Route::get('resend-verify/{type}', 'sendVerifyCode')->name('send.verify.code');
        Route::post('verify-email', 'emailVerification')->name('verify.email');
        Route::post('verify-mobile', 'mobileVerification')->name('verify.mobile');
        Route::post('verify-g2fa', 'g2faVerification')->name('2fa.verify');
    });

    Route::middleware(['check.status', 'registration.complete'])->group(function () {

        Route::namespace('User')->group(function () {

            Route::controller('UserController')->group(function () {
                Route::get('dashboard', 'home')->name('home');
                Route::get('advertise', 'advertise')->name('advertise');
                Route::get('ad-banners', 'adBanners')->name('ad.banners');
                Route::get('manage-ads', 'manageAds')->name('manage.ads');
                Route::get('ad-banner/upload/{id}', 'adBannerUploadForm')->name('ad.banner.upload.form');
                Route::post('ad-banner/upload/{id}', 'adBannerUpload')->name('ad.banner.upload');
                Route::get('ad-banner/success', 'adBannerSuccess')->name('ad.banner.success');
                Route::post('ad-banner/pause/{id}', 'pauseBanner')->name('ad.banner.pause');
                Route::post('ad-banner/delete/{id}', 'deleteBanner')->name('ad.banner.delete');
                Route::get('ad-banner/check-active/{id}', 'checkActiveAd')->name('ad.banner.check.active');
                Route::get('download-attachments/{file_hash}', 'downloadAttachment')->name('download.attachment');

                //2FA
                Route::get('twofactor', 'show2faForm')->name('twofactor');
                Route::post('twofactor/enable', 'create2fa')->name('twofactor.enable');
                Route::post('twofactor/disable', 'disable2fa')->name('twofactor.disable');

                //KYC
                Route::get('kyc-form', 'kycForm')->name('kyc.form');
                Route::get('kyc-data', 'kycData')->name('kyc.data');
                Route::post('kyc-submit', 'kycSubmit')->name('kyc.submit');

                //Submit Coin
                Route::get('submit-coin', 'submitCoinForm')->name('submit.coin.form');
                Route::post('submit-coin/{step?}', 'submitCoin')->name('submit.coin.store');
                Route::get('submit-coin-success', 'submitCoinSuccess')->name('submit.coin.success');

                //Report
                Route::any('payment-log', 'paymentHistory')->name('payment.history');
                Route::get('transactions', 'transactions')->name('transactions');

                // referral
                Route::get('my-referral', 'referral')->name('referral');
                Route::get('referral-bonus-logs', 'referralLog')->name('referral.log');

                // Deposit Funds
                Route::get('deposit-funds', 'depositFunds')->name('deposit.funds');

                Route::post('add-device-token', 'addDeviceToken')->name('add.device.token');
            });

            //Profile setting
            Route::controller('ProfileController')->group(function () {
                Route::get('profile-setting', 'profile')->name('profile.setting');
                Route::post('profile-setting', 'submitProfile');
                Route::get('change-password', 'changePassword')->name('change.password');
                Route::post('change-password', 'submitPassword');
            });

            // Withdraw
            Route::controller('WithdrawController')->prefix('withdraw')->name('withdraw')->group(function () {
                Route::middleware('kyc')->group(function () {
                    Route::get('/', 'withdrawMoney');
                    Route::post('/', 'withdrawStore')->name('.money');
                    Route::get('preview/{id}', 'withdrawPreview')->name('.preview');
                });
                Route::get('my-withdrawals', 'withdrawLog')->name('.history');
            });

            // Plans
            Route::controller('PlanController')->name('plans.')->prefix('plans')->group(function () {
                Route::get('/', 'index')->name('index');
                Route::get('purchased', 'purchased')->name('purchased');
                Route::post('purchase', 'purchase')->name('purchase');
                Route::get('buy-trend-votes', 'buyTrendVotes')->name('buy.trend.votes');
                Route::get('buy-ad-credits', 'buyAdCredits')->name('buy.ad.credits');
                Route::get('buy-promote-credits', 'buyPromoteCredits')->name('buy.promote.credits');
                Route::get('payment/{id}', 'selectPayment')->name('select.payment');
                Route::post('payment/{id}', 'processPayment')->name('process.payment');
            });

            // Token Promotion
            Route::controller('TokenPromotionController')->name('token.')->prefix('token')->group(function () {
                Route::post('promote', 'promoteToken')->name('promote');
                Route::post('promote-presubmit', 'promoteTokenPresubmit')->name('promote.presubmit');
                Route::get('my-promotions', 'myPromotedTokens')->name('my.promotions');
            });

            // Token Watchlist
            Route::controller('TokenWatchlistController')->name('token.')->prefix('token')->group(function () {
                Route::get('my-watchlist', 'myWatchlist')->name('my.watchlist');
            });

            // Launchpad (formerly CCL Token Buy)
            Route::controller('CclTokenController')->name('ccl.token.')->prefix('launchpad')->group(function () {
                Route::get('buy', 'buyTokens')->name('buy');
                Route::get('my-tokens', 'myTokens')->name('my');
                Route::get('presale/{id}', 'viewPresale')->name('presale');
                Route::post('purchase', 'purchase')->name('purchase');
                Route::post('save-token-address', 'saveTokenAddress')->name('save.token.address');
            });

            // Submitted Coins
            Route::controller('SubmitCoinController')->name('coin.')->prefix('coin')->group(function () {
                Route::get('my-submissions', 'mySubmittedCoins')->name('my.submissions');
            });
        });

        // Payment
        Route::prefix('deposit')->controller('Gateway\PaymentController')->group(function () {
            Route::any('/payment/{id}', 'payment')->name('payment');
            Route::name('deposit.')->group(function () {
                Route::post('insert', 'depositInsert')->name('insert');
                Route::get('confirm', 'depositConfirm')->name('confirm');
                Route::get('manual', 'manualDepositConfirm')->name('manual.confirm');
                Route::post('manual', 'manualDepositUpdate')->name('manual.update');
            });
        });
    });
});
