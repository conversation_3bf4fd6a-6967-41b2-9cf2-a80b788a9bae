@extends($activeTemplate . 'layouts.master')

@section('content')
<!-- Error <PERSON> -->
<div class="modal fade" id="errorModal" tabindex="-1" role="dialog" aria-labelledby="errorModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="background-color: #2c2f3e; color: #B9BABB; border: 1px solid #BE8400;">
            <div class="modal-header" style="border-bottom: 1px solid #3b3f4c;">
                <h5 class="modal-title" id="errorModalLabel">@lang('Error')</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" style="color: #B9BABB; background-color: transparent; border: none; font-size: 1.5rem; font-weight: 700; opacity: 0.5;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
            <div class="modal-footer" style="border-top: 1px solid #3b3f4c;">
                <button type="button" class="btn btn--danger btn--sm" data-bs-dismiss="modal">@lang('Close')</button>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="custom--card">
            <div class="card-header">
                <h5 class="card-title">@lang('Upload Banner for') {{ $adPosition->name }}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-info" style="background-color: rgba(49, 215, 169, 0.1); border-color: #31D7A9; color: #B9BABB;">
                            <p class="mb-0">@lang('Upload your banner image for this position. Required size:') <strong>{{ $adPosition->size }}</strong></p>
                            <p class="mb-0 mt-2">@lang('Current ad credits:') <strong>{{ auth()->user()->ad_credits }}</strong></p>
                        </div>
                    </div>
                </div>

                <form action="{{ route('user.ad.banner.upload', $adPosition->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>@lang('Banner Image')</label>
                                <input type="file" class="form-control" name="image" id="image" accept="image/*" required>
                                <small class="text-muted">@lang('Supported formats: jpg, jpeg, png, gif. Max file size: 5MB')<span style="color: #dc3545;">*</span></small>
                                <div id="image_preview" class="mt-3 d-none">
                                    <img src="" alt="@lang('Banner Preview')" class="w-100">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>@lang('Redirect URL')</label>
                                <input type="url" class="form-control" name="redirect_url" id="redirect_url" placeholder="https://example.com" value="{{ $existingBanner->redirect_url ?? '' }}">
                                <small class="text-muted">@lang('When users click on your banner, they will be redirected to this URL in a new tab.')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12 text-center">
                            <div class="form-group">
                                <button type="submit" class="btn--base" style="min-width: 150px;">@lang('Submit')</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    // Function to show error in modal - defined in global scope
    function showError(message) {
        $('#errorMessage').text(message);
        var errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
        errorModal.show();
    }

    // Initialize Bootstrap components
    document.addEventListener('DOMContentLoaded', function() {
        // Make sure modal is properly initialized
        var errorModalEl = document.getElementById('errorModal');
        if (errorModalEl) {
            var errorModal = new bootstrap.Modal(errorModalEl);

            // Add manual close handlers for the modal
            document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(function(element) {
                element.addEventListener('click', function() {
                    errorModal.hide();
                });
            });
        }
    });

    (function($) {
        "use strict";

        // Image preview and validation
        $('#image').on('change', function() {
            var file = this.files[0];
            if (file) {
                // Check file type
                var fileType = file.type;
                if (!fileType.match('image/(jpeg|jpg|png|gif)')) {
                    showError('Please upload a valid image file (jpg, jpeg, png, gif)');
                    $(this).val('');
                    return;
                }

                // Check file size (5MB = 5 * 1024 * 1024 bytes)
                if (file.size > 5 * 1024 * 1024) {
                    showError('File size must be less than 5MB');
                    $(this).val('');
                    return;
                }

                // Create image object to check dimensions
                var img = new Image();
                img.onload = function() {
                    // Get the required dimensions from the position size text
                    var sizeText = "{{ $adPosition->size }}";
                    var dimensions = extractDimensions(sizeText);

                    if (dimensions && dimensions.width && dimensions.height) {
                        // If we have specific dimensions to check
                        if (img.width !== dimensions.width || img.height !== dimensions.height) {
                            showError('Image dimensions must be ' + dimensions.width + 'x' + dimensions.height + ' pixels');
                            $('#image').val('');
                            $('#image_preview').addClass('d-none');
                            $('#image_preview img').attr('src', '');
                        } else {
                            // If dimensions are correct, show the preview
                            $('#image_preview').removeClass('d-none');
                            $('#image_preview img').attr('src', img.src);
                        }
                    } else {
                        // If no specific dimensions to check, just show the preview
                        $('#image_preview').removeClass('d-none');
                        $('#image_preview img').attr('src', img.src);
                    }
                };

                // Read the file
                var reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            } else {
                $('#image_preview').addClass('d-none');
                $('#image_preview img').attr('src', '');
            }
        });

        // Function to extract dimensions from size text
        function extractDimensions(sizeText) {
            // If the size contains "Responsive", return null
            if (sizeText.indexOf('Responsive') !== -1) {
                return null;
            }

            // Extract the first dimension (usually desktop) from the string
            // Format examples: "570x100 (Desktop)", "300x250 (All devices)"
            var match = sizeText.match(/(\d+)x(\d+)/);
            if (match) {
                return {
                    width: parseInt(match[1]),
                    height: parseInt(match[2])
                };
            }

            return null;
        }

        // Form submission validation
        $('form').on('submit', function(e) {
            var fileInput = $('#image')[0];
            if (fileInput && fileInput.files.length > 0) {
                var file = fileInput.files[0];

                // Check file size again (5MB = 5 * 1024 * 1024 bytes)
                if (file.size > 5 * 1024 * 1024) {
                    showError('File size must be less than 5MB');
                    e.preventDefault();
                    return false;
                }

                // File type is already checked on file selection
            }
            // Let the form submit if all validations pass
        });
    })(jQuery);
</script>
@endpush
