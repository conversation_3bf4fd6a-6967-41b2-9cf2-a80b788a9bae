@php
    use Illuminate\Support\Facades\Schema;

    $footerCaption = getContent('footer.content', true);
    $contactCaption = getContent('contact_us.content', true);
    $socialIcons = getContent('social_icon.element', false, null, true);

    // Check if column exists before filtering
    if (Schema::hasColumn('pages', 'show_in_footer')) {
        $pages = App\Models\Page::activeTemplate()
            ->where('is_default', Status::NO)
            ->where('show_in_footer', 1)
            ->get();
    } else {
        $pages = App\Models\Page::activeTemplate()
            ->where('is_default', Status::NO)
            ->get();
    }

    $policyPages = getContent('policy_pages.element', false, null, true);
@endphp

<footer class="footer-area bg-img bg-overlay-one" style="background-image: url({{ frontendImage('footer', @$footerCaption->data_values->background_image, '1900x650') }});">
    <!-- Footer Ad -->
    <div class="footer-ad-container py-3">
        <div class="container">
            <x-ad position="footer_full" />
        </div>
    </div>
    <div class="footer-top py-50 container">
        <div class="row justify-content-center gy-5">
            <div class="col-xl-4 col-sm-6 pe-lg-5">
                <div class="footer-item">
                    <div class="footer-item__logo">
                        <a href="{{ route('home') }}"> <img src="{{ siteLogo() }}" alt="{{ gs('site_name') }}"></a>
                    </div>
                    <p class="footer-item__desc">{{ __(@$footerCaption->data_values->short_details) }}</p>
                </div>
            </div>
            <div class="col-xl-2 col-sm-6">
                <div class="footer-item">
                    <h5 class="footer-item__title"> @lang('Quick Links') </h5>
                    <ul class="footer-menu">
                        @foreach ($pages as $item)
                            <li class="footer-menu__item"><a class="footer-menu__link" href="{{ route('pages', ['slug' => $item->slug]) }}">{{ __($item->name) }}</a></li>
                        @endforeach

                        <li class="footer-menu__item"><a class="footer-menu__link" href="{{ route('blog') }}">@lang('Articles')</a></li>
                        @auth
                        <li class="footer-menu__item"><a class="footer-menu__link" href="{{ route('contact') }}">@lang('Create Ticket')</a></li>
                        @endauth
                        @guest
                        <li class="footer-menu__item"><a class="footer-menu__link" href="{{ route('contact2') }}">@lang('Contact')</a></li>
                        @endguest
                    </ul>
                </div>
            </div>
            <div class="col-xl-3 col-sm-6">
                <div class="footer-item">
                    <h5 class="footer-item__title">@lang('Useful Links')</h5>
                    <ul class="footer-menu">
                        @foreach ($policyPages as $page)
                            <li class="footer-menu__item"><a class="footer-menu__link" href="{{ route('policy.pages', $page->slug) }}">{{ __($page->data_values->title) }}</a></li>
                        @endforeach

                    </ul>
                </div>
            </div>
            <div class="col-xl-3 col-sm-6">
                <div class="footer-item">
                    <h5 class="footer-item__title">@lang('Socials') </h5>
                    <ul class="footer-contact-menu">
                        @foreach($socialIcons as $social)
                        <li class="footer-contact-menu__item">
                            <div class="footer-contact-menu__item-icon">
                                @if(isset($social->data_values->social_image))
                                    <img src="{{ frontendImage('social_icon', @$social->data_values->social_image, '50x50') }}" alt="{{ $social->data_values->title }}">
                                @else
                                    @php echo $social->data_values->social_icon @endphp
                                @endif
                            </div>
                            <div class="footer-contact-menu__item-content">
                                <p><a href="{{ $social->data_values->url }}" target="_blank">{{ __($social->data_values->title) }}</a></p>
                            </div>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="bottom-footer py-3">
        <div class="container">
            <div class="row gy-3">
                <div class="col-md-12 text-center">
                    <div class="bottom-footer-text text-white">@lang('Copyright') &copy; {{ date('Y') }} <a class="text--base" href="{{ route('home') }}">{{ gs('site_name') }}</a>. @lang('All rights reserved')</div>
                </div>
            </div>
        </div>
    </div>
</footer>

@push('style')
<style>
    .footer-contact-menu__item-icon img {
        max-width: 24px;
        max-height: 24px;
        width: auto;
        height: auto;
    }
    .footer-contact-menu__item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    .footer-contact-menu__item-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
    }

    /* Footer Ad Styles */
    .footer-ad-container {
        text-align: center;
    }

    .footer-ad-container .ad-container {
        margin: 0 auto;
    }
</style>
@endpush
