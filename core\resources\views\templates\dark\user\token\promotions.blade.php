@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="container">

    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card custom--card" style="background-color: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.15); border-radius: 5px;">
                <div class="card-header" style="background-color: #BE8400; color: #ffffff; border-radius: 5px 5px 0 0;">
                    <h5 class="card-title mb-0">@lang('My Promoted Tokens List')</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover custom--table">
                            <thead>
                                <tr>
                                    <th>@lang('Token')</th>
                                    <th>@lang('Chain')</th>
                                    <th>@lang('Days')</th>
                                    <th>@lang('Start Date')</th>
                                    <th>@lang('End Date')</th>
                                    <th>@lang('Status')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($promotions as $promotion)
                                    @php
                                        // Get the token (we know it exists because of our controller filter)
                                        $token = \App\Models\DexscreenerToken::where('chain_id', $promotion->chain_id)
                                            ->where('token_address', $promotion->token_address)
                                            ->first();
                                        $now = now();
                                        $isActive = $promotion->is_active && $now < $promotion->end_date;

                                        // Calculate days left with special handling for less than 24 hours
                                        if ($isActive) {
                                            $diffInHours = $now->diffInHours($promotion->end_date, false);
                                            if ($diffInHours < 24) {
                                                $daysLeft = 1; // Show as 1 day left if less than 24 hours remain
                                            } else {
                                                $daysLeft = (int) $now->diffInDays($promotion->end_date, false);
                                                if ($daysLeft == 0 && $diffInHours > 0) {
                                                    $daysLeft = 1; // Ensure we show at least 1 day if hours remain
                                                }
                                            }
                                        } else {
                                            $daysLeft = 0;
                                        }
                                    @endphp
                                    <tr>
                                        <td data-label="@lang('Token')">
                                            <div class="d-flex align-items-center">
                                                <img src="@if(isset($token->image_url) && !filter_var($token->image_url, FILTER_VALIDATE_URL) && strpos($token->image_url, 'http') !== 0){{ asset('assets/images/coin_logos/'.$token->image_url) }}@else{{ $token->image_url ?? asset('assets/images/default.png') }}@endif" alt="{{ $token->token_symbol }}" class="coin-icon me-2" width="32" height="32">
                                                <div class="token-info">
                                                    <div class="fw-bold">{{ $token->token_symbol }}</div>
                                                    <div class="small">{{ Str::limit($token->token_name, 20) }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td data-label="@lang('Chain')">
                                            <span class="badge chain-badge">{{ ucfirst($promotion->chain_id) }}</span>
                                        </td>
                                        <td data-label="@lang('Days')">{{ $promotion->days }}</td>
                                        <td data-label="@lang('Start Date')">{{ showDateTime($promotion->start_date) }}</td>
                                        <td data-label="@lang('End Date')">{{ showDateTime($promotion->end_date) }}</td>
                                        <td data-label="@lang('Status')">
                                            @if($isActive)
                                                <span class="badge bg-success">@lang('Active')</span>
                                                <div class="small text-muted">{{ $daysLeft }} @lang('days left')</div>
                                            @else
                                                <span class="badge bg-danger">@lang('Expired')</span>
                                            @endif
                                        </td>
                                        <td data-label="@lang('Action')">
                                            <a href="{{ route('token.details', ['chainId' => $promotion->chain_id, 'tokenAddress' => $promotion->token_address]) }}" class="btn btn-sm btn-primary">
                                                <i class="las la-eye"></i> @lang('View')
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center" data-label="">@lang('No promoted tokens found')</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($promotions->hasPages())
    <div class="mt-4 pagination-container">
        {{ paginateLinks($promotions) }}
    </div>
    @endif
</div>
@endsection

@push('style')
<style>
    .coin-icon {
        border-radius: 50%;
        object-fit: cover;
    }
    .chain-badge {
        background-color: #BE8400;
        color: #ffffff;
        font-weight: 500;
    }

    /* Token info styling */
    .token-info {
        display: flex;
        flex-direction: column;
    }

    /* Table styling */
    .table {
        color: #fff;
    }

    .table thead th {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-weight: 500;
    }

    .table tbody td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        vertical-align: middle;
    }

    /* Base table responsive styles */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        width: 100%;
    }

    .custom--table {
        min-width: 800px;
        width: 100%;
    }

    /* Pagination responsive styles */
    /* Hide unnecessary elements */
    .pagination-wrapper .d-flex.justify-content-between.flex-fill,
    .pagination-container .d-flex.justify-content-between.flex-fill {
        display: none !important;
    }

    /* Main container for pagination */
    .pagination-wrapper,
    .pagination-container {
        width: 100%;
        overflow-x: auto;
        padding: 5px 0;
        margin: 10px 0;
        scrollbar-width: thin;
    }

    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        display: block !important;
        width: 100%;
    }

    /* Center the pagination buttons */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-wrapper nav,
    .pagination-wrapper .pagination,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-container nav,
    .pagination-container .pagination,
    .pagination-container nav > ul {
        display: flex;
        justify-content: center !important;
        width: 100%;
    }

    /* Improved pagination styling for all devices */
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
        margin: 0;
        padding: 0;
        justify-content: center;
        max-width: 100%;
    }

    /* Handle large number of pagination items - general fallback */
    .pagination.pagination-sm {
        justify-content: center;
        padding: 5px 0;
    }

    /* Ensure consistent spacing between pagination items */
    .pagination .page-item {
        margin: 3px;
    }

    /* Style for pagination links */
    .pagination .page-item .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        padding: 0 10px;
        border-radius: 4px !important;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    /* Style for forward and back buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item.previous .page-link,
    .pagination .page-item.next .page-link {
        font-size: 14px;
        padding: 0 12px;
        min-width: 40px; /* Slightly wider for navigation buttons */
    }

    /* Ensure arrow icons are visible */
    .pagination .page-item .page-link span[aria-hidden="true"] {
        display: inline-block;
        line-height: 1;
    }

    /* Style for disabled pagination items */
    .pagination .page-item.disabled .page-link {
        background-color: hsl(0deg 0% 100% / 40%);
        opacity: 0.7;
    }

    /* Responsive styles for promotions card */
    @media (max-width: 1024px) {
        .custom--card .card-body {
            padding: 20px;
        }

        /* Table responsive styles for all devices */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            position: relative;
            width: 100%;
            margin-bottom: 15px;
        }

        .custom--table {
            min-width: 800px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom--table thead th,
        .custom--table tbody td {
            padding: 10px 6px;
            font-size: 13px;
            white-space: nowrap;
        }

        .coin-icon {
            max-width: 28px;
            max-height: 28px;
        }

        .token-info {
            flex-direction: row;
            align-items: center;
            gap: 8px;
        }

        .token-info .small {
            margin-top: 0;
        }
    }

    /* Tablet specific styles */
    @media (width: 768px) and (height: 1024px),
           (width: 820px) and (height: 1180px),
           (width: 912px) and (height: 1368px),
           (width: 853px) and (height: 1280px),
           (width: 1024px) and (height: 1366px) {
        .custom--card {
            width: 100%;
        }

        /* Ensure table is scrollable on tablets */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
        }

        .table-responsive table {
            min-width: 800px;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 12px;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .custom--card .card-header {
            padding: 15px;
        }

        .custom--card .card-body {
            padding: 15px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .coin-icon {
            max-width: 24px;
            max-height: 24px;
        }

        /* Ensure horizontal scrolling for tables on mobile */
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
            max-width: 100%;
            margin-bottom: 15px;
        }

        .table-responsive table {
            min-width: 800px;
            width: 100%;
        }

        .table-responsive table th,
        .table-responsive table td {
            white-space: nowrap;
            padding: 8px 10px;
            font-size: 12px;
        }

        /* Responsive adjustments for smaller screens */
        .pagination-wrapper,
        .pagination-container {
            padding: 3px 0;
            margin: 8px 0;
        }

        .pagination {
            gap: 3px;
        }

        .pagination .page-item {
            margin: 2px;
        }

        .pagination .page-item .page-link {
            min-width: 32px;
            height: 32px;
            font-size: 13px;
        }

        /* Adjust forward/back buttons on small screens */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 36px;
            padding: 0 10px;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .custom--card .card-header {
            padding: 12px;
        }

        .custom--card .card-body {
            padding: 12px;
        }

        .card-title {
            font-size: 16px;
        }

        .btn-sm {
            padding: 3px 6px;
            font-size: 11px;
        }

        /* Ensure table remains scrollable on extra small devices */
        .table-responsive {
            margin: 0 -12px;
            width: calc(100% + 24px);
            padding: 0;
        }

        .table-responsive table th,
        .table-responsive table td {
            padding: 6px 8px;
            font-size: 11px;
        }

        .coin-icon {
            max-width: 20px;
            max-height: 20px;
        }

        /* Responsive adjustments for very small screens */
        .pagination .page-item .page-link {
            min-width: 30px;
            height: 30px;
            font-size: 12px;
            padding: 0 8px;
        }
    }

    /* Tablet-specific adjustments */
    @media (min-width: 768px) and (max-width: 1024px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 8px 0;
            margin: 12px 0;
        }

        .pagination {
            gap: 6px;
        }

        .pagination .page-item {
            margin: 3px;
        }

        .pagination .page-item .page-link {
            min-width: 38px;
            height: 38px;
            font-size: 15px;
        }

        /* Adjust forward/back buttons on tablets */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 42px;
            padding: 0 12px;
            font-size: 15px;
        }
    }
</style>
@endpush
