@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="row">
    <div class="col-lg-12">
        <div class="dashboard-table">
            <table class="table table--responsive--lg">
                <thead>
                    <tr>
                        <th>@lang('Plan')</th>
                        <th>@lang('Price')</th>
                        <th>@lang('Trend Votes')</th>
                        <th>@lang('Promote Credits')</th>
                        <th>@lang('Transaction')</th>
                        <th>@lang('Status')</th>
                        <th>@lang('Time')</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($purchases as $purchase)
                    <tr>
                        <td>@lang('Plan Purchase')</td>
                        <td>{{ showAmount($purchase->price) }} {{ gs('cur_text') }}</td>
                        <td>{{ $purchase->plan->trend_votes }}</td>
                        <td>{{ $purchase->plan->promote_credits ?? 0 }}</td>
                        <td>{{ $purchase->trx }}</td>
                        <td>
                            @if($purchase->status == Status::COMPLETED)
                            <span class="badge badge--success">@lang('Completed')</span>
                            @elseif($purchase->status == Status::PENDING)
                            <span class="badge badge--warning">@lang('Pending')</span>
                            @else
                            <span class="badge badge--danger">@lang('Cancelled')</span>
                            @endif
                        </td>
                        <td>{{ showDateTime($purchase->created_at) }}</td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="100%" class="text-center">{{ $emptyMessage }}</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($purchases->hasPages())
        <div class="mt-4 pagination-container">
            {{ paginateLinks($purchases) }}
        </div>
        @endif
    </div>
</div>
@endsection
