<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\DexscreenerService;

class UpdateUnpopularTokensDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-unpopular-tokens-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refreshes unpopular token data from <PERSON>screener.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(DexscreenerService $dexscreenerService)
    {
        Log::info('==== UpdateUnpopularTokensDataCommand: DEXSCREENER UNPOPULAR TOKEN REFRESH STARTED ====');

        try {
            // Clear all token-related caches
            Cache::forget('dexscreener_tokens');
            Cache::forget('admin_tokens');
            Cache::forget('admin_tokens_active');
            Cache::forget('admin_tokens_all');
            Log::info('UpdateUnpopularTokensDataCommand: Cleared token caches.');

            $dexResult = $dexscreenerService->refreshUnpopularTokens();

            if ($dexResult) {
                Log::info('==== UpdateUnpopularTokensDataCommand: DEXSCREENER UNPOPULAR TOKEN REFRESH COMPLETED SUCCESSFULLY ====');
                $this->info('Unpopular token data refreshed successfully.');
                return Command::SUCCESS;
            } else {
                Log::error('==== UpdateUnpopularTokensDataCommand: DEXSCREENER UNPOPULAR TOKEN REFRESH FAILED ====');
                $this->error('Failed to refresh unpopular token data.');
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            Log::error('UpdateUnpopularTokensDataCommand: Dexscreener API Error: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            $this->error('Error refreshing unpopular tokens: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
} 