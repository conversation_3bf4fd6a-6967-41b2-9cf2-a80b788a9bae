<?php

namespace App\Jobs;

use App\Models\CryptoCurrency;
use App\Models\CryptoSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchCryptoDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $setting = CryptoSetting::first();
        
        if (!$setting || !$setting->is_active || !$setting->api_key) {
            return;
        }

        try {
            $response = Http::withHeaders([
                'X-CMC_PRO_API_KEY' => $setting->api_key,
                'Accept' => 'application/json'
            ])->get('https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest', [
                'limit' => $setting->display_count,
                'convert' => 'USD'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                // Clear existing data
                CryptoCurrency::truncate();
                
                // Insert new data
                foreach ($data['data'] as $crypto) {
                    CryptoCurrency::create([
                        'name' => $crypto['name'],
                        'symbol' => $crypto['symbol'],
                        'price' => $crypto['quote']['USD']['price'],
                        'percent_change_24h' => $crypto['quote']['USD']['percent_change_24h'],
                        'market_cap' => $crypto['quote']['USD']['market_cap'],
                        'volume_24h' => $crypto['quote']['USD']['volume_24h'],
                        'last_updated' => now(),
                    ]);
                }
                
                // Update last updated time
                $setting->last_updated = now();
                $setting->save();
                
                Log::info('Crypto data fetched successfully');
            } else {
                Log::error('Failed to fetch crypto data: ' . ($response->json()['status']['error_message'] ?? 'Unknown error'));
            }
        } catch (\Exception $e) {
            Log::error('API Error: ' . $e->getMessage());
        }
    }
} 