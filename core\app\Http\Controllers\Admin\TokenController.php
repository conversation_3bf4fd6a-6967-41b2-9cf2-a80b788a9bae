<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DexscreenerToken;
use App\Models\SubmitCoin;
use Illuminate\Http\Request;

class TokenController extends Controller
{
    public function submittedTokens()
    {
        $pageTitle = 'Pending User Submitted Tokens';
        $submittedTokens = DexscreenerToken::where('submitted_by_user_id', '!=', null)
            ->where('is_verified', false)
            ->orderBy('created_at', 'desc')
            ->paginate(getPaginate());

        return view('admin.tokens.submitted', compact('pageTitle', 'submittedTokens'));
    }

    public function verifyToken($id)
    {
        $token = DexscreenerToken::findOrFail($id);
        $token->is_verified = true;
        $token->save();

        // Also update the submit_coins record if it exists
        // For presale tokens, the contract_address might be a generated ID
        $submitCoin = SubmitCoin::where('contract_address', $token->token_address)
            ->where('user_id', $token->submitted_by_user_id)
            ->first();

        if ($submitCoin) {
            $submitCoin->status = 1; // Approved
            $submitCoin->save();
        }

        $notify[] = ['success', 'Token has been verified and will now be displayed on the homepage'];
        return back()->withNotify($notify);
    }

    public function rejectToken($id)
    {
        $token = DexscreenerToken::findOrFail($id);

        // Also update the submit_coins record if it exists
        // For presale tokens, the contract_address might be a generated ID
        $submitCoin = SubmitCoin::where('contract_address', $token->token_address)
            ->where('user_id', $token->submitted_by_user_id)
            ->first();

        if ($submitCoin) {
            $submitCoin->status = 2; // Rejected
            $submitCoin->save();
        }

        // Delete associated social links
        \App\Models\SocialLink::where('token_chain_id', $token->chain_id)
            ->where('token_address', $token->token_address)
            ->delete();

        // Delete the token from dexscreener_tokens
        $token->delete();

        $notify[] = ['success', 'Token has been rejected and removed from the database'];
        return back()->withNotify($notify);
    }

    public function viewToken($id)
    {
        $pageTitle = 'Token Details';
        $token = DexscreenerToken::findOrFail($id);

        return view('admin.tokens.view', compact('pageTitle', 'token'));
    }
}