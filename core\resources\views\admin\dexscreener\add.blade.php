@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title"></h5>
            </div>
            <form action="{{ route('admin.dexscreener.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="chain_id">@lang('Blockchain')</label>
                                <select name="chain_id" id="chain_id" class="form-control" required>
                                    <option value="">@lang('Select Blockchain')</option>
                                    @foreach($blockchains as $id => $name)
                                        <option value="{{ $id }}" {{ old('chain_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="token_address">@lang('Token Contract Address')</label>
                                <input type="text" name="token_address" id="token_address" class="form-control" value="{{ old('token_address') }}" required placeholder="0x..." />
                                <small class="text-muted">@lang('Enter the contract address of the token (e.g., 0x1234...)')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="token_name">@lang('Token Name')</label>
                                <input type="text" name="token_name" id="token_name" class="form-control" value="{{ old('token_name') }}" required placeholder="Bitcoin" />
                                <small class="text-muted">@lang('Enter the full name of the token (e.g., Bitcoin)')</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="token_symbol">@lang('Token Symbol')</label>
                                <input type="text" name="token_symbol" id="token_symbol" class="form-control" value="{{ old('token_symbol') }}" required placeholder="BTC" />
                                <small class="text-muted">@lang('Enter the symbol of the token (e.g., BTC)')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="image">@lang('Token Logo')</label>
                                <x-image-uploader type="coinLogos" name="image" id="image-upload-input" :required="false" />
                                <small class="text-muted">@lang('Upload a logo for the token. This will be used if the API cannot fetch a logo.')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="las la-info-circle me-2"></i> @lang('After adding the token, the system will automatically try to fetch price and other market data from the DexScreener API.')
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="form-group">
                        <button type="submit" class="btn btn--primary w-100 h-45">@lang('Add Token')</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.dexscreener.index') }}" class="btn btn-sm btn--primary">
        <i class="las la-list"></i> @lang('All Tokens')
    </a>
@endpush

@push('script')
<script>
    (function($) {
        "use strict";

        // You can add any additional script here if needed

    })(jQuery);
</script>
@endpush