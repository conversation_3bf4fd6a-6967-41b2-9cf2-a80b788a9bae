<?php

namespace App\Http\Controllers\Gateway;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Lib\FormProcessor;
use App\Models\AdminNotification;
use App\Models\Deposit;
use App\Models\GatewayCurrency;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public function payment($id)
    {
        abort(404);
    }


    public function depositInsert(Request $request)
    {
        $request->validate([
            'amount'   => 'required|numeric|gt:0',
            'gateway'  => 'required',
            'currency' => 'required',
        ]);

        $amount       = $request->amount;
        $user = auth()->user();

        // Allow only specific gateways
        $allowedGateways = [101, 104, 503, 506, 504]; // Paypal Express, 2Checkout, CoinPayments, Coinbase Commerce, CoinPayments Fiat
        $allowedAliases = ['paypal', 'twocheckout', 'coinpayments', 'coinbasecommerce', 'coinpaymentsfiat'];

        $gate = GatewayCurrency::whereHas('method', function ($gate) use ($allowedGateways, $allowedAliases) {
            $gate->where('status', Status::ENABLE)
                 ->where(function($query) use ($allowedGateways, $allowedAliases) {
                     $query->whereIn('code', $allowedGateways)
                           ->orWhereIn('alias', $allowedAliases);
                 });
        })->where('method_code', $request->gateway)->where('currency', $request->currency)->first();

        if (!$gate) {
            $notify[] = ['error', 'Invalid gateway'];
            return back()->withNotify($notify);
        }

        if ($gate->min_amount > $amount || $gate->max_amount < $amount) {
            $notify[] = ['error', 'Please follow deposit limit'];
            return back()->withNotify($notify);
        }

        $charge    = $gate->fixed_charge + ($amount * $gate->percent_charge / 100);
        $payable   = $amount + $charge;
        $finalAmount = $payable * $gate->rate;

        $data                  = new Deposit();
        $data->user_id         = $user->id;
        $data->order_id        = 0;
        $data->method_code     = $gate->method_code;
        $data->method_currency = strtoupper($gate->currency);
        $data->amount          = $request->amount;
        $data->charge          = $charge;
        $data->rate            = $gate->rate;
        $data->final_amount    = $finalAmount;
        $data->btc_amount      = 0;
        $data->btc_wallet      = "";
        $data->trx             = getTrx();
        $data->status          = Status::PAYMENT_INITIATE;
        $data->success_url     = urlPath('user.payment.history');
        $data->failed_url      = urlPath('user.payment.history');
        $data->save();
        session()->put('Track', $data->trx);
        return to_route('user.deposit.confirm');
    }


    public function appDepositConfirm($hash)
    {
        try {
            $id = decrypt($hash);
        } catch (\Exception $ex) {
            abort(404);
        }
        $data = Deposit::where('id', $id)->where('status', Status::PAYMENT_INITIATE)->orderBy('id', 'DESC')->firstOrFail();
        $user = User::findOrFail($data->user_id);
        auth()->login($user);
        session()->put('Track', $data->trx);
        return to_route('user.deposit.confirm');
    }


    public function depositConfirm()
    {
        $track = session()->get('Track');
        $deposit = Deposit::where('trx', $track)->where('status', Status::PAYMENT_INITIATE)->orderBy('id', 'DESC')->with('gateway')->firstOrFail();

        if ($deposit->method_code >= 1000) {
            return to_route('user.deposit.manual.confirm');
        }

        $dirName = $deposit->gateway->alias;
        $new = __NAMESPACE__ . '\\' . $dirName . '\\ProcessController';

        $data = $new::process($deposit);
        $data = json_decode($data);

        if (!$data) {
            $notify[] = ['error', 'Invalid payment gateway response'];
            return back()->withNotify($notify);
        }

        if (isset($data->error)) {
            $notify[] = ['error', $data->message];
            return back()->withNotify($notify);
        }

        if (isset($data->redirect)) {
            return redirect($data->redirect_url);
        }

        // for Stripe V3
        if (@$data->session) {
            $deposit->btc_wallet = $data->session->id;
            $deposit->save();
        }

        $pageTitle = 'Payment Confirm';
        return view("Template::$data->view", compact('data', 'pageTitle', 'deposit'));
    }



    public static function userDataUpdate($deposit, $isManual = null)
    {
        if ($deposit->status == Status::PAYMENT_INITIATE || $deposit->status == Status::PAYMENT_PENDING) {
            $general = gs();

            $deposit->status = Status::PAYMENT_SUCCESS;
            $deposit->save();

            $user = User::find($deposit->user_id);

            $postBalance = $user->balance;
            $postBalance += $deposit->amount;

            // Update user balance
            $oldBalance = $user->balance;
            $user->balance = $postBalance;
            $user->save();

            // Log balance update
            \Illuminate\Support\Facades\Log::channel('daily')->info('User balance updated after deposit', [
                'user_id' => $user->id,
                'username' => $user->username,
                'deposit_id' => $deposit->id,
                'deposit_amount' => $deposit->amount,
                'old_balance' => $oldBalance,
                'new_balance' => $postBalance,
                'trx' => $deposit->trx
            ]);

            $methodName = $deposit->methodName();

            $transaction               = new Transaction();
            $transaction->user_id      = $deposit->user_id;
            $transaction->amount       = $deposit->amount;
            $transaction->post_balance = $postBalance;
            $transaction->charge       = $deposit->charge;
            $transaction->trx_type     = '+';
            $transaction->details      = 'Deposit via ' . $methodName;
            $transaction->trx          = $deposit->trx;
            $transaction->currency     = $general->cur_text;
            $transaction->remark       = 'deposit';
            $transaction->save();

            // Check if this is a plan purchase and update accordingly
            if ($deposit->order_id > 0) {
                // Find the plan purchase
                $planPurchase = \App\Models\PlanPurchase::where('id', $deposit->order_id)
                    ->where('status', Status::PENDING)
                    ->first();

                if ($planPurchase) {
                    // Get the plan details
                    $plan = \App\Models\Plan::find($planPurchase->plan_id);

                    if ($plan) {
                        // Update the purchase status
                        $planPurchase->status = Status::COMPLETED;
                        $planPurchase->save();

                        // Add trend votes, promote credits, and ad credits to user
                        $user->trend_votes += $plan->trend_votes;
                        $user->promote_credits += $plan->promote_credits;
                        $user->ad_credits += $plan->ad_credits;
                        $user->save();

                        // Build transaction details based on what was purchased
                        $details = [];
                        if ($plan->trend_votes > 0) {
                            $details[] = $plan->trend_votes . ' Trend Votes';
                        }
                        if ($plan->promote_credits > 0) {
                            $details[] = $plan->promote_credits . ' Promote Credits';
                        }
                        if ($plan->ad_credits > 0) {
                            $details[] = $plan->ad_credits . ' Ad Credits';
                        }

                        // Update transaction details
                        if (count($details) > 0) {
                            $transaction->details = 'Purchase of Plan: ' . implode(' and ', $details) . ' via ' . $methodName;
                        } else {
                            $transaction->details = 'Purchase of Plan via ' . $methodName;
                        }
                        $transaction->remark = 'plan_purchase';
                        $transaction->save();

                        // Process referral commission on plan purchase
                        if (gs()->referral_system && $user->ref_by) {
                            \Illuminate\Support\Facades\Log::channel('daily')->info('GATEWAY PAYMENT: Processing referral commission', [
                                'user_id' => $user->id,
                                'username' => $user->username,
                                'ref_by' => $user->ref_by,
                                'plan_price' => $plan->price,
                                'trx' => $deposit->trx
                            ]);
                            levelCommission($user, $plan->price, $deposit->trx);
                        } else {
                            // Create direct referral records if needed (fallback mechanism)
                            if ($user->ref_by > 0) {
                                try {
                                    // Check if a commission has already been given for this user
                                    $existingCommission = \App\Models\ReferralLog::where('user_id', $user->id)
                                        ->first();

                                    if ($existingCommission) {
                                        \Illuminate\Support\Facades\Log::channel('daily')->info('GATEWAY PAYMENT: Commission already given for this user', [
                                            'user_id' => $user->id
                                        ]);
                                        return;
                                    }

                                    // Referral system might be disabled but we still want to credit
                                    $referrer = \App\Models\User::find($user->ref_by);
                                    $referralLevel = \App\Models\Referral::where('level', 1)->first();

                                    if (!$referralLevel) {
                                        $referralLevel = new \App\Models\Referral();
                                        $referralLevel->level = 1;
                                        $referralLevel->percent = 10;
                                        $referralLevel->save();
                                    }

                                    $commissionValue = ($plan->price * $referralLevel->percent) / 100;

                                    // Do NOT add to referrer balance - referral commissions are tracked via referral logs
                                    // and can only be withdrawn through the referral withdrawal system

                                    // Create transaction record for tracking purposes only
                                    $refTransaction = new \App\Models\Transaction();
                                    $refTransaction->user_id = $referrer->id;
                                    $refTransaction->amount = $commissionValue;
                                    $refTransaction->post_balance = $referrer->balance; // Balance remains unchanged
                                    $refTransaction->charge = 0;
                                    $refTransaction->trx_type = '+';
                                    $refTransaction->details = 'You have received referral commission from ' . $user->username;
                                    $refTransaction->trx = $deposit->trx . '-REF';
                                    $refTransaction->remark = 'referral_commission';
                                    $refTransaction->save();

                                    // Create referral log
                                    $refLog = new \App\Models\ReferralLog();
                                    $refLog->user_id = $user->id;
                                    $refLog->referee_id = $referrer->id;
                                    $refLog->amount = $commissionValue;
                                    $refLog->level = 1;
                                    $refLog->percent = $referralLevel->percent;
                                    $refLog->trx = $deposit->trx;
                                    $refLog->save();

                                    \Illuminate\Support\Facades\Log::channel('daily')->info('GATEWAY PAYMENT: Forced referral commission processing', [
                                        'user_id' => $user->id,
                                        'referrer_id' => $referrer->id,
                                        'amount' => $commissionValue
                                    ]);
                                } catch (\Exception $e) {
                                    \Illuminate\Support\Facades\Log::channel('daily')->error('GATEWAY PAYMENT: Forced referral processing failed', [
                                        'error' => $e->getMessage()
                                    ]);
                                }
                            } else {
                                \Illuminate\Support\Facades\Log::channel('daily')->info('GATEWAY PAYMENT: Skipping referral commission', [
                                    'referral_system_enabled' => gs()->referral_system,
                                    'user_has_referrer' => (bool)$user->ref_by,
                                    'user_id' => $user->id
                                ]);
                            }
                        }
                    }
                }
            }

            if (!$isManual) {
                $adminNotification            = new AdminNotification();
                $adminNotification->user_id   = $user->id;
                $adminNotification->title     = 'Payment successful via ' . $methodName;
                $adminNotification->click_url = urlPath('admin.payment.successful');
                $adminNotification->save();
            }

            notify($user, $isManual ? 'PAYMENT_APPROVE' : 'PAYMENT_COMPLETE', [
                'method_name'     => $methodName,
                'method_currency' => $deposit->method_currency,
                'method_amount'   => showAmount($deposit->final_amount, currencyFormat:false),
                'amount'          => showAmount($deposit->amount, currencyFormat:false),
                'charge'          => showAmount($deposit->charge, currencyFormat:false),
                'rate'            => showAmount($deposit->rate, currencyFormat:false),
                'trx'             => $deposit->trx,
            ]);
        }
    }

    public function manualDepositConfirm()
    {
        $track = session()->get('Track');
        $data = Deposit::with('gateway')->where('status', Status::PAYMENT_INITIATE)->where('trx', $track)->first();
        abort_if(!$data, 404);
        if ($data->method_code > 999) {
            $pageTitle = 'Confirm Payment';
            $method = $data->gatewayCurrency();
            $gateway = $method->method;
            return view('Template::user.payment.manual', compact('data', 'pageTitle', 'method', 'gateway'));
        }
        abort(404);
    }

    public function manualDepositUpdate(Request $request)
    {
        $track   = session()->get('Track');
        $deposit = Deposit::with('gateway', 'order')->where('status', Status::PAYMENT_INITIATE)->where('trx', $track)->first();

        if (!$deposit) {
            abort(404);
        }

        $order = $deposit->order;

        $gatewayCurrency = $deposit->gatewayCurrency();
        $gateway         = $gatewayCurrency->method;
        $formData        = $gateway->form->form_data;

        $formProcessor  = new FormProcessor();
        $validationRule = $formProcessor->valueValidation($formData);
        $request->validate($validationRule);
        $userData = $formProcessor->processFormData($request, $formData);

        $deposit->detail = $userData;
        $deposit->status = Status::PAYMENT_PENDING; // pending
        $deposit->save();

        $adminNotification            = new AdminNotification();
        $adminNotification->user_id   = $deposit->user->id;
        $adminNotification->title     = 'Payment request form ' . $deposit->user->username;
        $adminNotification->click_url = urlPath('admin.payment.details', $deposit->id);
        $adminNotification->save();

        if ($order) {
            $order->status = Status::ORDER_PENDING; //pending
            $order->save();

            $short_code = [
                'method_name'     => $deposit->gatewayCurrency()->name,
                'method_currency' => $deposit->method_currency,
                'method_amount'   => showAmount($deposit->final_amount),
                'amount'          => showAmount($deposit->amount),
                'charge'          => showAmount($deposit->charge),
                'rate'            => showAmount($deposit->rate),
                'trx'             => $deposit->trx,
            ];

            notify($deposit->user, 'PAYMENT_REQUEST', $short_code);
        }

        $notify[] = ['success', 'Your payment request has been taken'];
        return to_route('user.plans.purchased')->withNotify($notify);
    }
}
