@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="custom--card">
            <div class="card-header">
                <h5 class="card-title">@lang('Ad Banners')</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-info" style="background-color: rgba(49, 215, 169, 0.1); border-color: #31D7A9; color: #B9BABB;">
                            <p class="mb-0">@lang('Select an ad banner position to promote your project. Current ad credits: ') <strong>{{ auth()->user()->ad_credits }}</strong></p>
                        </div>
                    </div>
                </div>

                <div class="row gy-4">
                    @foreach($adPositions as $position)
                    <div class="col-xl-4 col-md-6">
                        <div class="price-item">
                            <div class="price-item__header">
                                <h4 class="price-item__title">{{ $position->name }}</h4>
                            </div>
                            <div class="price-item__content">
                                <div class="price-item__body">
                                    <p class="mb-2">
                                        <span class="preview-position-btn" style="cursor: pointer; color: #BE8400; text-decoration: underline;" data-position-key="{{ $position->key }}">
                                            <i class="las la-eye"></i> @lang('Preview position')
                                        </span>
                                    </p>
                                    <p>{{ $position->size }}</p>
                                </div>
                                <div class="price-item__button">
                                    <a href="javascript:void(0)" data-position-id="{{ $position->id }}" class="btn--base banner-select-btn">@lang('Select')</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                @if(count($userBanners) > 0)
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="alert alert-success" style="background-color: rgba(49, 215, 169, 0.1); border-color: #31D7A9; color: #B9BABB;">
                            <p class="mb-0">@lang('You have active banner ads. Go to ') <a href="{{ route('user.manage.ads') }}" class="text--base">@lang('Manage Ads')</a> @lang(' to view and update them.')</p>
                        </div>
                    </div>
                </div>
                @endif

                @if(count($adPositions) == 0)
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <p class="mb-0">@lang('No ad positions are currently available.')</p>
                        </div>
                    </div>
                </div>
                @endif

                <div class="row mt-5">
                    <div class="col-md-12">
                        <div class="custom--card">
                            <div class="card-header">
                                <h5 class="card-title">@lang('Need More Ad Credits?')</h5>
                            </div>
                            <div class="card-body">
                                <p>@lang('Purchase ad credits to promote your project or service on our platform.')</p>
                                <div class="mt-3">
                                    <a href="{{ route('user.plans.buy.ad.credits') }}" class="btn--base">@lang('Buy Ad Credits')</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- No Ad Credits Modal -->
<div class="modal custom--modal fade" id="noAdCreditsModal" tabindex="-1" aria-labelledby="noAdCreditsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff;">
                <h5 class="modal-title" id="noAdCreditsModalLabel">@lang('Ad Credits Required')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <i class="las la-exclamation-circle fs-1" style="color: #BE8400;"></i>
                <p class="mt-3">@lang('You do not have enough ad credits to create a banner ad. Please purchase ad credits to continue.')</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                <a href="{{ route('user.plans.buy.ad.credits') }}" class="btn btn--base">@lang('Buy Ad Credits')</a>
            </div>
        </div>
    </div>
</div>
<!-- Preview Position Modal -->
<div class="modal custom--modal fade" id="previewPositionModal" tabindex="-1" aria-labelledby="previewPositionModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="max-width: 1300px; width: 90%;">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff;">
                <h5 class="modal-title" id="previewPositionModalLabel">@lang('Ad Position Preview')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <img id="positionPreviewImage" src="" alt="@lang('Position Preview')" style="width: auto; height: auto; max-width: 100%;" class="img-fluid">
                <p class="mt-3" id="positionPreviewText">@lang('This is how your ad will appear in this position.')</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
            </div>
        </div>
    </div>
</div>

<!-- Active Ad Error Modal -->
<div class="modal custom--modal fade" id="activeAdErrorModal" tabindex="-1" aria-labelledby="activeAdErrorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff;">
                <h5 class="modal-title" id="activeAdErrorModalLabel">@lang('Active Ad Exists')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <i class="las la-exclamation-circle fs-1" style="color: #BE8400;"></i>
                <p class="mt-3">@lang('You already have an active or pending ad in this location. You cannot have multiple ads in the same location.')</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
                <a href="{{ route('user.manage.ads') }}" class="btn btn--base">@lang('Manage Ads')</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
    /* Override the dots before list items in plans */
    .text-list__item::before,
    .text-list__item::after {
        display: none !important;
    }

    /* Add padding for better alignment without the dots */
    .text-list__item {
        padding-left: 0 !important;
    }

    /* Style for the preview position button */
    .preview-position-btn {
        color: #BE8400;
        margin-right: 5px;
        transition: all 0.3s;
    }

    .preview-position-btn:hover {
        color: #ffffff;
    }

    .preview-position-btn i {
        font-size: 16px;
    }
</style>
@endpush

@push('script')
<script>
    (function($) {
        "use strict";

        console.log('Ad banners script loaded');

        // Banner select button click handler
        $('.banner-select-btn').on('click', function() {
            var positionId = $(this).data('position-id');
            var adCredits = {{ auth()->user()->ad_credits }};

            if (adCredits <= 0) {
                // Show the modal if user doesn't have enough credits
                $('#noAdCreditsModal').modal('show');
            } else {
                // Check if user already has an active ad in this position
                $.ajax({
                    url: "{{ route('user.ad.banner.check.active', '') }}/" + positionId,
                    type: 'GET',
                    success: function(response) {
                        if (response.success) {
                            // Proceed to the upload form if user doesn't have an active ad
                            window.location.href = "{{ route('user.ad.banner.upload.form', '') }}/" + positionId;
                        } else {
                            // Show error modal if user already has an active ad
                            $('#activeAdErrorModal').modal('show');
                        }
                    },
                    error: function() {
                        // If there's an error, just proceed to the form (server-side validation will catch it)
                        window.location.href = "{{ route('user.ad.banner.upload.form', '') }}/" + positionId;
                    }
                });
            }
        });

        // Preview position button click handler - using document.on for better event delegation
        $(document).on('click', '.preview-position-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Preview button clicked');
            var positionKey = $(this).data('position-key');
            console.log('Position key:', positionKey);

            // Construct the image path based on position key
            var previewImagePath = "{{ asset('assets/images/ads_images') }}/" + positionKey + "-Ad-Preview.png";
            console.log('Preview image path:', previewImagePath);

            // Set the image source in the modal
            $('#positionPreviewImage').attr('src', previewImagePath);

            // Show the modal
            var modal = $('#previewPositionModal');
            modal.modal('show');

            console.log('Modal should be visible now');

            // Fallback method if the modal doesn't show
            setTimeout(function() {
                if (!$('#previewPositionModal').hasClass('show')) {
                    console.log('Modal not showing, trying alternate method');
                    $('#previewPositionModal').modal({
                        show: true,
                        backdrop: 'static',
                        keyboard: false
                    });
                }
            }, 500);
        });

        // Make sure modal is properly initialized
        var previewModal = document.getElementById('previewPositionModal');
        if (previewModal) {
            console.log('Preview modal found in DOM');
        } else {
            console.log('Preview modal NOT found in DOM');
        }
    })(jQuery);
</script>
@endpush
