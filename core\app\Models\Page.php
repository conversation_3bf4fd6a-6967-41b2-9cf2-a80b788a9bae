<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;

class Page extends Model
{
    protected $casts = [
        'seo_content' => 'object',
        'show_in_header' => 'boolean',
        'show_in_footer' => 'boolean'
    ];

    protected $attributes = [
        'show_in_header' => true,
        'show_in_footer' => true
    ];

    public function scopeActiveTemplate($query)
    {
        return $query->where('tempname', activeTemplate());
    }

    /**
     * Get show_in_header attribute with fallback
     * 
     * @param mixed $value
     * @return bool
     */
    public function getShowInHeaderAttribute($value)
    {
        // Check if the column exists in the database
        if (!Schema::hasColumn('pages', 'show_in_header')) {
            return true;
        }
        return (bool) $value;
    }

    /**
     * Get show_in_footer attribute with fallback
     * 
     * @param mixed $value
     * @return bool
     */
    public function getShowInFooterAttribute($value)
    {
        // Check if the column exists in the database
        if (!Schema::hasColumn('pages', 'show_in_footer')) {
            return true;
        }
        return (bool) $value;
    }
}
