<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\DexscreenerToken;
use App\Models\TokenWatchlist;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TokenWatchlistController extends Controller
{
    /**
     * Check multiple tokens in the user's watchlist at once (batch operation)
     */
    public function batchCheckWatchlist(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        $request->validate([
            'tokens' => 'required|array',
            'tokens.*.chain_id' => 'required|string',
            'tokens.*.token_address' => 'required|string',
        ]);

        $tokens = $request->tokens;
        $userId = auth()->id();
        $results = [];

        // Get all user's watchlisted tokens in one query
        $watchlistedTokens = TokenWatchlist::where('user_id', $userId)->get();

        // Create a lookup map for faster checking
        $watchlistMap = [];
        foreach ($watchlistedTokens as $entry) {
            $key = $entry->chain_id . '_' . $entry->token_address;
            $watchlistMap[$key] = true;
        }

        // Check each token against the map
        foreach ($tokens as $token) {
            $chainId = $token['chain_id'];
            $tokenAddress = $token['token_address'];
            $key = $chainId . '_' . $tokenAddress;

            $results[] = [
                'chain_id' => $chainId,
                'token_address' => $tokenAddress,
                'is_watchlisted' => isset($watchlistMap[$key])
            ];
        }

        return response()->json([
            'success' => true,
            'results' => $results
        ]);
    }
    /**
     * Toggle a token in the user's watchlist
     */
    public function toggleWatchlist(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        $request->validate([
            'chain_id' => 'required|string',
            'token_address' => 'required|string',
        ]);

        $chainId = $request->chain_id;
        $tokenAddress = $request->token_address;
        $userId = auth()->id();

        // Verify token exists
        $token = DexscreenerToken::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->first();

        if (!$token) {
            return response()->json(['error' => 'Token not found'], 404);
        }

        try {
            // Check if token is already in watchlist
            $watchlistEntry = TokenWatchlist::where('user_id', $userId)
                ->where('chain_id', $chainId)
                ->where('token_address', $tokenAddress)
                ->first();

            // Check if this is a remove request
            $isRemoveRequest = $request->has('remove') && $request->remove == 1;

            if ($watchlistEntry && $isRemoveRequest) {
                // Remove from watchlist (only if explicitly requested)
                $watchlistEntry->delete();
                return response()->json([
                    'success' => true,
                    'message' => 'Token removed from watchlist',
                    'is_watchlisted' => false
                ]);
            } else if ($watchlistEntry) {
                // Token already in watchlist
                return response()->json([
                    'success' => true,
                    'message' => 'Token already in watchlist',
                    'is_watchlisted' => true,
                    'already_exists' => true
                ]);
            } else {
                // Add to watchlist
                TokenWatchlist::create([
                    'user_id' => $userId,
                    'chain_id' => $chainId,
                    'token_address' => $tokenAddress,
                ]);
                return response()->json([
                    'success' => true,
                    'message' => 'Token added to watchlist',
                    'is_watchlisted' => true
                ]);
            }
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Watchlist toggle error: ' . $e->getMessage());

            // Check if the token was added despite the error
            $isWatchlisted = TokenWatchlist::where('user_id', $userId)
                ->where('chain_id', $chainId)
                ->where('token_address', $tokenAddress)
                ->exists();

            return response()->json([
                'success' => true,
                'message' => $isWatchlisted ? 'Token added to watchlist' : 'Token removed from watchlist',
                'is_watchlisted' => $isWatchlisted
            ]);
        }
    }

    /**
     * Check if a token is in the user's watchlist
     */
    public function checkWatchlist(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        $request->validate([
            'chain_id' => 'required|string',
            'token_address' => 'required|string',
        ]);

        $chainId = $request->chain_id;
        $tokenAddress = $request->token_address;
        $userId = auth()->id();

        // Check if token is in watchlist
        $isWatchlisted = TokenWatchlist::where('user_id', $userId)
            ->where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->exists();

        return response()->json([
            'success' => true,
            'is_watchlisted' => $isWatchlisted
        ]);
    }

    /**
     * Display user's watchlisted tokens
     */
    public function myWatchlist()
    {
        $pageTitle = 'My Watchlist';

        // Get all watchlisted tokens for the current user
        $watchlistEntries = TokenWatchlist::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->get();

        // Collect token data for each watchlisted token
        $tokensCollection = collect();
        foreach ($watchlistEntries as $entry) {
            $token = DexscreenerToken::where('chain_id', $entry->chain_id)
                ->where('token_address', $entry->token_address)
                ->first();

            if ($token) {
                // Format token data
                $tokensCollection->push([
                    'id' => $token->id,
                    'chain_id' => $token->chain_id,
                    'token_address' => $token->token_address,
                    'token_name' => $token->token_name,
                    'token_symbol' => $token->token_symbol,
                    'price_usd' => is_numeric($token->price_usd) ? $token->price_usd : 0,
                    'price_change_24h' => is_numeric($token->price_change_24h) ? $token->price_change_24h : 0,
                    'image_url' => $token->image_url,
                    'added_at' => $entry->created_at->format('Y-m-d H:i:s'),
                    'token_details_url' => route('token.details', ['chainId' => $token->chain_id, 'tokenAddress' => $token->token_address])
                ]);
            }
        }

        // Paginate the collection manually
        $page = request()->input('page', 1);
        $perPage = 10;
        $offset = ($page - 1) * $perPage;

        // Get the paginated items
        $items = $tokensCollection->slice($offset, $perPage)->all();

        // Create a new paginator instance
        $tokens = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $tokensCollection->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return view('Template::user.token.watchlist', compact('pageTitle', 'tokens'));
    }
}
