<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GoPlusSecurityService
{
    /**
     * Base URL for the GoPlus Security API
     */
    protected $baseUrl = 'https://api.gopluslabs.io/api/v1/token_security';



    /**
     * Get token security information from GoPlus API
     *
     * @param string $chainId
     * @param string $tokenAddress
     * @return array|null
     */
    public function getTokenSecurity($chainId, $tokenAddress)
    {
        try {
            // Convert chain ID to GoPlus chain ID format
            $goPlusChainId = $this->convertChainId($chainId);

            if (!$goPlusChainId) {
                Log::error('Unsupported chain ID for GoPlus API', [
                    'chainId' => $chainId
                ]);
                return null;
            }

            // Format token address according to chain requirements
            $tokenAddress = $this->formatTokenAddress($goPlusChainId, $tokenAddress);

            // Construct the URL with the correct format
            $url = "{$this->baseUrl}/{$goPlusChainId}?contract_addresses={$tokenAddress}";

            // Make the API call with Laravel's HTTP client
            $response = Http::timeout(15)
                ->withoutVerifying()
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept' => 'application/json',
                    'Cache-Control' => 'no-cache'
                ])
                ->get($url);

            if ($response->successful()) {
                $data = $response->json();

                // Process the response
                if (is_array($data) && isset($data['result'])) {
                    // The result field contains the token data
                    if (isset($data['result'][$tokenAddress])) {
                        return $data['result'][$tokenAddress];
                    } else {
                        return $data['result'];
                    }
                }
            }

            // If Laravel HTTP client failed, try with curl as a fallback
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: application/json',
                'Cache-Control: no-cache'
            ]);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200 && !empty($result)) {
                $data = json_decode($result, true);

                if (is_array($data) && isset($data['result'])) {
                    // The result field contains the token data
                    if (isset($data['result'][$tokenAddress])) {
                        return $data['result'][$tokenAddress];
                    } else {
                        return $data['result'];
                    }
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while fetching token security info from GoPlus API', [
                'message' => $e->getMessage(),
                'chainId' => $chainId,
                'tokenAddress' => $tokenAddress
            ]);

            return null;
        }
    }

    /**
     * Convert chain ID to GoPlus chain ID format
     *
     * @param string $chainId
     * @return string|null
     */
    private function convertChainId($chainId)
    {
        // Chain ID mapping based on GoPlus API documentation
        $chainMapping = [
            // Main networks
            'ethereum' => '1',      // Ethereum
            'eth' => '1',           // Alternative name for Ethereum
            'bsc' => '56',          // BNB Chain
            'bnb' => '56',          // Alternative name for BNB Chain
            'polygon' => '137',     // Polygon
            'matic' => '137',       // Alternative name for Polygon
            'arbitrum' => '42161',  // Arbitrum
            'arb' => '42161',       // Alternative name for Arbitrum
            'optimism' => '10',     // Optimism
            'op' => '10',           // Alternative name for Optimism
            'avalanche' => '43114', // Avalanche
            'avax' => '43114',      // Alternative name for Avalanche
            'fantom' => '250',      // Fantom
            'ftm' => '250',         // Alternative name for Fantom
            'cronos' => '25',       // Cronos
            'cro' => '25',          // Alternative name for Cronos
            'okc' => '66',          // OKC
            'heco' => '128',        // HECO
            'tron' => 'tron',       // Tron
            'trx' => 'tron',        // Alternative name for Tron
            'kcc' => '321',         // KCC

            // Layer 2 and other networks
            'zksync' => '324',      // zkSync Era
            'zksyncera' => '324',   // Alternative name for zkSync Era
            'linea' => '59144',     // Linea Mainnet
            'base' => '8453',       // Base
            'scroll' => '534352',   // Scroll
            'gnosis' => '100',      // Gnosis
            'xdai' => '100',        // Alternative name for Gnosis
            'ethw' => '10001',      // ETHW
            'fon' => '201022',      // FON
            'mantle' => '5000',     // Mantle
            'opbnb' => '204',       // opBNB
            'zkfair' => '42766',    // ZKFair
            'blast' => '81457',     // Blast
            'manta' => '169',       // Manta Pacific
            'mantapacific' => '169',// Alternative name for Manta Pacific
            'berachain' => '80094', // Berachain
            'bera' => '80094',      // Alternative name for Berachain
            'abstract' => '2741',   // Abstract
            'hashkey' => '177',     // Hashkey Chain
            'hashkeychain' => '177',// Alternative name for Hashkey Chain
            'sonic' => '146',       // Sonic
            'story' => '1514'       // Story
        ];

        // If the input is already a numeric chain ID, return it directly
        if (is_numeric($chainId)) {
            return (string)$chainId;
        }

        // Try to get the chain ID from the mapping
        return $chainMapping[strtolower($chainId)] ?? null;
    }

    /**
     * Format token address according to chain requirements
     *
     * @param string $chainId
     * @param string $tokenAddress
     * @return string
     */
    private function formatTokenAddress($chainId, $tokenAddress)
    {
        // For EVM chains (Ethereum, BSC, etc.), convert to lowercase
        if ($chainId !== 'solana' && $chainId !== 'tron') {
            return strtolower($tokenAddress);
        }

        // For Solana and Tron, keep the original format
        return $tokenAddress;
    }
}
