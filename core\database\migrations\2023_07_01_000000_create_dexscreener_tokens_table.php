<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dexscreener_tokens', function (Blueprint $table) {
            $table->id();
            $table->string('chain_id');
            $table->string('token_address');
            $table->string('token_name');
            $table->string('token_symbol');
            $table->string('price_usd')->nullable();
            $table->string('price_change_24h')->nullable();
            $table->decimal('volume_24h', 18, 8)->nullable();
            $table->decimal('market_cap', 18, 8)->nullable();
            $table->decimal('liquidity_usd', 18, 8)->nullable();
            $table->json('metadata')->nullable();
            $table->string('pair_address')->nullable();
            $table->string('dex_id')->nullable();
            $table->string('image_url')->nullable();
            $table->timestamps();
            
            $table->unique(['chain_id', 'token_address']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dexscreener_tokens');
    }
}; 