@extends('admin.layouts.app')
@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('Username')</th>
                                <th>@lang('Token Name')</th>
                                <th>@lang('Token Symbol')</th>
                                <th>@lang('Amount')</th>
                                <th>@lang('Tokens')</th>
                                <th>@lang('User Token Address')</th>
                                <th>@lang('Token Transfer')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($userPurchases as $purchase)
                                <tr>
                                    <td>
                                        <span class="fw-bold">
                                            <a href="{{ route('admin.users.detail', $purchase['user_id']) }}">{{ @$purchase['user']->username }}</a>
                                        </span>
                                    </td>
                                    <td>{{ $purchase['token_name'] }}</td>
                                    <td>{{ $purchase['token_symbol'] }}</td>
                                    <td>{{ showAmount($purchase['total_amount']) }} USD</td>
                                    <td>{{ showAmount($purchase['total_tokens'], exceptZeros: true, currencyFormat: false) }}</td>
                                    <td>
                                        @if(@$purchase['user']->ccl_token_address)
                                            <span class="text-break">{{ @$purchase['user']->ccl_token_address }}</span>
                                        @else
                                            <span class="text-muted">@lang('Not provided')</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if(isset($purchase['token_transfer_status']) && $purchase['token_transfer_status'] == \App\Models\UserTokenPurchase::STATUS_COMPLETED)
                                            <span style="color: green;">Completed</span>
                                        @else
                                            <form action="{{ route('admin.presales.purchases.complete') }}" method="POST" class="d-inline">
                                                @csrf
                                                <input type="hidden" name="user_id" value="{{ $purchase['user_id'] }}">
                                                <input type="hidden" name="token_symbol" value="{{ $purchase['token_symbol'] }}">
                                                <button type="submit" class="btn btn-sm btn--danger">
                                                    <span style="color: white;">Pending</span>
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage ?? 'No purchases found') }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($userPurchases->hasPages())
                <div class="card-footer py-4">
                    {{ paginateLinks($userPurchases) }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<div class="d-flex flex-wrap justify-content-end gap-2">
    <form action="{{ request()->route()->getName() == 'admin.presales.purchases.completed' ? route('admin.presales.purchases.completed') : route('admin.presales.purchases') }}" method="GET" class="d-flex gap-2">
        <div class="input-group">
            <input type="text" name="search" class="form-control" placeholder="Username / Token Name / Token Symbol / User Token Address" value="{{ $search ?? '' }}">
            <button class="btn btn--primary input-group-text"><i class="la la-search"></i></button>
        </div>
    </form>
    @if(isset($search) && $search != '')
    <a href="{{ request()->route()->getName() == 'admin.presales.purchases.completed' ? route('admin.presales.purchases.completed') : route('admin.presales.purchases') }}" class="btn btn-sm btn--dark">
        <i class="las la-undo"></i> @lang('Reset')
    </a>
    @endif
    <a href="{{ route('admin.presales.purchases.export', ['search' => $search ?? '', 'completed' => request()->route()->getName() == 'admin.presales.purchases.completed' ? 1 : 0]) }}" class="btn btn-sm btn--success">
        <i class="las la-download"></i> @lang('Export CSV')
    </a>
    <a href="{{ route('admin.presales.index') }}" class="btn btn-sm btn--primary">
        <i class="las la-list"></i> @lang('Manage Presales')
    </a>
</div>
@endpush
