<?php

namespace App\Models;

use App\Constants\Status;
use Illuminate\Database\Eloquent\Model;

class PlanPurchase extends Model
{
    protected $guarded = ['id'];
    
    protected $casts = [
        'price' => 'decimal:2'
    ];

    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', Status::ENABLE);
    }

    public function scopePending($query)
    {
        return $query->where('status', Status::PENDING);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', Status::COMPLETED);
    }
} 