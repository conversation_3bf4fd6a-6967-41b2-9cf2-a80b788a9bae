<?php

namespace App\Services;

use App\Models\DexscreenerToken;
use App\Models\PopularCronUpdate;
use App\Models\PresaleCronUpdate;
use App\Models\SubmitCoin;
use App\Models\UnpopularCronUpdate;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class DexscreenerService
{
    /**
     * Base URL for the Dexscreener API
     */
    protected $baseUrl = 'https://api.dexscreener.com';

    /**
     * Base URL for the Dexscreener DEX API endpoints
     */
    protected $dexBaseUrl = 'https://api.dexscreener.com/latest/dex';

    /**
     * Get the latest boosted tokens from the Dexscreener API
     */
    public function getBoostedTokens()
    {
        try {
            // Log the API call for debugging
            Log::info('Fetching boosted tokens from DexScreener API', [
                'url' => $this->baseUrl . '/token-boosts/top/v1'
            ]);

            // Set a reasonable timeout for the API call (5 seconds)
            $response = Http::timeout(5)
                ->withHeaders([
                    'User-Agent' => 'CryptoWebsite/1.0',
                    'Accept' => 'application/json'
                ])
                ->get($this->baseUrl . '/token-boosts/top/v1');

            if ($response->successful()) {
                $data = $response->json();
                Log::info('Successfully fetched tokens from DexScreener API', [
                    'count' => is_array($data) ? count($data) : 0
                ]);
                return $data;
            }

            Log::error('Failed to fetch boosted tokens from Dexscreener API', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            // Try alternative API endpoint if the first one fails
            Log::info('Trying alternative endpoint for top tokens');
            $response = Http::timeout(5)->get($this->baseUrl . '/search?q=top');

            if ($response->successful() && isset($response->json()['pairs'])) {
                $pairs = $response->json()['pairs'];
                $tokens = [];

                // Convert pairs to token format
                foreach ($pairs as $pair) {
                    if (isset($pair['chainId']) && isset($pair['baseToken']['address'])) {
                        $tokens[] = [
                            'chainId' => $pair['chainId'],
                            'tokenAddress' => $pair['baseToken']['address']
                        ];
                    }
                }

                Log::info('Successfully fetched tokens from alternative endpoint', [
                    'count' => count($tokens)
                ]);

                return $tokens;
            }

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching boosted tokens from Dexscreener API', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [];
        }
    }

    /**
     * Get token pairs for a specific token on a specific chain
     */
    public function getTokenPairs($chainId, $tokenAddress)
    {
        try {
            // Log the API call
            Log::info('Fetching token pairs', [
                'url' => $this->dexBaseUrl . "/pairs/{$chainId}/{$tokenAddress}",
                'chainId' => $chainId,
                'tokenAddress' => $tokenAddress
            ]);

            // Set a reasonable timeout for the API call
            $response = Http::timeout(10)
                ->withHeaders([
                    'User-Agent' => 'CryptoWebsite/1.0',
                    'Accept' => 'application/json'
                ])
                ->get($this->dexBaseUrl . "/search?q={$tokenAddress}");

            if ($response->successful()) {
                $data = $response->json();
                $pairs = $data['pairs'] ?? [];

                Log::info('Successfully fetched token pairs', [
                    'count' => count($pairs)
                ]);

                return $pairs;
            }

            Log::error('Failed to fetch token pairs from Dexscreener API', [
                'status' => $response->status(),
                'body' => $response->body(),
                'chainId' => $chainId,
                'tokenAddress' => $tokenAddress
            ]);

            // Try to fetch token information directly if pairs don't work
            // Log::info('Trying to fetch token directly', [
            //     'url' => $this->baseUrl . "/tokens/v1/{$chainId}/{$tokenAddress}"
            // ]);

            // $tokenResponse = Http::timeout(10)->get($this->baseUrl . "/tokens/v1/{$chainId}/{$tokenAddress}");

            // if ($tokenResponse->successful() && isset($tokenResponse->json()['pairs'])) {
            //     $pairs = $tokenResponse->json()['pairs'];
            //     Log::info('Successfully fetched token directly', [
            //         'count' => count($pairs)
            //     ]);
            //     return $pairs;
            // }

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching token pairs from Dexscreener API', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'chainId' => $chainId,
                'tokenAddress' => $tokenAddress
            ]);

            return [];
        }
    }

    /**
     * Get multiple tokens at once
     */
    public function getTokens($chainId, array $tokenAddresses)
    {
        try {
            $addresses = implode(',', $tokenAddresses);
            $response = Http::get($this->baseUrl . "/tokens/v1/{$chainId}/{$addresses}");

            if ($response->successful()) {
                return $response->json() ?? [];
            }

            Log::error('Failed to fetch tokens from Dexscreener API', [
                'status' => $response->status(),
                'body' => $response->body(),
                'chainId' => $chainId,
                'tokenAddresses' => $addresses
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching tokens from Dexscreener API', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'chainId' => $chainId,
                'tokenAddresses' => implode(',', $tokenAddresses)
            ]);

            return [];
        }
    }

    // tokens with votes
    public function refreshPopularTokens()
    {
        try {
            ini_set('max_execution_time', 240);

            $popularTokensCount = PopularCronUpdate::count();

            Log::info('Starting popular tokens refresh, Popular tokens count: ' . $popularTokensCount);

            if ($popularTokensCount > 0) {
                $popularTokenIds = PopularCronUpdate::limit(250)->pluck('dexscreener_token_id')->toArray();
                Log::info('Fetched ' . count($popularTokenIds) . ' popular tokens from queue');
                $popularTokens = DexscreenerToken::whereIn('id', $popularTokenIds)
                    ->where(function ($query) {
                        $query->where('token_address', 'not like', 'presale_%')
                              ->where('token_address', 'not like', 'fairlaunch_%');
                    })->get();
            } else {

                // Get tokens with votes
                DexscreenerToken::select([
                        'dexscreener_tokens.*',
                        DB::raw('(
                            SELECT COUNT(*) 
                            FROM token_votes 
                            WHERE token_votes.chain_id COLLATE utf8mb4_unicode_ci = dexscreener_tokens.chain_id COLLATE utf8mb4_unicode_ci
                            AND token_votes.token_address COLLATE utf8mb4_unicode_ci = dexscreener_tokens.token_address COLLATE utf8mb4_unicode_ci
                            AND is_negative = 0
                        ) + COALESCE(dexscreener_tokens.trend_votes, 0) as total_vote_count')
                    ])
                    ->where(function($query) {
                        $query->where('is_verified', true)
                              ->orWhereNull('submitted_by_user_id'); // Admin added tokens have null user_id
                    })
                    ->where(function ($query) {
                        $query->where('token_address', 'not like', 'presale_%')
                              ->where('token_address', 'not like', 'fairlaunch_%');
                    })
                    ->having('total_vote_count', '>', 0) // Only get tokens with votes
                    ->orderByRaw('total_vote_count DESC, created_at DESC')
                    ->chunk(1000, function ($tokens) {
                        $insertData = $tokens->map(fn($token) => ['dexscreener_token_id' => $token->id])->toArray();
                        PopularCronUpdate::insertOrIgnore($insertData);
                    });

                $popularTokenIds = PopularCronUpdate::limit(250)->pluck('dexscreener_token_id')->toArray();
                $popularTokens = DexscreenerToken::whereIn('id', $popularTokenIds)
                    ->where(function ($query) {
                        $query->where('token_address', 'not like', 'presale_%')
                              ->where('token_address', 'not like', 'fairlaunch_%');
                    })->get();
            }

            PopularCronUpdate::whereIn('dexscreener_token_id', $popularTokenIds)->delete();

            $updatedCount = 0;

            try {
                // update popular tokens
                foreach ($popularTokens as $token) {
                    // Fetch token details from API
                    Log::info("Updating popular token: {$token->token_symbol} ({$token->chain_id}/{$token->token_address})");

                    $tokenData = $this->fetchTokenDetails($token->chain_id, $token->token_address);

                    if ($tokenData && isset($tokenData['price_usd'])) {
                        // Update the token with the latest price data
                        $updated = DexscreenerToken::where('id', $token->id)->update([
                            'price_usd' => $tokenData['price_usd'],
                            'price_change_24h' => $tokenData['price_change_24h'] ?? $token->price_change_24h,
                            'price_change_5m' => $tokenData['price_change_5m'] ?? $token->price_change_5m,
                            'price_change_1h' => $tokenData['price_change_1h'] ?? $token->price_change_1h,
                            'price_change_6h' => $tokenData['price_change_6h'] ?? $token->price_change_6h,
                            'volume_24h' => $tokenData['volume_24h'] ?? $token->volume_24h,
                            'market_cap' => $tokenData['market_cap'] ?? $token->market_cap,
                            'liquidity_usd' => $tokenData['liquidity_usd'] ?? $token->liquidity_usd,
                            'txn_24h' => $tokenData['txn_24h'] ?? $token->txn_24h,
                            'buy_count' => $tokenData['buy_count'] ?? $token->buy_count,
                            'sell_count' => $tokenData['sell_count'] ?? $token->sell_count,
                            'token_age' => $tokenData['token_age'] ?? null,
                            'metadata' => $tokenData['metadata'] ?? $token->metadata,
                            'pair_address' => $tokenData['pair_address'] ?? $token->pair_address,
                            'dex_id' => $tokenData['dex_id'] ?? $token->dex_id,
                            'image_url' => $tokenData['image_url'] ?? $token->image_url,
                            'updated_at' => now(),
                        ]);

                        $updatedCount++;
                        Log::info("Updated popular token #{$updatedCount}: {$token->token_symbol}, Update successful: {$updated}");
                    } else {
                        Log::warning("Failed to update token {$token->token_symbol} - no data returned from API");
                    }
                }

                Log::info("Updated {$updatedCount} popular tokens total");

            } catch (\Exception $e) {
                Log::error("Error fetching tokens", [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Clear cache
            Cache::forget('dexscreener_tokens');

            return true;
        } catch (\Exception $e) {
            Log::error('Exception while refreshing token data', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }
    
    // tokens without votes
    public function refreshUnpopularTokens()
    {
        try {
            ini_set('max_execution_time', 240);

            $unpopularTokensCount = UnpopularCronUpdate::count();

            Log::info('Starting unpopular tokens refresh, Unpopular tokens count: ' . $unpopularTokensCount);

            if ($unpopularTokensCount > 0) {
                $unpopularTokenIds = UnpopularCronUpdate::limit(250)->pluck('dexscreener_token_id')->toArray();
                Log::info('Fetched ' . count($unpopularTokenIds) . ' unpopular tokens from queue');
                $unpopularTokens = DexscreenerToken::whereIn('id', $unpopularTokenIds)
                    ->where(function ($query) {
                        $query->where('token_address', 'not like', 'presale_%')
                              ->where('token_address', 'not like', 'fairlaunch_%');
                    })->get();
            } else {
                // Get tokens without votes
                DexscreenerToken::select([
                        'dexscreener_tokens.*',
                        DB::raw('(
                            SELECT COUNT(*) 
                            FROM token_votes 
                            WHERE token_votes.chain_id COLLATE utf8mb4_unicode_ci = dexscreener_tokens.chain_id COLLATE utf8mb4_unicode_ci
                            AND token_votes.token_address COLLATE utf8mb4_unicode_ci = dexscreener_tokens.token_address COLLATE utf8mb4_unicode_ci
                            AND is_negative = 0
                        ) + COALESCE(dexscreener_tokens.trend_votes, 0) as total_vote_count')
                    ])
                    ->where(function($query) {
                        $query->where('is_verified', true)
                              ->orWhereNull('submitted_by_user_id'); // Admin added tokens have null user_id
                    })
                    ->where(function ($query) {
                        $query->where('token_address', 'not like', 'presale_%')
                              ->where('token_address', 'not like', 'fairlaunch_%');
                    })
                    ->having('total_vote_count', '=', 0) // Only get tokens with no votes
                    ->orderBy('created_at', 'DESC')
                    ->chunk(1000, function ($tokens) {
                        $insertData = $tokens->map(fn($token) => ['dexscreener_token_id' => $token->id])->toArray();
                        UnpopularCronUpdate::insertOrIgnore($insertData);
                    });

                $unpopularTokenIds = UnpopularCronUpdate::limit(250)->pluck('dexscreener_token_id')->toArray();
                $unpopularTokens = DexscreenerToken::whereIn('id', $unpopularTokenIds)
                    ->where(function ($query) {
                        $query->where('token_address', 'not like', 'presale_%')
                              ->where('token_address', 'not like', 'fairlaunch_%');
                    })->get();
            }

            UnpopularCronUpdate::whereIn('dexscreener_token_id', $unpopularTokenIds)->delete();

            $updatedCount = 0;

            try {
                // update unpopular tokens
                foreach ($unpopularTokens as $token) {
                    // Fetch token details from API
                    Log::info("Updating unpopular token: {$token->token_symbol} ({$token->chain_id}/{$token->token_address})");

                    $tokenData = $this->fetchTokenDetails($token->chain_id, $token->token_address);

                    if ($tokenData && isset($tokenData['price_usd'])) {
                        // Update the token with the latest price data
                        $updated = DexscreenerToken::where('id', $token->id)->update([
                            'price_usd' => $tokenData['price_usd'],
                            'price_change_24h' => $tokenData['price_change_24h'] ?? $token->price_change_24h,
                            'price_change_5m' => $tokenData['price_change_5m'] ?? $token->price_change_5m,
                            'price_change_1h' => $tokenData['price_change_1h'] ?? $token->price_change_1h,
                            'price_change_6h' => $tokenData['price_change_6h'] ?? $token->price_change_6h,
                            'volume_24h' => $tokenData['volume_24h'] ?? $token->volume_24h,
                            'market_cap' => $tokenData['market_cap'] ?? $token->market_cap,
                            'liquidity_usd' => $tokenData['liquidity_usd'] ?? $token->liquidity_usd,
                            'txn_24h' => $tokenData['txn_24h'] ?? $token->txn_24h,
                            'buy_count' => $tokenData['buy_count'] ?? $token->buy_count,
                            'sell_count' => $tokenData['sell_count'] ?? $token->sell_count,
                            'token_age' => $tokenData['token_age'] ?? null,
                            'metadata' => $tokenData['metadata'] ?? $token->metadata,
                            'pair_address' => $tokenData['pair_address'] ?? $token->pair_address,
                            'dex_id' => $tokenData['dex_id'] ?? $token->dex_id,
                            'image_url' => $tokenData['image_url'] ?? $token->image_url,
                            'updated_at' => now(),
                        ]);

                        $updatedCount++;
                        Log::info("Updated unpopular token #{$updatedCount}: {$token->token_symbol}, Update successful: {$updated}");
                    } else {
                        Log::warning("Failed to update token {$token->token_symbol} - no data returned from API");
                    }
                }

                Log::info("Updated {$updatedCount} unpopular tokens total");

            } catch (\Exception $e) {
                Log::error("Error fetching tokens", [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Clear cache
            Cache::forget('dexscreener_tokens');

            return true;
        } catch (\Exception $e) {
            Log::error('Exception while refreshing token data', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Refresh token data from the API and store in the database
     *
     * @param string|null $searchTerm Optional search term to use instead of default 'top'
     * @return bool
     */
    public function refreshTokenData()
    {
        try {
            // Get admin token limit setting
            // $general = \App\Models\GeneralSetting::first();
            // $tokenLimit = $general->dexscreener_tokens_count ?? 10;

            // Log::info("Starting token refresh for {$tokenLimit} tokens");

            $updatedCount = 0;
            // $addedCount = 0;

            try {
                // First, get some popular tokens using the search endpoint
                // $response = Http::timeout(30)
                //     ->withHeaders([
                //         'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                //         'Accept' => 'application/json'
                //     ])
                //     ->get($this->dexBaseUrl . '/search?q=top');

                // $searchPairs = [];
                // if ($response->successful() && isset($response->json()['pairs'])) {
                //     $searchPairs = $response->json()['pairs'];
                //     Log::info("Found " . count($searchPairs) . " pairs from search");
                // }

                // Process the tokens from search results
                // $processedTokens = [];
                // foreach ($searchPairs as $pair) {
                //     // if (count($processedTokens) >= $tokenLimit) {
                //     //     break;
                //     // }

                //     if (isset($pair['baseToken']['address']) && isset($pair['chainId'])) {
                //         $chainId = $pair['chainId'];
                //         $tokenAddress = $pair['baseToken']['address'];
                //         $key = $chainId . '_' . $tokenAddress;

                //         if (!isset($processedTokens[$key])) {
                //             $processedTokens[$key] = [
                //                 'chainId' => $chainId,
                //                 'tokenAddress' => $tokenAddress,
                //                 'pair' => $pair
                //             ];
                //         }
                //     }
                // }

                // Now use the tokens/v1 endpoint to get detailed token data
                // foreach ($processedTokens as $key => $data) {
                //     $chainId = $data['chainId'];
                //     $tokenAddress = $data['tokenAddress'];

                //     Log::info("Fetching token data", [
                //         'chainId' => $chainId,
                //         'tokenAddress' => $tokenAddress
                //     ]);

                //     $pairInfo = $this->getTokenPairs($chainId, $tokenAddress);

                //     if (!empty($pairInfo)) {
                //         // Get the most liquid pair
                //         $pair = $pairInfo[0];

                //         // Check if token exists in our database
                //         $token = DexscreenerToken::where('chain_id', $chainId)
                //             ->where('token_address', $tokenAddress)
                //             ->first();

                //         $tokenName = $pair['baseToken']['name'] ?? 'Unknown Token';
                //         $tokenSymbol = $pair['baseToken']['symbol'] ?? 'UNKNOWN';

                //         $priceUsd = isset($pair['priceUsd']) ? (string)$pair['priceUsd'] : null;
                //         $priceChange24h = $this->extractPriceChange($pair, 'h24');
                //         $priceChange5m = $this->extractPriceChange($pair, 'm5');
                //         $priceChange1h = $this->extractPriceChange($pair, 'h1');
                //         $priceChange6h = $this->extractPriceChange($pair, 'h6');
                //         $volume24h = isset($pair['volume']) && isset($pair['volume']['h24']) ? $pair['volume']['h24'] : null;
                //         $marketCap = $pair['marketCap'] ?? null;
                //         $liquidityUsd = isset($pair['liquidity']) && isset($pair['liquidity']['usd']) ? $pair['liquidity']['usd'] : null;
                //         $txn24h = $this->extractTransactionCount($pair);
                //         $buyCount = $this->extractBuyCount($pair);
                //         $sellCount = $this->extractSellCount($pair);
                //         $tokenAge = $this->extractTokenAge($pair);

                //         if ($token) {
                //             // Update existing token
                //             $token->update([
                //                 'token_name' => $tokenName,
                //                 'token_symbol' => $tokenSymbol,
                //                 'price_usd' => $priceUsd,
                //                 'price_change_24h' => $priceChange24h,
                //                 'price_change_5m' => $priceChange5m,
                //                 'price_change_1h' => $priceChange1h,
                //                 'price_change_6h' => $priceChange6h,
                //                 'volume_24h' => $volume24h,
                //                 'market_cap' => $marketCap,
                //                 'liquidity_usd' => $liquidityUsd,
                //                 'txn_24h' => $txn24h,
                //                 'buy_count' => $buyCount,
                //                 'sell_count' => $sellCount,
                //                 'token_age' => $tokenAge,
                //                 'metadata' => json_encode([
                //                     'dexId' => $pair['dexId'] ?? null,
                //                     'url' => $pair['url'] ?? null,
                //                     'lastApiResponse' => json_encode($pair)
                //                 ]),
                //                 'pair_address' => $pair['pairAddress'] ?? null,
                //                 'dex_id' => $pair['dexId'] ?? null,
                //                 'image_url' => isset($pair['baseToken']) ? ($pair['baseToken']['logoURI'] ?? null) : null
                //             ]);

                //             $updatedCount++;

                //             Log::info("Updated existing token", [
                //                 'symbol' => $tokenSymbol,
                //                 'price' => $priceUsd,
                //                 'age' => $tokenAge
                //             ]);
                //         } else {
                //             // Create new token
                //             DexscreenerToken::create([
                //                 'chain_id' => $chainId,
                //                 'token_address' => $tokenAddress,
                //                 'token_name' => $tokenName,
                //                 'token_symbol' => $tokenSymbol,
                //                 'price_usd' => $priceUsd,
                //                 'price_change_24h' => $priceChange24h,
                //                 'price_change_5m' => $priceChange5m,
                //                 'price_change_1h' => $priceChange1h,
                //                 'price_change_6h' => $priceChange6h,
                //                 'volume_24h' => $volume24h,
                //                 'market_cap' => $marketCap,
                //                 'liquidity_usd' => $liquidityUsd,
                //                 'txn_24h' => $txn24h,
                //                 'buy_count' => $buyCount,
                //                 'sell_count' => $sellCount,
                //                 'token_age' => $tokenAge,
                //                 'metadata' => json_encode([
                //                     'dexId' => $pair['dexId'] ?? null,
                //                     'url' => $pair['url'] ?? null,
                //                     'lastApiResponse' => json_encode($pair)
                //                 ]),
                //                 'pair_address' => $pair['pairAddress'] ?? null,
                //                 'dex_id' => $pair['dexId'] ?? null,
                //                 'image_url' => isset($pair['baseToken']) ? ($pair['baseToken']['logoURI'] ?? null) : null
                //             ]);

                //             $addedCount++;

                //             Log::info("Added new token", [
                //                 'symbol' => $tokenSymbol,
                //                 'price' => $priceUsd,
                //                 'age' => $tokenAge
                //             ]);
                //         }
                //     }
                // }

                // // Update manually added tokens that weren't updated in the first part
                // // Get all tokens that weren't updated recently
                // $lastUpdateThreshold = now()->subMinutes(5);

                // First prioritize tokens with null price values
                $nullPriceTokens = DexscreenerToken::whereNull('price_usd')
                    ->orWhere('price_usd', 0)
                    ->orWhere('price_usd', '0')
                    ->get();

                Log::info("Found {$nullPriceTokens->count()} tokens with null/zero price values that need updating");

                $manualUpdated = 0;

                // First update tokens with null prices
                foreach ($nullPriceTokens as $token) {
                    // Fetch token details from API
                    Log::info("Updating token with null price: {$token->token_symbol} ({$token->chain_id}/{$token->token_address})");

                    $tokenData = $this->fetchTokenDetails($token->chain_id, $token->token_address);

                    if ($tokenData && isset($tokenData['price_usd'])) {
                        // Update the token with the latest price data
                        $updated = DexscreenerToken::where('id', $token->id)->update([
                            'price_usd' => $tokenData['price_usd'],
                            'price_change_24h' => $tokenData['price_change_24h'] ?? $token->price_change_24h,
                            'price_change_5m' => $tokenData['price_change_5m'] ?? $token->price_change_5m,
                            'price_change_1h' => $tokenData['price_change_1h'] ?? $token->price_change_1h,
                            'price_change_6h' => $tokenData['price_change_6h'] ?? $token->price_change_6h,
                            'volume_24h' => $tokenData['volume_24h'] ?? $token->volume_24h,
                            'market_cap' => $tokenData['market_cap'] ?? $token->market_cap,
                            'liquidity_usd' => $tokenData['liquidity_usd'] ?? $token->liquidity_usd,
                            'txn_24h' => $tokenData['txn_24h'] ?? $token->txn_24h,
                            'buy_count' => $tokenData['buy_count'] ?? $token->buy_count,
                            'sell_count' => $tokenData['sell_count'] ?? $token->sell_count,
                            'token_age' => $tokenData['token_age'] ?? null,
                            'metadata' => $tokenData['metadata'] ?? $token->metadata,
                            'pair_address' => $tokenData['pair_address'] ?? $token->pair_address,
                            'dex_id' => $tokenData['dex_id'] ?? $token->dex_id,
                            'image_url' => $tokenData['image_url'] ?? $token->image_url,
                            'updated_at' => now(),
                        ]);

                        $manualUpdated++;
                        $updatedCount++;
                        Log::info("Updated null price token #{$manualUpdated}: {$token->token_symbol}, Update successful: {$updated}, New price: {$tokenData['price_usd']}, New 24h Change: {$tokenData['price_change_24h']}, 5m: {$tokenData['price_change_5m']}, 1h: {$tokenData['price_change_1h']}, 6h: {$tokenData['price_change_6h']}");
                    } else {
                        Log::warning("Failed to update token with null price {$token->token_symbol} - no data returned from API");
                    }
                }

                // Then update any other tokens that haven't been updated recently
                // $otherManualTokens = DexscreenerToken::where('updated_at', '<', $lastUpdateThreshold)
                //     ->whereNotNull('price_usd')
                //     ->where('price_usd', '>', 0)
                //     ->get();

                // Log::info("Found {$otherManualTokens->count()} other manual tokens that need updating");

                // foreach ($otherManualTokens as $token) {
                //     // Fetch token details from API
                //     Log::info("Updating manual token: {$token->token_symbol} ({$token->chain_id}/{$token->token_address})");

                //     $tokenData = $this->fetchTokenDetails($token->chain_id, $token->token_address);

                //     if ($tokenData) {
                //         // Update the token with the latest price data
                //         $updated = DexscreenerToken::where('id', $token->id)->update([
                //             'price_usd' => $tokenData['price_usd'] ?? $token->price_usd,
                //             'price_change_24h' => $tokenData['price_change_24h'] ?? $token->price_change_24h,
                //             'price_change_5m' => $tokenData['price_change_5m'] ?? $token->price_change_5m,
                //             'price_change_1h' => $tokenData['price_change_1h'] ?? $token->price_change_1h,
                //             'price_change_6h' => $tokenData['price_change_6h'] ?? $token->price_change_6h,
                //             'volume_24h' => $tokenData['volume_24h'] ?? $token->volume_24h,
                //             'market_cap' => $tokenData['market_cap'] ?? $token->market_cap,
                //             'liquidity_usd' => $tokenData['liquidity_usd'] ?? $token->liquidity_usd,
                //             'txn_24h' => $tokenData['txn_24h'] ?? $token->txn_24h,
                //             'buy_count' => $tokenData['buy_count'] ?? $token->buy_count,
                //             'sell_count' => $tokenData['sell_count'] ?? $token->sell_count,
                //             'token_age' => $tokenData['token_age'] ?? null,
                //             'metadata' => $tokenData['metadata'] ?? $token->metadata,
                //             'pair_address' => $tokenData['pair_address'] ?? $token->pair_address,
                //             'dex_id' => $tokenData['dex_id'] ?? $token->dex_id,
                //             'image_url' => $tokenData['image_url'] ?? $token->image_url,
                //             'updated_at' => now(),
                //         ]);

                //         $manualUpdated++;
                //         $updatedCount++;
                //         Log::info("Updated manual token #{$manualUpdated}: {$token->token_symbol}, Update successful: {$updated}, New price: {$tokenData['price_usd']}, New 24h Change: {$tokenData['price_change_24h']}, 5m: {$tokenData['price_change_5m']}, 1h: {$tokenData['price_change_1h']}, 6h: {$tokenData['price_change_6h']}");
                //     } else {
                //         Log::warning("Failed to update manual token {$token->token_symbol} - no data returned from API");
                //     }
                // }

                Log::info("Updated {$manualUpdated} manually added tokens total");

            } catch (\Exception $e) {
                Log::error("Error fetching tokens", [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Clear cache
            Cache::forget('dexscreener_tokens');

            // Check for presale tokens with real contract addresses that have price data
            // $this->updatePresaleTokensWithPriceData();

            // $finalCount = DexscreenerToken::count();
            // Log::info("Token refresh completed. Updated {$updatedCount} tokens, added {$addedCount} tokens. Total: {$finalCount} tokens.");

            return true;
        } catch (\Exception $e) {
            Log::error('Exception while refreshing token data', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Get promoted tokens for the homepage
     */
    public function getPromotedTokens($limit = null)
    {
        // If no limit provided, get it from general settings
        if ($limit === null) {
            $general = \App\Models\GeneralSetting::first();
            $limit = $general->dexscreener_tokens_count ?? 10;
        }

        // Clear the cache to ensure fresh data
        Cache::forget('dexscreener_tokens');

        // Get tokens from database with price filtering and verification requirement (or admin added)
        $tokens = DexscreenerToken::whereNotNull('price_usd')
            ->where('price_usd', '>', 0)
            ->where(function($query) {
                $query->where('is_verified', true) // Verified user-submitted tokens
                      ->orWhereNull('submitted_by_user_id'); // Admin added tokens
            })
            ->orderBy('updated_at', 'desc')
            ->limit($limit)
            ->get();

        // If we don't have enough tokens with price, get some without the price filter but still verified or admin-added
        if ($tokens->count() < $limit) {
            $additionalLimit = $limit - $tokens->count();
            $additionalTokens = DexscreenerToken::where(function($query) {
                    $query->where('is_verified', true) // Verified user-submitted tokens
                          ->orWhereNull('submitted_by_user_id'); // Admin added tokens
                })
                ->where(function($query) {
                    $query->whereNull('price_usd')
                          ->orWhere('price_usd', '<=', 0);
                })
                ->orderBy('updated_at', 'desc')
                ->limit($additionalLimit)
                ->get();

            // Merge the collections
            $tokens = $tokens->merge($additionalTokens);
        }

        // Store in cache for 5 minutes
        Cache::put('dexscreener_tokens', $tokens, now()->addMinutes(5));

        return $tokens;
    }

    /**
     * Fetch token details for a single token by chain ID and token address
     * This is used for manually adding tokens to the system
     *
     * @param string $chainId
     * @param string $tokenAddress
     * @return array|null
     */
    public function fetchTokenDetails($chainId, $tokenAddress)
    {
        try {
            Log::info('Fetching token details', [
                'chainId' => $chainId,
                'tokenAddress' => $tokenAddress
            ]);

            // Try the pairs endpoint first as it's generally more reliable
            // $pairsResponse = Http::timeout(10)
            //     ->withHeaders([
            //         'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            //         'Accept' => 'application/json'
            //     ])
            //     ->get($this->dexBaseUrl . "/pairs/{$chainId}/{$tokenAddress}");

            // if ($pairsResponse->successful() && isset($pairsResponse->json()['pairs']) && !empty($pairsResponse->json()['pairs'])) {
            //     $pair = $pairsResponse->json()['pairs'][0]; // Get the first pair

            //     // Extract useful data using helper methods
            //     $priceUsd = isset($pair['priceUsd']) ? $pair['priceUsd'] : null;
            //     $priceChange24h = $this->extractPriceChange($pair, 'h24');
            //     $priceChange5m = $this->extractPriceChange($pair, 'm5');
            //     $priceChange1h = $this->extractPriceChange($pair, 'h1');
            //     $priceChange6h = $this->extractPriceChange($pair, 'h6');
            //     $volume24h = isset($pair['volume']) && isset($pair['volume']['h24']) ? $pair['volume']['h24'] : null;
            //     $marketCap = $pair['marketCap'] ?? null;
            //     $liquidityUsd = isset($pair['liquidity']) && isset($pair['liquidity']['usd']) ? $pair['liquidity']['usd'] : null;
            //     $txn24h = $this->extractTransactionCount($pair);
            //     $buyCount = $this->extractBuyCount($pair);
            //     $sellCount = $this->extractSellCount($pair);
            //     $tokenAge = $this->extractTokenAge($pair);

            //     Log::info('Successfully fetched token details from pairs endpoint', [
            //         'token' => $pair['baseToken']['symbol'] ?? 'Unknown',
            //         'price' => $priceUsd,
            //         'change24h' => $priceChange24h,
            //         'change5m' => $priceChange5m,
            //         'change1h' => $priceChange1h,
            //         'change6h' => $priceChange6h,
            //         'txn24h' => $txn24h,
            //         'buyCount' => $buyCount,
            //         'sellCount' => $sellCount,
            //         'tokenAge' => $tokenAge,
            //     ]);

            //     return [
            //         'price_usd' => $priceUsd,
            //         'price_change_24h' => $priceChange24h,
            //         'price_change_5m' => $priceChange5m,
            //         'price_change_1h' => $priceChange1h,
            //         'price_change_6h' => $priceChange6h,
            //         'volume_24h' => $volume24h,
            //         'market_cap' => $marketCap,
            //         'liquidity_usd' => $liquidityUsd,
            //         'txn_24h' => $txn24h,
            //         'buy_count' => $buyCount,
            //         'sell_count' => $sellCount,
            //         'token_age' => $tokenAge,
            //         'metadata' => json_encode([
            //             'dexId' => $pair['dexId'] ?? null,
            //             'url' => $pair['url'] ?? null,
            //             'lastApiResponse' => json_encode($pair)
            //         ]),
            //         'pair_address' => $pair['pairAddress'] ?? null,
            //         'dex_id' => $pair['dexId'] ?? null,
            //         'image_url' => isset($pair['baseToken']) ? ($pair['baseToken']['logoURI'] ?? null) : null
            //     ];
            // }

            // If pairs endpoint fails, try the tokens/v1 endpoint
            Log::info('Using tokens/v1 endpoint get token details by chain id and token address', [
                'url' => $this->baseUrl . "/tokens/v1/{$chainId}/{$tokenAddress}"
            ]);

            // Get token information using the tokens/v1 endpoint which is more reliable for price data
            $response = Http::timeout(10)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept' => 'application/json'
                ])
                ->get($this->baseUrl . "/tokens/v1/{$chainId}/{$tokenAddress}");

            // Log the response for debugging
            Log::debug('DexScreener API response for manual token', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);


            if ($response->successful()) {
                $pair = @$response->json()[0];
                if($pair){
                    // Extract useful data with better error handling using helper methods
                    $priceUsd = isset($pair['priceUsd']) ? $pair['priceUsd'] : null;
                    $priceChange24h = $this->extractPriceChange($pair, 'h24');
                    $priceChange5m = $this->extractPriceChange($pair, 'm5');
                    $priceChange1h = $this->extractPriceChange($pair, 'h1');
                    $priceChange6h = $this->extractPriceChange($pair, 'h6');
                    $volume24h = isset($pair['volume']) && isset($pair['volume']['h24']) ? $pair['volume']['h24'] : null;
                    $marketCap = $pair['marketCap'] ?? null;
                    $liquidityUsd = isset($pair['liquidity']) && isset($pair['liquidity']['usd']) ? $pair['liquidity']['usd'] : null;
                    $txn24h = $this->extractTransactionCount($pair);
                    $buyCount = $this->extractBuyCount($pair);
                    $sellCount = $this->extractSellCount($pair);
                    $tokenAge = $this->extractTokenAge($pair);

                    // Log the price data for debugging
                    Log::info('Got Token price data from tokens/v1 endpoint , response success...', [
                        'symbol' => $pair['baseToken']['symbol'] ?? 'unknown',
                        'priceUsd' => $priceUsd,
                        'priceChange24h' => $priceChange24h,
                        'priceChange5m' => $priceChange5m,
                        'priceChange1h' => $priceChange1h,
                        'priceChange6h' => $priceChange6h,
                        'txn24h' => $txn24h,
                        'buyCount' => $buyCount,
                        'sellCount' => $sellCount,
                        'tokenAge' => $tokenAge,
                    ]);

                    return [
                        'price_usd' => $priceUsd,
                        'price_change_24h' => $priceChange24h,
                        'price_change_5m' => $priceChange5m,
                        'price_change_1h' => $priceChange1h,
                        'price_change_6h' => $priceChange6h,
                        'volume_24h' => $volume24h,
                        'market_cap' => $marketCap,
                        'liquidity_usd' => $liquidityUsd,
                        'txn_24h' => $txn24h,
                        'buy_count' => $buyCount,
                        'sell_count' => $sellCount,
                        'token_age' => $tokenAge,
                        'metadata' => json_encode([
                            'dexId' => $pair['dexId'] ?? null,
                            'url' => $pair['url'] ?? null,
                            'lastApiResponse' => json_encode($pair)
                        ]),
                        'pair_address' => $pair['pairAddress'] ?? null,
                        'dex_id' => $pair['dexId'] ?? null,
                        'image_url' => isset($pair['baseToken']) ? ($pair['baseToken']['logoURI'] ?? null) : null
                    ];
                }
            }

            // No manual default value, return null if no data is found
            return null;
        } catch (\Exception $e) {
            Log::error('Error fetching token details for manual addition', [
                'chainId' => $chainId,
                'tokenAddress' => $tokenAddress,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Helper method to extract price change values from various API response formats
     *
     * @param array $pair The pair data from the API
     * @param string $interval The time interval (m5, h1, h6, h24)
     * @return string|null The extracted price change value or null
     */
    protected function extractPriceChange($pair, $interval)
    {
        // Handle standard format like 'h24'
        if (isset($pair['priceChange']) && isset($pair['priceChange'][$interval])) {
            return (string)$pair['priceChange'][$interval];
        }

        // Handle alternative format without prefix like '24h'
        $alternativeInterval = str_replace(['m', 'h'], '', $interval);
        if (isset($pair['priceChange']) && isset($pair['priceChange'][$alternativeInterval . 'h']) && $interval !== 'm5') {
            return (string)$pair['priceChange'][$alternativeInterval . 'h'];
        }
        if (isset($pair['priceChange']) && isset($pair['priceChange'][$alternativeInterval . 'm']) && $interval === 'm5') {
            return (string)$pair['priceChange'][$alternativeInterval . 'm'];
        }

        // Handle full word format like 'hour24'
        $timeWords = [
            'm5' => 'minute5',
            'h1' => 'hour1',
            'h6' => 'hour6',
            'h24' => 'hour24'
        ];
        if (isset($timeWords[$interval]) && isset($pair['priceChange']) && isset($pair['priceChange'][$timeWords[$interval]])) {
            return (string)$pair['priceChange'][$timeWords[$interval]];
        }

        // Nothing found
        return null;
    }

    /**
     * Helper method to extract transaction count from various API response formats
     *
     * @param array $pair The pair data from the API
     * @return int|null The extracted transaction count or null
     */
    protected function extractTransactionCount($pair)
    {
        // Log transaction data structure for debugging
        if (isset($pair['txns'])) {
            Log::debug('DexScreener API transaction structure:', [
                'txns' => $pair['txns'],
                'keys' => isset($pair['txns']) ? array_keys((array)$pair['txns']) : [],
                'h24_keys' => isset($pair['txns']['h24']) ? array_keys((array)$pair['txns']['h24']) : [],
            ]);
        }

        // Standard format: txns.h24.buys + txns.h24.sells
        if (isset($pair['txns']) && isset($pair['txns']['h24']) &&
            isset($pair['txns']['h24']['buys']) && isset($pair['txns']['h24']['sells'])) {
            return (int)($pair['txns']['h24']['buys'] + $pair['txns']['h24']['sells']);
        }

        // Alternative format: txns.24h.buys + txns.24h.sells
        if (isset($pair['txns']) && isset($pair['txns']['24h']) &&
            isset($pair['txns']['24h']['buys']) && isset($pair['txns']['24h']['sells'])) {
            return (int)($pair['txns']['24h']['buys'] + $pair['txns']['24h']['sells']);
        }

        // Simple format: txns.h24 as a single number
        if (isset($pair['txns']) && isset($pair['txns']['h24']) && is_numeric($pair['txns']['h24'])) {
            return (int)$pair['txns']['h24'];
        }

        // Alternative simple format: txns.24h as a single number
        if (isset($pair['txns']) && isset($pair['txns']['24h']) && is_numeric($pair['txns']['24h'])) {
            return (int)$pair['txns']['24h'];
        }

        // Direct field
        if (isset($pair['txCount24h']) && is_numeric($pair['txCount24h'])) {
            return (int)$pair['txCount24h'];
        }

        // Nothing found
        return null;
    }

    /**
     * Extract buy count from transaction data
     *
     * @param array $pair The pair data from the API
     * @return int|null The buy count or null
     */
    protected function extractBuyCount($pair)
    {
        // Standard format: txns.h24.buys
        if (isset($pair['txns']) && isset($pair['txns']['h24']) && isset($pair['txns']['h24']['buys'])) {
            return (int)$pair['txns']['h24']['buys'];
        }

        // Alternative format: txns.24h.buys
        if (isset($pair['txns']) && isset($pair['txns']['24h']) && isset($pair['txns']['24h']['buys'])) {
            return (int)$pair['txns']['24h']['buys'];
        }

        // Nothing found
        return null;
    }

    /**
     * Extract sell count from transaction data
     *
     * @param array $pair The pair data from the API
     * @return int|null The sell count or null
     */
    protected function extractSellCount($pair)
    {
        // Standard format: txns.h24.sells
        if (isset($pair['txns']) && isset($pair['txns']['h24']) && isset($pair['txns']['h24']['sells'])) {
            return (int)$pair['txns']['h24']['sells'];
        }

        // Alternative format: txns.24h.sells
        if (isset($pair['txns']) && isset($pair['txns']['24h']) && isset($pair['txns']['24h']['sells'])) {
            return (int)$pair['txns']['24h']['sells'];
        }

        // Nothing found
        return null;
    }

    /**
     * Extract token age from API response
     *
     * @param array $pair The pair data from API
     * @return int|null The age in days or null if not found
     */
    private function extractTokenAge($pair)
    {
        Log::debug('DexScreener API token age data fields:', [
            'pairCreatedAt' => $pair['pairCreatedAt'] ?? 'not_found',
            'createdAt' => $pair['createdAt'] ?? 'not_found',
            'listedAt' => $pair['listedAt'] ?? 'not_found',
            'baseToken.listedAt' => isset($pair['baseToken']) ? ($pair['baseToken']['listedAt'] ?? 'not_found') : 'no_baseToken',
            'baseToken.timestamp' => isset($pair['baseToken']) ? ($pair['baseToken']['timestamp'] ?? 'not_found') : 'no_baseToken',
            'timestamp' => $pair['timestamp'] ?? 'not_found',
            'age' => $pair['age'] ?? 'not_found',
            'pairAge' => $pair['pairAge'] ?? 'not_found',
            'baseToken.age' => isset($pair['baseToken']) ? ($pair['baseToken']['age'] ?? 'not_found') : 'no_baseToken'
        ]);

        // Current time for age calculations
        $now = time();

        // 1. Direct age field (most reliable if provided)
        if (isset($pair['age']) && is_numeric($pair['age'])) {
            $age = (int)$pair['age'];
            if ($age > 0) {
                Log::debug("Found direct age field: {$age} days");
                return $age;
            }
        }

        // 2. Try baseToken.age
        if (isset($pair['baseToken']['age']) && is_numeric($pair['baseToken']['age'])) {
            $age = (int)$pair['baseToken']['age'];
            if ($age > 0) {
                Log::debug("Found baseToken.age field: {$age} days");
                return $age;
            }
        }

        // 3. Try pairAge (less reliable, but still direct)
        if (isset($pair['pairAge']) && is_numeric($pair['pairAge'])) {
            $age = (int)$pair['pairAge'];
            if ($age > 0) {
                Log::debug("Found pairAge field: {$age} days");
                return $age;
            }
        }

        // 4. Calculate from pairCreatedAt (most common) - properly handle millisecond timestamp
        if (isset($pair['pairCreatedAt']) && is_numeric($pair['pairCreatedAt'])) {
            $pairCreatedAt = (int)$pair['pairCreatedAt'];

            // Check if timestamp is in milliseconds (13 digits) and convert to seconds
            if (strlen((string)$pairCreatedAt) > 10) {
                $pairCreatedAt = (int)($pairCreatedAt / 1000);
            }

            // Sanity check - ensure the timestamp is not in the future and is reasonable
            if ($pairCreatedAt > 0 && $pairCreatedAt <= $now) {
                $ageInDays = floor(($now - $pairCreatedAt) / 86400); // 86400 seconds in a day
                Log::debug("Calculated age from pairCreatedAt: {$ageInDays} days (from timestamp {$pair['pairCreatedAt']})");
                return max(1, (int)$ageInDays); // At least 1 day old
            } else if ($pairCreatedAt > $now) {
                // If timestamp is in future, it's likely an error or misconfiguration
                Log::warning("pairCreatedAt timestamp is in the future, using fallback: {$pair['pairCreatedAt']}");
                return 1; // Fallback to 1 day as minimum
            }
        }

        // 5. Try baseToken.listedAt
        if (isset($pair['baseToken']['listedAt']) && !empty($pair['baseToken']['listedAt'])) {
            $listedAt = $pair['baseToken']['listedAt'];
            if (is_numeric($listedAt)) {
                // Handle millisecond timestamps
                if (strlen((string)$listedAt) > 10) {
                    $listedAt = (int)($listedAt / 1000);
                }

                // Sanity check - ensure the timestamp is not in the future
                if ($listedAt > 0 && $listedAt <= $now) {
                    $ageInDays = floor(($now - $listedAt) / 86400);
                    Log::debug("Calculated age from baseToken.listedAt numeric: {$ageInDays} days");
                    return max(1, (int)$ageInDays); // At least 1 day old
                } else if ($listedAt > $now) {
                    Log::warning("baseToken.listedAt timestamp is in the future, using fallback");
                    return 1; // Fallback to 1 day as minimum
                }
            } elseif (is_string($listedAt) && !empty($listedAt)) {
                $parsedListedAt = strtotime($listedAt);
                if ($parsedListedAt && $parsedListedAt <= $now) {
                    $ageInDays = floor(($now - $parsedListedAt) / 86400);
                    Log::debug("Calculated age from baseToken.listedAt string: {$ageInDays} days");
                    return max(1, (int)$ageInDays); // At least 1 day old
                } else if ($parsedListedAt > $now) {
                    Log::warning("baseToken.listedAt string date is in the future, using fallback");
                    return 1; // Fallback to 1 day as minimum
                }
            }
        }

        // 6. Try createdAt
        if (isset($pair['createdAt']) && !empty($pair['createdAt'])) {
            $createdAt = $pair['createdAt'];
            if (is_numeric($createdAt)) {
                // Handle millisecond timestamps
                if (strlen((string)$createdAt) > 10) {
                    $createdAt = (int)($createdAt / 1000);
                }

                // Sanity check - ensure the timestamp is not in the future
                if ($createdAt > 0 && $createdAt <= $now) {
                    $ageInDays = floor(($now - $createdAt) / 86400);
                    Log::debug("Calculated age from createdAt numeric: {$ageInDays} days");
                    return max(1, (int)$ageInDays); // At least 1 day old
                } else if ($createdAt > $now) {
                    Log::warning("createdAt timestamp is in the future, using fallback");
                    return 1; // Fallback to 1 day as minimum
                }
            } elseif (is_string($createdAt) && !empty($createdAt)) {
                $parsedCreatedAt = strtotime($createdAt);
                if ($parsedCreatedAt && $parsedCreatedAt <= $now) {
                    $ageInDays = floor(($now - $parsedCreatedAt) / 86400);
                    Log::debug("Calculated age from createdAt string: {$ageInDays} days");
                    return max(1, (int)$ageInDays); // At least 1 day old
                } else if ($parsedCreatedAt > $now) {
                    Log::warning("createdAt string date is in the future, using fallback");
                    return 1; // Fallback to 1 day as minimum
                }
            }
        }

        // 7. Try timestamp field
        if (isset($pair['timestamp']) && is_numeric($pair['timestamp'])) {
            $timestamp = (int)$pair['timestamp'];

            // Handle standard and millisecond formats
            if (strlen((string)$timestamp) > 10) {
                $timestamp = (int)($timestamp / 1000); // Convert from milliseconds
            }

            // Sanity check - ensure the timestamp is not in the future
            if ($timestamp > 0 && $timestamp <= $now) {
                $ageInDays = floor(($now - $timestamp) / 86400);
                Log::debug("Calculated age from timestamp: {$ageInDays} days");
                return max(1, (int)$ageInDays); // At least 1 day old
            } else if ($timestamp > $now) {
                Log::warning("timestamp is in the future, using fallback: {$timestamp}");
                return 1; // Fallback to 1 day as minimum
            }
        }

        // If all else fails but we know the token exists, set a minimum age
        Log::debug("Could not determine token age from any field, using minimum fallback");
        return 1; // Default to 1 day old if token exists but no valid age found
    }

    /**
     * Update presale tokens that have real contract addresses and price data
     * This method is called after each token refresh to automatically update
     * presale tokens that are now live on the blockchain
     */
    public function updatePresaleTokensWithPriceData()
    {
        try {

            ini_set('max_execution_time', 240);
            
            Log::info('Checking for presale tokens with real contract addresses that have price data...');

            $presaleTokensCount = PresaleCronUpdate::count();

            if ($presaleTokensCount > 0) {
                $presaleTokenIds = PresaleCronUpdate::limit(250)->pluck('submit_coin_id')->toArray();
                Log::info('Fetched ' . count($presaleTokenIds) . ' presale tokens from queue');
                $presaleTokens = SubmitCoin::whereIn('id', $presaleTokenIds)->get();
            } else {
                SubmitCoin::where('is_presale', 1)
                    ->where('contract_address', 'not like', 'presale_%')
                    ->where('contract_address', '!=', '')
                    ->whereNotNull('contract_address')
                    ->chunk(1000, function ($coins) {
                        $insertData = $coins->map(fn($coin) => ['submit_coin_id' => $coin->id])->toArray();
                        PresaleCronUpdate::insertOrIgnore($insertData);
                    });

                $presaleTokenIds = PresaleCronUpdate::limit(250)->pluck('submit_coin_id')->toArray();
                $presaleTokens = SubmitCoin::whereIn('id', $presaleTokenIds)->get();
            }

            PresaleCronUpdate::whereIn('submit_coin_id', $presaleTokenIds)->delete();

            Log::info('Found ' . $presaleTokens->count() . ' presale tokens with real contract addresses');

            // Process each presale token
            $updatedCount = 0;
            foreach ($presaleTokens as $submitCoin) {
                Log::info('Checking token: ' . $submitCoin->name . ' (' . $submitCoin->contract_address . ')');

                // Find the corresponding DexscreenerToken
                $token = \App\Models\DexscreenerToken::where('token_address', $submitCoin->contract_address)
                    ->first();

                if (!$token) {
                    Log::info('No DexscreenerToken found, creating one...');

                    // Create a new DexscreenerToken record
                    $token = new \App\Models\DexscreenerToken();
                    $token->chain_id = $submitCoin->blockchain;
                    $token->token_address = $submitCoin->contract_address;
                    $token->token_name = $submitCoin->name;
                    $token->token_symbol = $submitCoin->symbol;
                    $token->submitted_by_user_id = $submitCoin->user_id;
                    $token->is_verified = $submitCoin->status == 1; // Copy verified status
                    $token->save();

                    Log::info('Created new DexscreenerToken record');
                }

                // Check if the token has price data
                if ($token && $token->price_usd && $token->price_usd > 0) {
                    Log::info('Token has price data! Updating presale status...');

                    // Update the token metadata to remove force_presale_display flag if it exists
                    if (is_object($token->metadata)) {
                        // If metadata is already an object, convert it to array
                        $metadata = json_decode(json_encode($token->metadata), true);
                    } elseif (is_string($token->metadata)) {
                        // If metadata is a string, decode it
                        $metadata = json_decode($token->metadata ?? '{}', true);
                    } else {
                        // Default to empty array
                        $metadata = [];
                    }

                    // Remove the force_presale_display flag if it exists
                    if (isset($metadata['force_presale_display'])) {
                        unset($metadata['force_presale_display']);
                        $token->metadata = json_encode($metadata);
                        $token->save();
                        Log::info('Removed force_presale_display flag from token metadata');
                    }

                    // Update the SubmitCoin record to mark it as no longer presale or fair launch
                    $submitCoin->is_presale = 0;
                    $submitCoin->is_fair_launch = 0;
                    $submitCoin->save();

                    $updatedCount++;
                    Log::info('Successfully updated presale token with live data');
                } else {
                    // If the token doesn't have price data yet, try to fetch it from the API
                    if ($token) {
                        Log::info('Token exists but has no price data. Trying to fetch from API...');
                        // 300 requests per minute allowed
                        $tokenData = $this->fetchTokenDetails($token->chain_id, $token->token_address);

                        // If we got data from the API with a price, update the token
                        if ($tokenData && isset($tokenData['price_usd']) && $tokenData['price_usd'] > 0) {
                            Log::info('API returned price data! Updating token...');

                            // Update the token with the latest price data
                            if (is_object($token->metadata)) {
                                // If metadata is already an object, convert it to array
                                $metadata = json_decode(json_encode($token->metadata), true);
                            } elseif (is_string($token->metadata)) {
                                // If metadata is a string, decode it
                                $metadata = json_decode($token->metadata ?? '{}', true);
                            } else {
                                // Default to empty array
                                $metadata = [];
                            }

                            // Remove the force_presale_display flag if it exists
                            if (isset($metadata['force_presale_display'])) {
                                unset($metadata['force_presale_display']);
                            }

                            // Update the token data
                            $token->price_usd = $tokenData['price_usd'];
                            $token->price_change_24h = $tokenData['price_change_24h'] ?? $token->price_change_24h;
                            $token->price_change_5m = $tokenData['price_change_5m'] ?? $token->price_change_5m;
                            $token->price_change_1h = $tokenData['price_change_1h'] ?? $token->price_change_1h;
                            $token->price_change_6h = $tokenData['price_change_6h'] ?? $token->price_change_6h;
                            $token->volume_24h = $tokenData['volume_24h'] ?? $token->volume_24h;
                            $token->market_cap = $tokenData['market_cap'] ?? $token->market_cap;
                            $token->liquidity_usd = $tokenData['liquidity_usd'] ?? $token->liquidity_usd;
                            $token->txn_24h = $tokenData['txn_24h'] ?? $token->txn_24h;
                            $token->buy_count = $tokenData['buy_count'] ?? $token->buy_count;
                            $token->sell_count = $tokenData['sell_count'] ?? $token->sell_count;
                            $token->token_age = $tokenData['token_age'] ?? null;
                            $token->metadata = json_encode($metadata);
                            $token->pair_address = $tokenData['pair_address'] ?? $token->pair_address;
                            $token->dex_id = $tokenData['dex_id'] ?? $token->dex_id;
                            $token->image_url = $tokenData['image_url'] ?? $token->image_url;
                            $token->updated_at = now();
                            $token->save();

                            // Update the SubmitCoin record to mark it as no longer presale
                            $submitCoin->is_presale = 0;
                            $submitCoin->save();

                            $updatedCount++;
                            Log::info('Successfully updated presale token with live data from API');
                        } else {
                            Log::info('Token is not yet live or no data available from API');
                        }
                    }
                }
            }

            Log::info('Updated ' . $updatedCount . ' presale tokens with live data');
            return $updatedCount;
        } catch (\Exception $e) {
            Log::error('Error updating presale tokens: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return 0;
        }
    }
}