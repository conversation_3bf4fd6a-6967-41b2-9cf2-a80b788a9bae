<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CryptoCurrency;
use App\Models\CryptoSetting;
use App\Constants\Status;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class CryptoController extends Controller
{
    public function index()
    {
        $pageTitle = 'Crypto Ticker Settings';
        $setting = CryptoSetting::first() ?? new CryptoSetting();
        $currencies = CryptoCurrency::latest()->get();
        
        return view('admin.crypto.index', compact('pageTitle', 'setting', 'currencies'));
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'api_key' => 'required|string',
            'display_count' => 'required|integer|min:1|max:50',
            'is_active' => 'nullable'
        ]);

        $setting = CryptoSetting::first();
        if (!$setting) {
            $setting = new CryptoSetting();
        }

        $setting->api_key = $request->api_key;
        $setting->display_count = $request->display_count;
        $setting->is_active = $request->has('is_active') ? 1 : 0;
        $setting->save();

        // Ensure the cron job exists for fetching crypto data
        $this->ensureCryptoCronJobExists();

        $notify[] = ['success', 'Crypto ticker settings updated successfully'];
        return back()->withNotify($notify);
    }

    // Helper function to ensure the crypto cron job exists
    private function ensureCryptoCronJobExists()
    {
        $cronJob = \App\Models\CronJob::where('alias', 'fetch_crypto_data')->first();
        
        if (!$cronJob) {
            // Find an hourly schedule, or create one if it doesn't exist
            $schedule = \App\Models\CronSchedule::where('name', 'Hourly')->first();
            
            if (!$schedule) {
                $schedule = new \App\Models\CronSchedule();
                $schedule->name = 'Hourly';
                $schedule->interval = 3600; // 1 hour in seconds
                $schedule->status = Status::ENABLE;
                $schedule->save();
            }
            
            // Create the crypto fetching cron job
            $cronJob = new \App\Models\CronJob();
            $cronJob->name = 'Fetch Crypto Data';
            $cronJob->alias = 'fetch_crypto_data';
            $cronJob->action = ['App\Http\Controllers\CronController', 'fetchCryptoData'];
            $cronJob->next_run = now();
            $cronJob->cron_schedule_id = $schedule->id;
            $cronJob->is_default = Status::YES;
            $cronJob->is_running = Status::YES;
            $cronJob->save();
        }
    }

    public function fetchCryptoData()
    {
        $setting = CryptoSetting::first();
        
        if (!$setting || !$setting->api_key) {
            $notify[] = ['error', 'API key is not configured'];
            return back()->withNotify($notify);
        }

        try {
            $response = Http::withHeaders([
                'X-CMC_PRO_API_KEY' => $setting->api_key,
                'Accept' => 'application/json'
            ])->get('https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest', [
                'limit' => $setting->display_count,
                'convert' => 'USD'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                // Clear existing data
                CryptoCurrency::truncate();
                
                // Insert new data
                foreach ($data['data'] as $crypto) {
                    CryptoCurrency::create([
                        'name' => $crypto['name'],
                        'symbol' => $crypto['symbol'],
                        'price' => $crypto['quote']['USD']['price'],
                        'percent_change_24h' => $crypto['quote']['USD']['percent_change_24h'],
                        'market_cap' => $crypto['quote']['USD']['market_cap'],
                        'volume_24h' => $crypto['quote']['USD']['volume_24h'],
                        'last_updated' => now(),
                    ]);
                }
                
                // Update last updated time
                $setting->last_updated = now();
                $setting->save();
                
                // Also update the general system last_cron time for consistency
                $general = gs();
                $general->last_cron = now();
                $general->save();
                
                $notify[] = ['success', 'Crypto data fetched successfully'];
            } else {
                $notify[] = ['error', 'Failed to fetch crypto data: ' . ($response->json()['status']['error_message'] ?? 'Unknown error')];
            }
        } catch (\Exception $e) {
            $notify[] = ['error', 'API Error: ' . $e->getMessage()];
        }
        
        return back()->withNotify($notify);
    }
} 