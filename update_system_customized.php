<?php

// This script updates the general_settings table to set system_customized to 1 (true)
// Run this script from the command line: php update_system_customized.php

require_once 'core/vendor/autoload.php';

// Bootstrap the Laravel application
$app = require_once 'core/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Update the general_settings table
try {
    $result = DB::table('general_settings')->update(['system_customized' => 1]);
    echo "Successfully updated system_customized to true. Rows affected: $result\n";
} catch (Exception $e) {
    echo "Error updating database: " . $e->getMessage() . "\n";
}
