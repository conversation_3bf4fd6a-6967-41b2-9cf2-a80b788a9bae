<?php

namespace App\Http\Controllers\Gateway\PaypalExpress;

use App\Constants\Status;
use App\Models\Deposit;
use App\Http\Controllers\Gateway\PaymentController;
use App\Http\Controllers\Controller;
use App\Lib\CurlRequest;

class ProcessController extends Controller
{
    public static function process($deposit)
    {
        $general = gs();
        $paypalExpressAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);

        // Create a new PayPal Express Checkout transaction
        $sandboxMode = @$paypalExpressAcc->sandbox_mode && $paypalExpressAcc->sandbox_mode == 'yes';

        if ($sandboxMode) {
            $url = 'https://www.sandbox.paypal.com/cgi-bin/webscr';
        } else {
            $url = 'https://www.paypal.com/cgi-bin/webscr';
        }

        $val = [
            'cmd' => '_xclick',
            'business' => trim($paypalExpressAcc->paypal_email),
            'cbt' => $general->site_name,
            'currency_code' => "$deposit->method_currency",
            'quantity' => 1,
            'amount' => round($deposit->final_amount, 2),
            'item_name' => "Payment To $general->site_name Account",
            'custom' => "$deposit->trx",
            'return' => route('user.plans.purchased'),
            'cancel_return' => route('user.plans.index'),
            'notify_url' => route('ipn.paypalexpress'),
            'no_shipping' => 1,
        ];

        $send = new \stdClass();
        $send->val = $val;
        $send->view = 'user.payment.paypalexpress';
        $send->method = 'post';
        $send->url = $url;

        return json_encode($send);
    }

    public function ipn()
    {
        $raw_post_data = file_get_contents('php://input');
        $raw_post_array = explode('&', $raw_post_data);
        $myPost = array();
        foreach ($raw_post_array as $keyval) {
            $keyval = explode('=', $keyval);
            if (count($keyval) == 2)
                $myPost[$keyval[0]] = urldecode($keyval[1]);
        }

        $req = 'cmd=_notify-validate';
        foreach ($myPost as $key => $value) {
            $value = urlencode(stripslashes($value));
            $req .= "&$key=$value";
            $details[$key] = $value;
        }

        // Check for deposit
        $deposit = Deposit::where('trx', @$_POST['custom'])->orderBy('id', 'DESC')->first();
        if (!$deposit) {
            exit;
        }

        $paypalExpressAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);
        $sandboxMode = @$paypalExpressAcc->sandbox_mode && $paypalExpressAcc->sandbox_mode == 'yes';

        // Use correct IPN URL based on sandbox mode
        if ($sandboxMode) {
            $paypalURL = "https://ipnpb.sandbox.paypal.com/cgi-bin/webscr?";
        } else {
            $paypalURL = "https://ipnpb.paypal.com/cgi-bin/webscr?";
        }

        $url = $paypalURL . $req;
        $response = CurlRequest::curlContent($url);

        if ($response == "VERIFIED") {
            $deposit->detail = $details;
            $deposit->save();

            // Check for payment_status completed, and other validation
            if (
                (@$_POST['payment_status'] == 'Completed' || @$_POST['payment_status'] == 'Processed' || @$_POST['payment_status'] == 'Success') &&
                @$_POST['mc_gross'] == round($deposit->final_amount,2) &&
                $deposit->status == Status::PAYMENT_INITIATE
            ) {
                PaymentController::userDataUpdate($deposit);

                // Update any pending plan purchase
                if ($deposit->order_id > 0) {
                    $planPurchase = \App\Models\PlanPurchase::where('id', $deposit->order_id)
                        ->where('status', Status::PENDING)
                        ->first();

                    if ($planPurchase) {
                        $plan = \App\Models\Plan::find($planPurchase->plan_id);

                        if ($plan) {
                            // Update the purchase status
                            $planPurchase->status = Status::COMPLETED;
                            $planPurchase->save();

                            // Add trend votes, promote credits, and ad credits to user
                            $user = \App\Models\User::find($deposit->user_id);
                            $user->trend_votes += $plan->trend_votes;
                            $user->promote_credits += $plan->promote_credits;
                            $user->ad_credits += $plan->ad_credits;
                            $user->save();

                            // Process referral commission on plan purchase
                            if (gs()->referral_system && $user->ref_by) {
                                levelCommission($user, $planPurchase->price, $deposit->trx);
                            }
                        }
                    }
                }
            }
        }
    }
}