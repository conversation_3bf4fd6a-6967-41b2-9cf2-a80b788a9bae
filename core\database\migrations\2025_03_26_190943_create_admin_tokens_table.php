<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_tokens', function (Blueprint $table) {
            $table->id();
            $table->string('chain_id')->comment('Blockchain identifier (e.g., ethereum, bsc)');
            $table->string('token_address')->comment('Token contract address');
            $table->string('token_name');
            $table->string('token_symbol');
            $table->decimal('price_usd', 30, 10)->nullable();
            $table->decimal('price_change_24h', 15, 2)->nullable();
            $table->decimal('volume_24h', 30, 2)->nullable();
            $table->decimal('market_cap', 30, 2)->nullable();
            $table->decimal('liquidity_usd', 30, 2)->nullable();
            $table->text('metadata')->nullable()->comment('Additional token data in JSON format');
            $table->string('pair_address')->nullable();
            $table->string('dex_id')->nullable()->comment('DEX identifier (e.g., uniswap, pancakeswap)');
            $table->string('image_url')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('position')->default(0)->comment('For manual ordering');
            $table->dateTime('last_updated')->nullable();
            $table->timestamps();
            
            // Add unique constraint to prevent duplicates
            $table->unique(['chain_id', 'token_address']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_tokens');
    }
};
