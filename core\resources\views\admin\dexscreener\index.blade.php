@extends('admin.layouts.app')
@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                        <h5 class="card-title">@lang('Dexscreener Tokens')</h5>
                        <div class="card-tools">
                            <a href="{{ route('admin.dexscreener.add') }}" class="btn btn--success me-2">
                                <i class="las la-plus"></i> @lang('Add Token')
                            </a>
                            <form action="{{ route('admin.dexscreener.refresh') }}" method="post" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn--primary">
                                    <i class="las la-sync"></i> @lang('Refresh Data')
                                </button>
                            </form>
                        </div>
                    </div>
                    <div class="d-flex justify-content-center mt-3">
                        <div class="col-md-6">
                            <form action="{{ route('admin.dexscreener.index') }}" method="GET" class="d-flex">
                                <div class="input-group w-100">
                                    <input type="text" name="search" class="form-control" placeholder="Search by token name or contract address" value="{{ $search ?? '' }}">
                                    <button class="btn btn--primary input-group-text"><i class="la la-search"></i></button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive--md table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th>@lang('Icon')</th>
                                    <th>@lang('Token')</th>
                                    <th>@lang('Chain')</th>
                                    <th>@lang('Price')</th>
                                    <th>@lang('Age')</th>
                                    <th>@lang('Volume 24h')</th>
                                    <th>@lang('Liquidity')</th>
                                    <th>@lang('Market Cap')</th>
                                    <th>@lang('Last Updated')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($tokens as $token)
                                <tr>
                                    <td>
                                        <img src="@if(isset($token->image_url) && !filter_var($token->image_url, FILTER_VALIDATE_URL) && strpos($token->image_url, 'http') !== 0){{ asset('assets/images/coin_logos/'.$token->image_url) }}@else{{ $token->image_url ?? asset('assets/images/default.png') }}@endif" alt="{{ $token->token_symbol }}" class="token-icon" width="32" height="32">
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ $token->token_name }}</span>
                                        <br>
                                        <small>{{ $token->token_symbol }}</small>
                                    </td>
                                    <td>{{ formatBlockchainName($token->chain_id) }}</td>
                                    <td>${{ $token->price_usd }}</td>
                                    <td>{{ $token->token_age ? $token->token_age . ' days' : '-' }}</td>
                                    <td>${{ formatAmount($token->volume_24h) }}</td>
                                    <td>${{ formatAmount($token->liquidity_usd) }}</td>
                                    <td>${{ formatAmount($token->market_cap) }}</td>
                                    <td>
                                        {{ $token->updated_at->diffForHumans() }}
                                        <br>
                                        <small class="text-muted">({{ $token->updated_at }})</small>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.dexscreener.edit', $token->id) }}" class="btn btn-sm btn--primary me-1">
                                            <i class="las la-edit"></i>
                                        </a>
                                        <a href="{{ route('admin.dexscreener.delete', $token->id) }}" class="btn btn-sm btn--danger">
                                            <i class="las la-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage) }}</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($tokens->hasPages())
                <div class="card-footer py-4">
                    <div class="d-flex justify-content-center">
                        {{ paginateLinks($tokens) }}
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('style')
<style>
    .token-icon {
        border-radius: 50%;
        object-fit: cover;
    }
</style>
@endpush

@push('script')
<script>
    /**
     * Format large numbers to a more readable format (K, M, B)
     */
    function formatAmount(amount) {
        if (amount >= 1000000000) {
            return (amount / 1000000000).toFixed(2) + 'B';
        } else if (amount >= 1000000) {
            return (amount / 1000000).toFixed(2) + 'M';
        } else if (amount >= 1000) {
            return (amount / 1000).toFixed(2) + 'K';
        }
        return amount.toFixed(2);
    }
</script>
@endpush