<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ArticleVote extends Model
{
    protected $fillable = [
        'article_id',
        'user_id',
        'ip_address',
        'voted_at',
        'used_trend_vote',
    ];

    protected $casts = [
        'voted_at' => 'date',
        'used_trend_vote' => 'boolean',
    ];

    public function article()
    {
        return $this->belongsTo(Frontend::class, 'article_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Check if a user with the given IP has already voted for the article today
    // Only checks for regular votes, not trend votes
    public static function hasVotedToday($articleId, $ipAddress)
    {
        return self::where('article_id', $articleId)
            ->where('ip_address', $ipAddress)
            ->where('voted_at', now()->format('Y-m-d'))
            ->where('used_trend_vote', false)
            ->exists();
    }

    // Get vote count for an article
    public static function getVoteCount($articleId)
    {
        return self::where('article_id', $articleId)->count();
    }
    
    // Get total vote count including admin trend votes
    public static function getTotalVoteCount($articleId)
    {
        // Get article
        $article = Frontend::where('id', $articleId)
                ->where('data_keys', 'blog.element')
                ->first();
        
        if (!$article) {
            return 0;
        }
        
        // Get regular user votes
        $userVotes = self::getVoteCount($articleId);
        
        // Get admin trend votes
        $adminTrendVotes = 0;
        if (isset($article->data_values->trend_votes) && is_numeric($article->data_values->trend_votes)) {
            $adminTrendVotes = (int)$article->data_values->trend_votes;
        }
        
        // Return total votes
        return $userVotes + $adminTrendVotes;
    }
} 