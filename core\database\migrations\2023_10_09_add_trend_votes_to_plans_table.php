<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if 'trend_votes' column doesn't exist in plans table
        if (!Schema::hasColumn('plans', 'trend_votes')) {
            Schema::table('plans', function (Blueprint $table) {
                $table->integer('trend_votes')->default(0)->after('price');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only drop if column exists
        if (Schema::hasColumn('plans', 'trend_votes')) {
            Schema::table('plans', function (Blueprint $table) {
                $table->dropColumn('trend_votes');
            });
        }
    }
}; 