<?php

namespace App\Http\Middleware;

use App\Constants\Status;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class Admin2FAMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = auth('admin')->user();
        
        if ($user && $user->ts == Status::ENABLE && $user->tv == Status::NO) {
            return to_route('admin.2fa.form');
        }
        
        return $next($request);
    }
}
