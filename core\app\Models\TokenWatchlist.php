<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TokenWatchlist extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'chain_id',
        'token_address',
    ];

    /**
     * Get the user who watchlisted the token
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the token that is watchlisted
     */
    public function token()
    {
        return $this->belongsTo(DexscreenerToken::class, 'token_address', 'token_address')
            ->where('chain_id', $this->chain_id);
    }

    /**
     * Check if a token is in a user's watchlist
     */
    public static function isWatchlisted($userId, $chainId, $tokenAddress)
    {
        return self::where('user_id', $userId)
            ->where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->exists();
    }
}
