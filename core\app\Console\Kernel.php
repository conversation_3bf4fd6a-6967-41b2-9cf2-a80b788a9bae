<?php

namespace App\Console;

// use Illuminate\Console\Scheduling\Schedule; // No longer needed here for scheduling
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\RefreshDexscreenerTokens::class,
        // Commands\ProcessCronJobs::class, // This was already commented
        Commands\ExpireTokenPromotions::class, // This is scheduled in bootstrap/app.php
        
        // These commands are now scheduled in bootstrap/app.php
        Commands\UpdateCoinMarketCapDataCommand::class,
        Commands\UpdatePresaleTokensDataCommand::class,
        Commands\UpdatePopularTokensDataCommand::class,
        Commands\UpdateUnpopularTokensDataCommand::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(/* Schedule $schedule */)
    {
        // Scheduling is now handled in core/bootstrap/app.php for Laravel 11+
        // You can remove all $schedule->command(...) lines from here.
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        // The require base_path('routes/console.php'); is usually done in bootstrap/app.php's withRouting section
        // If routes/console.php is where 'inspire' and other commands are registered, that's fine.
        // If you don't have a routes/console.php or don't need it because commands are auto-discovered or in $commands array, you can simplify.
        // For Laravel 11, commands in app/Console/Commands are auto-discovered by default if not specified in routes/console.php.
        require base_path('routes/console.php'); 
    }
}