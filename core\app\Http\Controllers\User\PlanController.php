<?php

namespace App\Http\Controllers\User;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\PlanPurchase;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PlanController extends Controller
{
    public function index()
    {
        // Check if there's a pending purchase in the session and clean it up
        if (session()->has('pending_purchase_id')) {
            $pendingPurchaseId = session()->get('pending_purchase_id');

            // Mark the purchase as cancelled if it's still pending
            PlanPurchase::where('id', $pendingPurchaseId)
                ->where('user_id', auth()->id())
                ->where('status', Status::PENDING)
                ->update(['status' => Status::CANCELLED]);

            // Remove the pending purchase from the session
            session()->forget('pending_purchase_id');
        }
        $pageTitle = 'Plans';
        $plans = Plan::active()->latest()->paginate(getPaginate());
        return view('Template::user.plans.index', compact('pageTitle', 'plans'));
    }

    public function purchased()
    {
        $pageTitle = 'My Plans';
        $user = auth()->user();

        // Check for any pending plan purchases with initiated payments
        $pendingPurchases = PlanPurchase::where('user_id', $user->id)
            ->where('status', Status::PENDING)
            ->with('plan')
            ->get();

        foreach ($pendingPurchases as $pendingPurchase) {
            // Check if there's a deposit record for this purchase
            $deposit = \App\Models\Deposit::where('order_id', $pendingPurchase->id)
                ->whereIn('status', [Status::PAYMENT_INITIATE, Status::PAYMENT_SUCCESS])
                ->whereHas('gateway', function($query) {
                    $query->where('alias', 'PaypalExpress');
                })
                ->where('created_at', '>', now()->subMinutes(30))
                ->first();

            if ($deposit) {
                // This is a recent PayPal Express payment that likely completed successfully
                // but the IPN might not have been received yet

                // Mark as completed
                $pendingPurchase->status = Status::COMPLETED;
                $pendingPurchase->save();

                // Add trend votes, promote credits, and ad credits to user
                if ($pendingPurchase->plan) {
                    $user->trend_votes += $pendingPurchase->plan->trend_votes;
                    $user->promote_credits += $pendingPurchase->plan->promote_credits;
                    $user->ad_credits += $pendingPurchase->plan->ad_credits;
                    $user->save();
                }

                // Update deposit status
                $deposit->status = Status::PAYMENT_SUCCESS;
                $deposit->save();

                // Create transaction record if it doesn't exist
                $transaction = \App\Models\Transaction::where('trx', $deposit->trx)->first();
                if (!$transaction) {
                    $transaction = new \App\Models\Transaction();
                    $transaction->user_id = $user->id;
                    $transaction->amount = $deposit->amount;
                    $transaction->post_balance = $user->balance;
                    $transaction->charge = $deposit->charge;
                    $transaction->trx_type = '+';
                    // Build transaction details based on what was purchased
                    $details = [];
                    if ($pendingPurchase->plan->trend_votes > 0) {
                        $details[] = $pendingPurchase->plan->trend_votes . ' Trend Votes';
                    }
                    if ($pendingPurchase->plan->promote_credits > 0) {
                        $details[] = $pendingPurchase->plan->promote_credits . ' Promote Credits';
                    }
                    if ($pendingPurchase->plan->ad_credits > 0) {
                        $details[] = $pendingPurchase->plan->ad_credits . ' Ad Credits';
                    }

                    if (count($details) > 0) {
                        $transaction->details = 'Purchase: ' . implode(' and ', $details);
                    } else {
                        $transaction->details = 'Purchase';
                    }
                    $transaction->trx = $deposit->trx;
                    $transaction->remark = 'plan_purchase';
                    $transaction->save();
                }
            }
        }

        // Only show completed transactions, limited to 5 per page
        $purchases = PlanPurchase::where('user_id', auth()->id())
            ->where('status', Status::COMPLETED)
            ->with('plan')
            ->latest()
            ->paginate(5);

        // Clean up old pending transactions
        PlanPurchase::where('user_id', auth()->id())
            ->where('status', Status::PENDING)
            ->where('created_at', '<', now()->subHours(24))
            ->update(['status' => Status::CANCELLED]);
        return view('Template::user.plans.purchased', compact('pageTitle', 'purchases'));
    }

    public function purchase(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
            'payment_method' => 'required|in:1,2', // 1 for balance, 2 for gateway
        ]);

        $user = auth()->user();
        $plan = Plan::active()->findOrFail($request->plan_id);

        if ($request->payment_method == 1) {
            // Check if user has enough balance
            if ($user->balance < $plan->price) {
                $notify[] = ['error', 'Insufficient balance'];
                return back()->withNotify($notify);
            }

            DB::beginTransaction();
            try {
                // Create purchase record
                $purchase = new PlanPurchase();
                $purchase->user_id = $user->id;
                $purchase->plan_id = $plan->id;
                $purchase->price = $plan->price;
                $purchase->trx = getTrx();
                $purchase->status = Status::COMPLETED;
                $purchase->save();

                // Update user balance
                $user->balance -= $plan->price;
                $user->save();

                // Add trend votes, promote credits, and ad credits to user
                $user->trend_votes += $plan->trend_votes;
                $user->promote_credits += $plan->promote_credits;
                $user->ad_credits += $plan->ad_credits;
                $user->save();

                // Check if there's a pending promotion from the submit form
                $pendingPromotion = session('pending_promotion');
                if ($pendingPromotion && isset($pendingPromotion['from_submit_form']) && $pendingPromotion['from_submit_form']) {
                    // Ensure days is an integer
                    $days = (int) $pendingPromotion['days'];

                    // If user now has enough credits, redirect back to the submit form
                    if ($user->promote_credits >= $days) {
                        // Check if the pending promotion has expired
                        if (isset($pendingPromotion['expires_at']) && $pendingPromotion['expires_at'] < now()->timestamp) {
                            session()->forget('pending_promotion');
                            $notify[] = ['error', 'Your pending promotion has expired. Please try again.'];
                            return redirect()->route('user.submit.coin.form', ['step' => 3])->withNotify($notify);
                        }

                        // Create a promotion record in session to be applied after submission
                        // Don't deduct credits yet - will be deducted after successful submission
                        session(['promote_after_submit' => [
                            'chain_id' => $pendingPromotion['chain_id'],
                            'token_address' => $pendingPromotion['token_address'],
                            'token_name' => $pendingPromotion['token_name'] ?? '',
                            'token_symbol' => $pendingPromotion['token_symbol'] ?? '',
                            'days' => $days, // Use the integer value
                            'expires_at' => now()->addHours(1)->timestamp // Add expiration
                        ]]);

                        session()->forget('pending_promotion');
                        $notify[] = ['success', 'Plan purchased successfully. Your token will be promoted after submission.'];
                        return redirect()->route('user.submit.coin.form', ['step' => 3])->withNotify($notify);
                    }
                }

                // Create transaction record
                $transaction = new Transaction();
                $transaction->user_id = $user->id;
                $transaction->amount = $plan->price;
                $transaction->post_balance = $user->balance;
                $transaction->charge = 0;
                $transaction->trx_type = '-';
                // Build transaction details based on what was purchased
                $details = [];
                if ($plan->trend_votes > 0) {
                    $details[] = $plan->trend_votes . ' Trend Votes';
                }
                if ($plan->promote_credits > 0) {
                    $details[] = $plan->promote_credits . ' Promote Credits';
                }
                if ($plan->ad_credits > 0) {
                    $details[] = $plan->ad_credits . ' Ad Credits';
                }

                if (count($details) > 0) {
                    $transaction->details = 'Purchase: ' . implode(' and ', $details);
                } else {
                    $transaction->details = 'Purchase';
                }
                $transaction->trx = $purchase->trx;
                $transaction->remark = 'plan_purchase';
                $transaction->save();

                // Process referral commission on plan purchase
                if (gs()->referral_system && $user->ref_by) {
                    \Illuminate\Support\Facades\Log::channel('daily')->info('PLAN PURCHASE: Processing referral commission', [
                        'user_id' => $user->id,
                        'username' => $user->username,
                        'ref_by' => $user->ref_by,
                        'plan_price' => $plan->price,
                        'trx' => $purchase->trx
                    ]);
                    levelCommission($user, $plan->price, $purchase->trx);
                } else {
                    // Create direct referral records if needed (fallback mechanism)
                    if ($user->ref_by > 0) {
                        try {
                            // Check if a commission has already been given for this user
                            $existingCommission = \App\Models\ReferralLog::where('user_id', $user->id)
                                ->first();

                            if ($existingCommission) {
                                \Illuminate\Support\Facades\Log::channel('daily')->info('PLAN PURCHASE: Commission already given for this user', [
                                    'user_id' => $user->id
                                ]);
                                return;
                            }

                            // Referral system might be disabled but we still want to credit
                            $referrer = \App\Models\User::find($user->ref_by);
                            $referralLevel = \App\Models\Referral::where('level', 1)->first();

                            if (!$referralLevel) {
                                $referralLevel = new \App\Models\Referral();
                                $referralLevel->level = 1;
                                $referralLevel->percent = 10;
                                $referralLevel->save();
                            }

                            $commissionValue = ($plan->price * $referralLevel->percent) / 100;

                            // Do NOT add to referrer balance - referral commissions are tracked via referral logs
                            // and can only be withdrawn through the referral withdrawal system

                            // Create transaction record for tracking purposes only
                            $transaction = new \App\Models\Transaction();
                            $transaction->user_id = $referrer->id;
                            $transaction->amount = $commissionValue;
                            $transaction->post_balance = $referrer->balance; // Balance remains unchanged
                            $transaction->charge = 0;
                            $transaction->trx_type = '+';
                            $transaction->details = 'You have received referral commission from ' . $user->username;
                            $transaction->trx = $purchase->trx . '-REF';
                            $transaction->remark = 'referral_commission';
                            $transaction->save();

                            // Create referral log
                            $refLog = new \App\Models\ReferralLog();
                            $refLog->user_id = $user->id;
                            $refLog->referee_id = $referrer->id;
                            $refLog->amount = $commissionValue;
                            $refLog->level = 1;
                            $refLog->percent = $referralLevel->percent;
                            $refLog->trx = $purchase->trx;
                            $refLog->save();

                            \Illuminate\Support\Facades\Log::channel('daily')->info('PLAN PURCHASE: Forced referral commission processing', [
                                'user_id' => $user->id,
                                'referrer_id' => $referrer->id,
                                'amount' => $commissionValue
                            ]);
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\Log::channel('daily')->error('PLAN PURCHASE: Forced referral processing failed', [
                                'error' => $e->getMessage()
                            ]);
                        }
                    } else {
                        \Illuminate\Support\Facades\Log::channel('daily')->info('PLAN PURCHASE: Skipping referral commission', [
                            'referral_system_enabled' => gs()->referral_system,
                            'user_has_referrer' => (bool)$user->ref_by,
                            'user_id' => $user->id
                        ]);
                    }
                }

                DB::commit();

                $notify[] = ['success', 'Plan purchased successfully'];
                return to_route('user.plans.purchased')->withNotify($notify);
            } catch (\Exception $e) {
                DB::rollback();
                $notify[] = ['error', 'Something went wrong: ' . $e->getMessage()];
                return back()->withNotify($notify);
            }
        } else {
            // Create a pending purchase record
            $purchase = new PlanPurchase();
            $purchase->user_id = $user->id;
            $purchase->plan_id = $plan->id;
            $purchase->price = $plan->price;
            $purchase->trx = getTrx();
            $purchase->status = Status::PENDING;
            $purchase->save();

            // Redirect to payment gateway selection page
            session()->put('order_id', $purchase->id);
            session()->put('pending_purchase_id', $purchase->id); // Track the pending purchase
            return to_route('user.plans.select.payment', $purchase->id);
        }
    }

    public function selectPayment($id)
    {
        // Validate that this is the current pending purchase
        if (session()->has('pending_purchase_id') && session()->get('pending_purchase_id') == $id) {
            // Continue with payment selection
        } else {
            // If user is trying to access an old payment selection page, redirect to plans
            $notify[] = ['error', 'Invalid payment session'];
            return to_route('user.plans.index')->withNotify($notify);
        }
        $pageTitle = 'Select Payment Method';
        $purchase = PlanPurchase::where('id', $id)->where('user_id', auth()->id())->where('status', Status::PENDING)->firstOrFail();
        $plan = Plan::findOrFail($purchase->plan_id);

        // Allow only specific gateways
        $allowedGateways = [101, 104, 503, 506, 504]; // Paypal Express, 2Checkout, CoinPayments, Coinbase Commerce, CoinPayments Fiat
        $allowedAliases = ['paypal', 'twocheckout', 'coinpayments', 'coinbasecommerce', 'coinpaymentsfiat'];

        $gatewayCurrency = \App\Models\GatewayCurrency::whereHas('method', function ($gate) use ($allowedGateways, $allowedAliases) {
            $gate->where('status', Status::ENABLE)
                 ->where(function($query) use ($allowedGateways, $allowedAliases) {
                     $query->whereIn('code', $allowedGateways)
                           ->orWhereIn('alias', $allowedAliases);
                 });
        })->with('method')->orderby('method_code')->get();

        if ($gatewayCurrency->isEmpty()) {
            $notify[] = ['error', 'No payment gateway available'];
            return to_route('user.plans.index')->withNotify($notify);
        }

        return view('Template::user.payment.gateway', compact('pageTitle', 'gatewayCurrency', 'purchase', 'plan'));
    }

    public function processPayment(Request $request, $id)
    {
        $request->validate([
            'gateway' => 'required',
            'currency' => 'required',
        ]);

        $purchase = PlanPurchase::where('id', $id)->where('user_id', auth()->id())->where('status', Status::PENDING)->firstOrFail();
        $amount = $purchase->price;
        $user = auth()->user();

        // Allow only specific gateways
        $allowedGateways = [101, 104, 503, 506, 504]; // Paypal Express, 2Checkout, CoinPayments, Coinbase Commerce, CoinPayments Fiat
        $allowedAliases = ['paypal', 'twocheckout', 'coinpayments', 'coinbasecommerce', 'coinpaymentsfiat'];

        $gate = \App\Models\GatewayCurrency::whereHas('method', function ($gate) use ($allowedGateways, $allowedAliases) {
            $gate->where('status', Status::ENABLE)
                 ->where(function($query) use ($allowedGateways, $allowedAliases) {
                     $query->whereIn('code', $allowedGateways)
                           ->orWhereIn('alias', $allowedAliases);
                 });
        })->where('method_code', $request->gateway)->where('currency', $request->currency)->first();

        if (!$gate) {
            $notify[] = ['error', 'Invalid gateway'];
            return back()->withNotify($notify);
        }

        if ($gate->min_amount > $amount || $gate->max_amount < $amount) {
            $notify[] = ['error', 'Please follow deposit limit'];
            return back()->withNotify($notify);
        }

        $charge    = $gate->fixed_charge + ($amount * $gate->percent_charge / 100);
        $payable   = $amount + $charge;
        $finalAmount = $payable * $gate->rate;

        $data = new \App\Models\Deposit();
        $data->user_id         = $user->id;
        $data->order_id        = $purchase->id;
        $data->method_code     = $gate->method_code;
        $data->method_currency = strtoupper($gate->currency);
        $data->amount          = $amount;
        $data->charge          = $charge;
        $data->rate            = $gate->rate;
        $data->final_amount    = $finalAmount;
        $data->btc_amount      = 0;
        $data->btc_wallet      = "";
        $data->trx             = getTrx();
        $data->status          = Status::PAYMENT_INITIATE;
        $data->success_url     = urlPath('user.plans.purchased');
        $data->failed_url      = urlPath('user.plans.index');
        $data->save();
        session()->put('Track', $data->trx);
        return to_route('user.deposit.confirm');
    }

    public function buyTrendVotes()
    {
        // Check if there's a pending purchase in the session and clean it up
        if (session()->has('pending_purchase_id')) {
            $pendingPurchaseId = session()->get('pending_purchase_id');

            // Mark the purchase as cancelled if it's still pending
            PlanPurchase::where('id', $pendingPurchaseId)
                ->where('user_id', auth()->id())
                ->where('status', Status::PENDING)
                ->update(['status' => Status::CANCELLED]);

            // Remove the pending purchase from the session
            session()->forget('pending_purchase_id');
        }
        $pageTitle = 'Buy Trend Votes';
        $plans = Plan::active()->where('trend_votes', '>', 0)->latest()->paginate(getPaginate());
        return view('Template::user.plans.trend_votes', compact('pageTitle', 'plans'));
    }

    public function buyPromoteCredits()
    {
        // Check if there's a pending purchase in the session and clean it up
        if (session()->has('pending_purchase_id')) {
            $pendingPurchaseId = session()->get('pending_purchase_id');

            // Mark the purchase as cancelled if it's still pending
            PlanPurchase::where('id', $pendingPurchaseId)
                ->where('user_id', auth()->id())
                ->where('status', Status::PENDING)
                ->update(['status' => Status::CANCELLED]);

            // Remove the pending purchase from the session
            session()->forget('pending_purchase_id');
        }
        $pageTitle = 'Buy Promote Credits';
        $plans = Plan::active()->where('promote_credits', '>', 0)->latest()->paginate(getPaginate());
        return view('Template::user.plans.promote_credits', compact('pageTitle', 'plans'));
    }

    public function buyAdCredits()
    {
        // Check if there's a pending purchase in the session and clean it up
        if (session()->has('pending_purchase_id')) {
            $pendingPurchaseId = session()->get('pending_purchase_id');

            // Mark the purchase as cancelled if it's still pending
            PlanPurchase::where('id', $pendingPurchaseId)
                ->where('user_id', auth()->id())
                ->where('status', Status::PENDING)
                ->update(['status' => Status::CANCELLED]);

            // Remove the pending purchase from the session
            session()->forget('pending_purchase_id');
        }
        $pageTitle = 'Buy Ad Credits';
        $plans = Plan::active()->where('ad_credits', '>', 0)->latest()->paginate(getPaginate());
        return view('Template::user.plans.ad_credits', compact('pageTitle', 'plans'));
    }
}