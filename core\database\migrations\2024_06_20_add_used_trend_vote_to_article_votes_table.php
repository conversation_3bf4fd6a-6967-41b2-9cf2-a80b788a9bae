<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('article_votes', function (Blueprint $table) {
            if (!Schema::hasColumn('article_votes', 'used_trend_vote')) {
                $table->boolean('used_trend_vote')->default(false)->after('voted_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('article_votes', function (Blueprint $table) {
            if (Schema::hasColumn('article_votes', 'used_trend_vote')) {
                $table->dropColumn('used_trend_vote');
            }
        });
    }
}; 