<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_token_purchases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('presale_id')->nullable()->constrained('presales')->onDelete('set null');
            $table->string('token_name');
            $table->string('token_symbol');
            $table->decimal('amount', 28, 8)->comment('Amount in USD');
            $table->decimal('tokens', 28, 8)->comment('Number of tokens purchased');
            $table->decimal('price', 28, 8)->comment('Price per token in USD');
            $table->foreignId('transaction_id')->nullable()->constrained('transactions')->onDelete('set null');
            $table->timestamps();
            
            // Add index for faster queries
            $table->index('user_id');
            $table->index('presale_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_token_purchases');
    }
};
