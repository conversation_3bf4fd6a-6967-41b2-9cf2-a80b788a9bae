<?php

namespace App\Http\Controllers\Gateway\CoinpaymentsFiat;

use App\Constants\Status;
use App\Models\Deposit;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Gateway\PaymentController;
use Illuminate\Http\Request;

class ProcessController extends Controller
{
    /*
     * CoinPaymentHosted Gateway
     */

    public static function process($deposit)
    {
        $coinpayAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);

        $val['merchant'] = $coinpayAcc->merchant_id;
        $val['item_name'] = 'Payment to ' . gs('site_name');
        $val['currency'] = $deposit->method_currency;
        $val['currency_code'] = "$deposit->method_currency";
        $val['amountf'] = round($deposit->final_amount,2);
        $callbackUrl = route('ipn.'.$deposit->gateway->alias);

        // Log the callback URL for debugging
        \Illuminate\Support\Facades\Log::channel('daily')->info('CoinpaymentsFiat callback URL', [
            'url' => $callbackUrl,
            'trx' => $deposit->trx
        ]);

        $val['ipn_url'] = $callbackUrl;
        $val['custom'] = "$deposit->trx";
        $val['amount'] = round($deposit->final_amount,2);
        $val['return'] = route('home').$deposit->success_url;
        $val['cancel_return'] = route('home').$deposit->failed_url;
        $val['notify_url'] = $callbackUrl;
        $val['success_url'] = route('home').$deposit->success_url;
        $val['cancel_url'] = route('home').$deposit->failed_url;
        $val['custom'] = $deposit->trx;
        $val['cmd'] = '_pay_simple';
        $val['want_shipping'] = 0;
        $send['val'] = $val;
        $send['view'] = 'user.payment.redirect';
        $send['method'] = 'post';
        $send['url'] = 'https://www.coinpayments.net/index.php';

        return json_encode($send);
    }

    public function ipn(Request $request)
    {
        // Log the IPN request for debugging
        \Illuminate\Support\Facades\Log::channel('daily')->info('CoinpaymentsFiat IPN received', [
            'request' => $request->all()
        ]);

        $track = $request->custom;
        $status = $request->status;
        $amount1 = floatval($request->amount1);
        $deposit = Deposit::where('trx', $track)->orderBy('id', 'DESC')->first();

        if (!$deposit) {
            \Illuminate\Support\Facades\Log::channel('daily')->error('CoinpaymentsFiat IPN: Deposit not found', [
                'track' => $track
            ]);
            return response('Deposit not found', 404);
        }

        \Illuminate\Support\Facades\Log::channel('daily')->info('CoinpaymentsFiat IPN: Deposit found', [
            'deposit_id' => $deposit->id,
            'status' => $deposit->status,
            'method_currency' => $deposit->method_currency,
            'final_amount' => round($deposit->final_amount,2),
            'request_status' => $status,
            'request_amount1' => $amount1,
            'request_currency1' => $request->currency1
        ]);

        // Status 100 = complete, 2 = pending
        if ($status >= 100 || $status == 2) {
            $coinPayAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);

            // Check if merchant ID matches and deposit is in initiated state
            $merchantCheck = ($coinPayAcc->merchant_id == $request->merchant);
            $currencyCheck = ($deposit->method_currency == $request->currency1);
            $amountCheck = (round($deposit->final_amount,2) <= $amount1);
            $statusCheck = ($deposit->status == Status::PAYMENT_INITIATE);

            \Illuminate\Support\Facades\Log::channel('daily')->info('CoinpaymentsFiat IPN: Validation checks', [
                'merchant_check' => $merchantCheck,
                'currency_check' => $currencyCheck,
                'amount_check' => $amountCheck,
                'status_check' => $statusCheck
            ]);

            if ($currencyCheck && $amountCheck && $merchantCheck && $statusCheck) {
                \Illuminate\Support\Facades\Log::channel('daily')->info('CoinpaymentsFiat IPN: Updating payment status', [
                    'deposit_id' => $deposit->id
                ]);

                PaymentController::userDataUpdate($deposit);
                return response('Payment updated successfully', 200);
            }
        }

        return response('IPN received but no action taken', 200);
    }
}
