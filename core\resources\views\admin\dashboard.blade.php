@extends('admin.layouts.app')

@section('panel')
    <div class="row gy-4">

        <div class="col-xxl-3 col-sm-6">
            <x-widget style="2" link="{{ route('admin.users.all') }}" icon="las la-users" title="Total Users" value="{{ $widget['total_users'] }}" color="primary" icon_style="solid" />
        </div><!-- dashboard-w1 end -->
        <div class="col-xxl-3 col-sm-6">
            <x-widget style="2" link="{{ route('admin.users.active') }}" icon="las la-user-check" title="Active Users" value="{{ $widget['verified_users'] }}" color="success" icon_style="solid" />
        </div><!-- dashboard-w1 end -->
        <div class="col-xxl-3 col-sm-6">
            <x-widget style="2" link="{{ route('admin.users.email.unverified') }}" icon="lar la-envelope" title="Email Unverified Users" value="{{ $widget['email_unverified_users'] }}" color="danger" icon_style="solid" />
        </div><!-- dashboard-w1 end -->
        <div class="col-xxl-3 col-sm-6">
            <x-widget style="2" link="{{ route('admin.users.mobile.unverified') }}" icon="las la-comment-slash" title="Mobile Unverified Users" value="{{ $widget['mobile_unverified_users'] }}" color="warning" icon_style="solid" />
        </div><!-- dashboard-w1 end -->
    </div><!-- row end-->

    <div class="row mb-none-30 mt-30">
        @if(request()->routeIs('admin.dashboard'))
        <div class="col-xl-12 mb-30">
            <div class="card border--primary">
                <div class="card-header bg--primary d-flex justify-content-between">
                    <h4 class="card-title text-white">@lang('Quick Links')</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Remove the trending articles quick link -->
                        <!-- Add more quick links as needed -->
                    </div>
                </div>
            </div>
        </div>
        @endif

        <div class="col-xl-8 mb-30 mx-auto">
            <div class="card border" style="background-color: #c5cace;">
                <div class="card-body">
                    <div class="d-flex justify-content-between flex-wrap gap-3">
                        <h5 class="card-title">@lang('Deposits & Withdrawals Report')</h5>

                        <div class="d-flex flex-wrap gap-3">
                            <select class="form-control w-auto" name="currency" id="trxCurrency">
                                @foreach ($transactionCurrencies as $transactionCurrency)
                                    <option value="{{ $transactionCurrency }}" @selected($transactionCurrency == gs()->cur_text)>{{ strtoupper($transactionCurrency) }}</option>
                                @endforeach
                            </select>

                            <div id="trxDatePicker" class="border daterangepicker-selectbox rounded">
                                <i class="la la-calendar"></i>&nbsp;
                                <span></span> <i class="la la-caret-down"></i>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex mb-3">
                        <div id="profitSummary" class="bg--dark p-3 rounded me-3" style="min-width: 200px;">
                            <h6 class="text-white mb-2">@lang('Profit Summary')</h6>
                            <div class="profit-data">
                                <p class="mb-1"><strong>@lang('Total Deposits'):</strong> <span id="totalDeposits">0</span></p>
                                <p class="mb-1"><strong>@lang('Total Withdrawals'):</strong> <span id="totalWithdrawals">0</span></p>
                                <hr class="my-2 border-light">
                                <p class="mb-0"><strong>@lang('Total Profits'):</strong> <span id="totalProfits" class="text-success">0</span></p>
                            </div>
                        </div>
                    </div>
                    <div id="transactionChartArea"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-none-30 mt-5">
        <div class="col-xl-4 col-lg-6 mb-30">
            <div class="card overflow-hidden">
                <div class="card-body">
                    <h5 class="card-title">@lang('Login By Browser') (@lang('Last 30 days'))</h5>
                    <canvas id="userBrowserChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 mb-30">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">@lang('Login By OS') (@lang('Last 30 days'))</h5>
                    <canvas id="userOsChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 mb-30">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">@lang('Login By Country') (@lang('Last 30 days'))</h5>
                    <canvas id="userCountryChart"></canvas>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('script-lib')
    <script src="{{ asset('assets/admin/js/vendor/apexcharts.min.js') }}"></script>
    <script src="{{ asset('assets/admin/js/vendor/chart.js.2.8.0.js') }}"></script>
    <script src="{{ asset('assets/admin/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/admin/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/admin/js/charts.js') }}"></script>
@endpush

@push('style-lib')
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/admin/css/daterangepicker.css') }}">
@endpush

@push('script')
    <script>
        "use strict";

        const start = moment().subtract(14, 'days');
        const end = moment();

        const dateRangeOptions = {
            startDate: start,
            endDate: end,
            ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 15 Days': [moment().subtract(14, 'days'), moment()],
                'Last 30 Days': [moment().subtract(30, 'days'), moment()],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                'Last 6 Months': [moment().subtract(6, 'months').startOf('month'), moment().endOf('month')],
                'This Year': [moment().startOf('year'), moment().endOf('year')],
            },
            maxDate: moment()
        }

        const changeDatePickerText = (element, startDate, endDate) => {
            $(element).html(startDate.format('MMMM D, YYYY') + ' - ' + endDate.format('MMMM D, YYYY'));
        }



        let trxChart = barChart(
            document.querySelector("#transactionChartArea"),
            $('#trxCurrency').val(),
            [{
                    name: "Deposits",
                    data: []
                },
                {
                    name: "Withdrawals",
                    data: []
                }
            ],
            []
        );



        const transactionChart = (startDate, endDate) => {
            let currency = $('#trxCurrency').val();

            const data = {
                start_date: startDate.format('YYYY-MM-DD'),
                end_date: endDate.format('YYYY-MM-DD'),
                currency
            }

            const url = @json(route('admin.chart.transaction'));


            $.get(url, data,
                function(data, status) {
                    if (status == 'success') {
                        trxChart.updateSeries(data.data);
                        trxChart.updateOptions({
                            xaxis: {
                                categories: data.created_on,
                            },
                            yaxis: {
                                title: {
                                    text: data.currency,
                                    style: {
                                        color: '#7c97bb'
                                    }
                                }
                            }
                        });

                        // Update profit summary
                        $('#totalDeposits').text(data.currency + ' ' + data.total_deposits);
                        $('#totalWithdrawals').text(data.currency + ' ' + data.total_withdrawals);

                        // Set color based on profit value
                        if (data.total_profits >= 0) {
                            $('#totalProfits').removeClass('text-danger').addClass('text-success');
                        } else {
                            $('#totalProfits').removeClass('text-success').addClass('text-danger');
                        }

                        $('#totalProfits').text(data.currency + ' ' + data.total_profits);
                    }
                }
            );
        }

        $('#trxDatePicker').daterangepicker(dateRangeOptions, (start, end) => changeDatePickerText('#trxDatePicker span', start, end));

        transactionChart(start, end);

        $('#trxDatePicker').on('apply.daterangepicker', (event, picker) => transactionChart(picker.startDate, picker.endDate));

        $("#trxCurrency").on("change", function() {
            let startDate = $("#trxDatePicker").data('daterangepicker').startDate._d;
            let endDate = $("#trxDatePicker").data('daterangepicker').endDate._d;

            transactionChart(moment(startDate), moment(endDate));
        });



        piChart(
            document.getElementById('userBrowserChart'),
            @json(@$chart['user_browser_counter']->keys()),
            @json(@$chart['user_browser_counter']->flatten())
        );

        piChart(
            document.getElementById('userOsChart'),
            @json(@$chart['user_os_counter']->keys()),
            @json(@$chart['user_os_counter']->flatten())
        );

        piChart(
            document.getElementById('userCountryChart'),
            @json(@$chart['user_country_counter']->keys()),
            @json(@$chart['user_country_counter']->flatten())
        );
    </script>
@endpush

@push('style')
    <style>
        .apexcharts-menu {
            min-width: 120px !important;
        }

        .daterangepicker-selectbox {
            height: 45px;
            padding: 0 12px;
            cursor: pointer !important;
        }

        .daterangepicker-selectbox i {
            line-height: 45px;
        }

        #profitSummary {
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        #profitSummary p {
            font-size: 14px;
        }

        #profitSummary .text-success {
            color: #00e396 !important;
        }

        #profitSummary .text-danger {
            color: #d92027 !important;
        }

    </style>
@endpush
