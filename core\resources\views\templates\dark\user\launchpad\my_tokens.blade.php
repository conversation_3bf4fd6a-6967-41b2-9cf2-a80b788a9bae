@extends($activeTemplate . 'layouts.master')
@section('content')

@if(!auth()->user()->ccl_token_address)
<div class="alert alert-info mb-4" role="alert">
    <strong>@lang('Important:')</strong> @lang('Please provide your token address where you would like to receive your CCL tokens. If you do not submit this address now, you will NOT receive your tokens after the presale has ended.')
</div>

<div class="token-address-form mb-4">
    <form action="{{ route('user.ccl.token.save.token.address') }}" method="POST">
        @csrf
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <label for="token_address">@lang('Your Token Address')</label>
                    <input type="text" class="form-control" id="token_address" name="token_address" placeholder="Enter your token address" required>
                    <small class="text-muted">@lang('This address will be used to receive your CCL tokens. Once submitted, it cannot be changed.')</small>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn--base w-100">@lang('Submit Address')</button>
            </div>
        </div>
    </form>
</div>
@else
<div class="alert alert-info mb-4" role="alert">
    <strong>@lang('Token Address:')</strong> {{ auth()->user()->ccl_token_address }}
</div>
@endif

<div class="card custom--card mb-4">
    <div class="card-header">
        <h5 class="card-title">@lang('My Tokens')</h5>
    </div>
    <div class="card-body">
        <div class="gateway-card">
            <div class="row justify-content-center gy-sm-2 gy-2 deposit-row">
                <div class="col-lg-12">
                    <div class="token-info-card compact-card">
                        <div class="row">
                            <div class="col-md-12">
                                <!-- Regular view (visible on larger screens) -->
                                <div class="table-responsive d-none d-md-block">
                                    <table class="table custom--table">
                                        <thead>
                                            <tr>
                                                <th>@lang('Date')</th>
                                                <th>@lang('Token Name')</th>
                                                <th>@lang('Token Symbol')</th>
                                                <th>@lang('Tokens')</th>
                                                <th>@lang('Token Transfer')</th>
                                                <th>@lang('Value (USD)')</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if(count($tokenPurchases) > 0)
                                                @foreach($tokenPurchases as $purchase)
                                                <tr>
                                                    <td>
                                                        {{ showDateTime($purchase->created_at) }}<br>
                                                        <small>{{ diffForHumans($purchase->created_at) }}</small>
                                                    </td>
                                                    <td>
                                                        {{ $purchase->token_name }}
                                                    </td>
                                                    <td>
                                                        {{ $purchase->token_symbol }}
                                                    </td>
                                                    <td>
                                                        {{ number_format($purchase->tokens, 0) }}
                                                    </td>
                                                    <td>
                                                        @if($purchase->token_transfer_status == \App\Models\UserTokenPurchase::STATUS_COMPLETED)
                                                            <span style="color: green;">Completed</span>
                                                        @else
                                                            <span style="color: red;">Pending</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        ${{ number_format($purchase->amount, 2) }}
                                                    </td>
                                                </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="6" class="text-center">@lang('No tokens purchased yet')</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="6" style="text-align: right;"><strong>@lang('Total') @if($pendingTokenSymbol) {{ $pendingTokenSymbol }} @endif @lang('Tokens'): {{ number_format($totalTokens, 0) }}</strong></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                <!-- Mobile view (stacked) -->
                                <div class="token-info-mobile d-md-none">
                                    @if(count($tokenPurchases) > 0)
                                        @foreach($tokenPurchases as $purchase)
                                        <div class="mobile-token-item mb-3">
                                            <div class="mobile-info-item">
                                                <div class="mobile-info-label">@lang('Date')</div>
                                                <div class="mobile-info-value">
                                                    {{ showDateTime($purchase->created_at) }}
                                                    <small class="d-block mt-1">{{ diffForHumans($purchase->created_at) }}</small>
                                                </div>
                                            </div>
                                            <div class="mobile-info-item">
                                                <div class="mobile-info-label">@lang('Token Name')</div>
                                                <div class="mobile-info-value">{{ $purchase->token_name }}</div>
                                            </div>
                                            <div class="mobile-info-item">
                                                <div class="mobile-info-label">@lang('Token Symbol')</div>
                                                <div class="mobile-info-value">{{ $purchase->token_symbol }}</div>
                                            </div>
                                            <div class="mobile-info-item">
                                                <div class="mobile-info-label">@lang('Tokens')</div>
                                                <div class="mobile-info-value">{{ number_format($purchase->tokens, 0) }}</div>
                                            </div>
                                            <div class="mobile-info-item">
                                                <div class="mobile-info-label">@lang('Token Transfer')</div>
                                                <div class="mobile-info-value">
                                                    @if($purchase->token_transfer_status == \App\Models\UserTokenPurchase::STATUS_COMPLETED)
                                                        <span style="color: green;">Completed</span>
                                                    @else
                                                        <span style="color: red;">Pending</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="mobile-info-item">
                                                <div class="mobile-info-label">@lang('Value (USD)')</div>
                                                <div class="mobile-info-value">${{ number_format($purchase->amount, 2) }}</div>
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <div class="text-center py-3">@lang('No tokens purchased yet')</div>
                                    @endif

                                    <div class="mobile-total-tokens mt-3 text-center">
                                        <strong>@lang('Total') @if($pendingTokenSymbol) {{ $pendingTokenSymbol }} @endif @lang('Tokens'): {{ number_format($totalTokens, 0) }}</strong>
                                    </div>
                                </div>
                                @if($tokenPurchases->hasPages())
                                <div class="mt-4 pagination-container">
                                    {{ paginateLinks($tokenPurchases) }}
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



@endsection

@push('style')
<style>
    /* Custom card styles for first card */
    .card.custom--card:first-of-type .card-header {
        padding: 10px 20px;
    }

    .card.custom--card:first-of-type .card-body {
        padding: 5px;
    }

    /* Token address form styles */
    .token-address-form {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        margin-top: 0;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .token-address-form label {
        color: #7c97bb;
        font-size: 14px;
        margin-bottom: 8px;
        display: block;
    }

    .token-address-form .form-control {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #fff;
        padding: 10px 15px;
        border-radius: 5px;
        height: auto;
    }

    .token-address-form .form-control:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        box-shadow: 0 0 0 0.2rem rgba(190, 132, 0, 0.25);
    }

    .token-address-form .text-muted {
        color: #7c97bb !important;
        font-size: 12px;
        margin-top: 5px;
    }

    .alert-info {
        background-color: rgba(23, 162, 184, 0.2);
        border-color: rgba(23, 162, 184, 0.3);
        color: #d9edf7;
        margin-top: 0;
        padding: 12px 15px;
    }

    /* Gateway card styles */
    .gateway-card {
        background-color: transparent;
        padding: 10px;
    }

    /* Token info card styles */
    .token-info-card {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .compact-card {
        padding: 15px;
    }

    .label-text {
        color: #7c97bb;
        font-size: 14px;
    }

    .value-text {
        color: #fff;
        font-size: 16px;
        font-weight: 500;
    }

    /* Mobile view styles */
    .token-info-mobile {
        padding: 5px;
    }

    .mobile-token-item {
        background-color: rgba(255, 255, 255, 0.03);
        border-radius: 5px;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 5px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-info-item:last-child {
        border-bottom: none;
    }

    .mobile-info-label {
        color: #7c97bb;
        font-size: 13px;
        font-weight: 500;
        width: 40%;
    }

    .mobile-info-value {
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        width: 60%;
        text-align: right;
    }

    .mobile-total-tokens {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 5px;
        padding: 10px;
        font-size: 16px;
        color: #fff;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Button styles */
    .btn--base {
        background-color: #BE8400;
        border-color: #BE8400;
        color: #fff;
        padding: 6px 20px;
        border-radius: 5px;
    }

    .btn--base:hover {
        background-color: #9e6e00;
        border-color: #9e6e00;
        color: #fff;
    }

    /* Table styles */
    .table {
        color: #fff;
        background-color: transparent;
    }

    .table thead th {
        background-color: rgba(255, 255, 255, 0.05);
        color: #7c97bb;
        font-weight: 500;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 10px;
    }

    .table tbody tr {
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .table tbody tr:last-child {
        border-bottom: none;
    }

    .table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.03);
    }

    .table td {
        padding: 12px 10px;
        vertical-align: middle;
    }

    /* Responsive table styles */
    .table-responsive {
        overflow-x: auto;
        margin-bottom: 0;
        width: 100%;
        -webkit-overflow-scrolling: touch;
        border-radius: 5px;
        position: relative;
    }

    /* Ensure table takes full width of container */
    .table {
        width: 100%;
        min-width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    /* Ensure consistent cell styling */
    .table thead th,
    .table tbody td {
        white-space: normal;
        word-break: break-word;
    }

    /* Ensure table is scrollable on all devices */
    @media (max-width: 1199px) {
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
            max-width: 100%;
        }

        .table {
            min-width: 800px;
            width: 100%;
        }

        /* Custom table styles for better display */
        .custom--table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom--table thead th,
        .custom--table tbody td {
            padding: 10px 6px;
            font-size: 13px;
            white-space: nowrap;
        }
    }

    /* Specific 1024px screen fixes */
    @media (width: 1024px) {
        .table-responsive {
            overflow-x: auto !important;
            width: 100%;
        }

        .custom--table {
            min-width: 800px;
        }
    }

    /* We don't need mobile table styles anymore since we're using a separate mobile view */

    /* Responsive styles for mobile */
    @media (max-width: 767px) {
        .gateway-card {
            padding: 5px;
        }
        .token-info-card {
            padding: 15px 10px;
        }
        .card-body {
            padding: 15px !important;
        }
        .label-text, .value-text {
            font-size: 13px;
        }

        /* Token address form responsive styles */
        .token-address-form {
            padding: 15px 10px;
        }

        .token-address-form .row {
            margin: 0;
        }

        .token-address-form .col-md-8,
        .token-address-form .col-md-4 {
            padding: 0;
        }

        .token-address-form .col-md-4 {
            margin-top: 15px;
        }

        .token-address-form label {
            font-size: 13px;
        }

        .token-address-form .text-muted {
            font-size: 11px;
        }

        /* Mobile optimizations for the token info display */
        .mobile-info-item {
            padding: 10px 5px;
        }
        .mobile-info-label {
            font-size: 13px;
            width: 40%;
        }
        .mobile-info-value {
            font-size: 13px;
            width: 60%;
        }

        /* Mobile token item spacing */
        .mobile-token-item {
            margin-bottom: 15px;
        }

        /* Total tokens display on mobile */
        .mobile-total-tokens {
            margin-top: 15px;
            padding: 12px 10px;
        }
    }

    /* Small mobile devices */
    @media (max-width: 480px) {
        .badge--success, .badge--primary, .badge--warning {
            padding: 4px 8px;
            font-size: 11px;
        }
        /* Further optimizations for small devices */
        .card-header h5.card-title {
            font-size: 16px;
            flex-direction: column;
            align-items: flex-start;
        }
        .card-header h5.card-title span:last-child {
            margin-top: 5px;
            font-size: 12px;
        }
        .token-info-card {
            padding: 10px 5px;
        }

        /* Smaller mobile info items */
        .mobile-info-item {
            padding: 8px 3px;
        }
        .mobile-info-label {
            font-size: 12px;
            width: 35%;
        }
        .mobile-info-value {
            font-size: 12px;
            width: 65%;
        }

        /* Adjust total tokens display */
        .mobile-total-tokens {
            font-size: 14px;
            padding: 10px 8px;
        }
    }

    /* Extra small mobile devices */
    @media (max-width: 375px) {
        .badge--success, .badge--primary, .badge--warning {
            padding: 3px 6px;
            font-size: 10px;
        }
        .card-header h5.card-title {
            font-size: 15px;
        }

        /* Even smaller mobile info items */
        .mobile-info-item {
            padding: 7px 2px;
        }
        .mobile-info-label {
            font-size: 11px;
            width: 35%;
        }
        .mobile-info-value {
            font-size: 11px;
            width: 65%;
        }

        /* Tighter spacing for very small screens */
        .token-info-card {
            padding: 8px 3px;
        }

        /* Adjust mobile token items */
        .mobile-token-item {
            padding: 8px 5px;
        }

        /* Smaller total tokens display */
        .mobile-total-tokens {
            font-size: 13px;
            padding: 8px 5px;
        }
    }

    /* Very small mobile devices */
    @media (max-width: 320px) {
        .mobile-info-label {
            font-size: 10px;
        }
        .mobile-info-value {
            font-size: 10px;
        }
        .card-header h5.card-title {
            font-size: 14px;
        }

        /* Minimal padding for very small screens */
        .mobile-info-item {
            padding: 6px 2px;
        }

        /* Adjust mobile token items */
        .mobile-token-item {
            padding: 6px 3px;
        }

        /* Smallest total tokens display */
        .mobile-total-tokens {
            font-size: 12px;
            padding: 6px 3px;
        }
    }

    /* Specific tablet device optimizations */
    @media only screen and (min-width: 768px) and (max-width: 1024px) {
        /* Optimize card for tablets */
        .card.custom--card .card-body {
            padding: 15px 10px !important;
        }
        .token-info-card {
            padding: 18px 15px;
        }
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
        }
        .table {
            min-width: 800px;
            width: 100%;
        }
        .table td {
            padding: 14px 12px;
        }
        .table thead th {
            padding: 12px;
        }
        .card.custom--card {
            margin: 15px 0;
            width: 100%;
        }
        .card.custom--card .card-header {
            padding: 12px 20px;
        }
        .card.custom--card .card-title {
            font-size: 18px;
        }
    }

    /* Specific tablet device dimensions */
    @media (width: 768px) and (height: 1024px),
           (width: 820px) and (height: 1180px),
           (width: 912px) and (height: 1368px),
           (width: 853px) and (height: 1280px),
           (width: 1024px) and (height: 1366px) {
        .card.custom--card {
            width: 100%;
        }
        .table-responsive {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
        }
        .table {
            min-width: 800px;
        }
    }

    /* Tablet landscape specific adjustments */
    @media (min-width: 992px) and (max-width: 1024px) {
        .token-info-card {
            padding: 20px;
        }
        .table td {
            padding: 15px;
        }
        .table thead th {
            padding: 12px 15px;
        }
        .table-responsive {
            overflow-x: auto !important;
            width: 100%;
        }
    }

    /* iPad Pro and larger tablets */
    @media (min-width: 1025px) and (max-width: 1366px) {
        .token-info-card {
            padding: 22px;
        }
        .table-responsive {
            overflow-x: auto;
            width: 100%;
        }
    }

    /* Pagination responsive styles */
    /* Hide unnecessary elements */
    .pagination-wrapper .d-flex.justify-content-between.flex-fill,
    .pagination-container .d-flex.justify-content-between.flex-fill {
        display: none !important;
    }

    /* Main container for pagination */
    .pagination-wrapper,
    .pagination-container {
        width: 100%;
        overflow-x: auto;
        padding: 5px 0;
        margin: 10px 0;
        scrollbar-width: thin;
    }

    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        display: block !important;
        width: 100%;
    }

    /* Center the pagination buttons */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-wrapper nav,
    .pagination-wrapper .pagination,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-container nav,
    .pagination-container .pagination,
    .pagination-container nav > ul {
        display: flex;
        justify-content: center !important;
        width: 100%;
    }

    /* Improved pagination styling for all devices */
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
        margin: 0;
        padding: 0;
        justify-content: center;
        max-width: 100%;
    }

    /* Handle large number of pagination items - general fallback */
    .pagination.pagination-sm {
        justify-content: center;
        padding: 5px 0;
    }

    /* Ensure consistent spacing between pagination items */
    .pagination .page-item {
        margin: 3px;
    }

    /* Style for pagination links */
    .pagination .page-item .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        padding: 0 10px;
        border-radius: 4px !important;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    /* Style for forward and back buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item.previous .page-link,
    .pagination .page-item.next .page-link {
        font-size: 14px;
        padding: 0 12px;
        min-width: 40px; /* Slightly wider for navigation buttons */
    }

    /* Ensure arrow icons are visible */
    .pagination .page-item .page-link span[aria-hidden="true"] {
        display: inline-block;
        line-height: 1;
    }

    /* Style for disabled pagination items */
    .pagination .page-item.disabled .page-link {
        background-color: hsl(0deg 0% 100% / 40%);
        opacity: 0.7;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 575px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 3px 0;
            margin: 8px 0;
        }

        .pagination {
            gap: 3px;
        }

        .pagination .page-item {
            margin: 2px;
        }

        .pagination .page-item .page-link {
            min-width: 32px;
            height: 32px;
            font-size: 13px;
        }

        /* Adjust forward/back buttons on small screens */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 36px;
            padding: 0 10px;
        }
    }

    /* Responsive adjustments for very small screens */
    @media (max-width: 375px) {
        .pagination .page-item .page-link {
            min-width: 30px;
            height: 30px;
            font-size: 12px;
            padding: 0 8px;
        }
    }

    /* Tablet-specific adjustments */
    @media (min-width: 768px) and (max-width: 1024px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 8px 0;
            margin: 12px 0;
        }

        .pagination {
            gap: 6px;
        }

        .pagination .page-item {
            margin: 3px;
        }

        .pagination .page-item .page-link {
            min-width: 38px;
            height: 38px;
            font-size: 15px;
        }

        /* Adjust forward/back buttons on tablets */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 42px;
            padding: 0 12px;
            font-size: 15px;
        }
    }
</style>
@endpush
