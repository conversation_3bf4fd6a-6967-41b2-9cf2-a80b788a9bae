@extends('admin.layouts.app')
@section('panel')
    <div class="row mb-none-30">
        <div class="col-lg-12">
            <div class="card">
                <form action="{{ route('admin.setting.trending-articles.update') }}" method="post">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>@lang('Homepage Trending Articles Count')</label>
                                    <input type="number" name="homepage_trending_articles_count" class="form-control" value="{{ $general->homepage_trending_articles_count }}" min="1" max="500" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn--primary w-100 h-45">@lang('Submit')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Manage Trending Articles')</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap justify-content-end mb-3">
                        <div class="d-inline">
                            <form action="{{ route('admin.setting.trending-articles') }}" method="GET" class="d-flex">
                                <div class="input-group justify-content-end">
                                    <input type="text" name="search" class="form-control bg--white" placeholder="@lang('Search')..." value="{{ $search ?? '' }}">
                                    <button type="submit" class="btn btn--primary input-group-text"><i class="fas fa-search"></i></button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">

                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th>@lang('Rank')</th>
                                    <th>@lang('Article')</th>
                                    <th>@lang('User Votes')</th>
                                    <th>@lang('Admin Votes')</th>
                                    <th>@lang('Total Votes')</th>
                                    <th>@lang('Admin Vote')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($articles as $index => $article)
                                <tr>
                                    <td>{{ $article->global_rank }}</td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold">{{ __($article->data_values->title) }}</span>
                                            <small>{{ route('blog.details', $article->slug) }}</small>
                                        </div>
                                    </td>
                                    <td>{{ $article->regular_votes }}</td>
                                    <td>{{ $article->admin_trend_votes }}</td>
                                    <td>{{ $article->vote_count }}</td>
                                    <td>
                                        @php
                                            $isTrending = isset($article->data_values->trending) &&
                                                ($article->data_values->trending == true ||
                                                 $article->data_values->trending == 1 ||
                                                 $article->data_values->trending == '1');
                                        @endphp
                                        <button type="button" class="btn btn-sm btn-outline--primary editBtn"
                                            data-id="{{ $article->id }}"
                                            data-title="{{ $article->data_values->title }}"
                                            data-trend-votes="{{ $article->admin_trend_votes }}"
                                        >
                                            <i class="las la-fire"></i> @lang('Vote')
                                        </button>
                                    </td>
                                    <td>
                                        <a href="{{ route('blog.details', $article->slug) }}" target="_blank" class="btn btn-sm btn-outline--primary">
                                            <i class="las la-eye text--shadow"></i> @lang('View')
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">@lang('No articles found')</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($articles->hasPages())
                <div class="card-footer py-4">
                    {{ paginateLinks($articles) }}
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Trending Modal -->
    <div class="modal fade" id="trendingModal" tabindex="-1" aria-labelledby="trendingModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.setting.trending-articles.update') }}" method="POST" id="trendVotesForm">
                    @csrf
                    <input type="hidden" name="article_id" id="article_id">
                    <div class="modal-header">
                        <h5 class="modal-title" id="trendingModalLabel">@lang('Add Votes')</h5>
                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="article_title" class="form-label">@lang('Article')</label>
                            <input type="text" class="form-control" id="article_title" readonly>
                        </div>
                        <div class="form-group">
                            <label for="trend_votes" class="form-label">@lang('Admin Votes')</label>
                            <input type="number" name="trend_votes" id="trend_votes" class="form-control" min="0" value="0">
                            <small class="text-muted">@lang('Number of admin votes to add (will be counted with user votes)')</small>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn--primary">@lang('Submit')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
    (function($) {
        "use strict";

        $('.editBtn').on('click', function() {
            var articleId = $(this).data('id');
            var articleTitle = $(this).data('title');
            var trendVotes = $(this).data('trend-votes') || 0;

            $('#article_id').val(articleId);
            $('#article_title').val(articleTitle);
            $('#trend_votes').val(trendVotes);

            $('#trendingModal').modal('show');
        });

        // Handle form submission
        $('#trendVotesForm').on('submit', function() {
            // Show loading indicator or disable button if needed
            $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="la la-spinner fa-spin"></i> Processing...');
        });

    })(jQuery);
</script>
@endpush