<?php

namespace App\Http\Controllers;

use App\Models\TokenVote;
use App\Models\DexscreenerToken;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class TokenVoteController extends Controller
{
    /**
     * Check multiple tokens for votes at once (batch operation)
     */
    public function batchCheckVote(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        $request->validate([
            'tokens' => 'required|array',
            'tokens.*.chain_id' => 'required|string',
            'tokens.*.token_address' => 'required|string',
        ]);

        $tokens = $request->tokens;
        $ipAddress = $request->ip();
        $today = Carbon::now()->format('Y-m-d');
        $results = [];

        try {
            // Get all user's votes for today in one query
            $userVotes = TokenVote::where('user_id', auth()->id())
                ->where('voted_at', $today)
                ->where('used_trend_vote', false)
                ->get();

            // Create lookup maps for faster checking
            $positiveVoteMap = [];
            $negativeVoteMap = [];

            foreach ($userVotes as $vote) {
                $key = $vote->chain_id . '_' . $vote->token_address;

                if ($vote->is_negative) {
                    $negativeVoteMap[$key] = true;
                } else {
                    $positiveVoteMap[$key] = true;
                }
            }

            // Check each token against the maps
            foreach ($tokens as $token) {
                $chainId = $token['chain_id'];
                $tokenAddress = $token['token_address'];
                $key = $chainId . '_' . $tokenAddress;

                $results[] = [
                    'chain_id' => $chainId,
                    'token_address' => $tokenAddress,
                    'has_voted' => isset($positiveVoteMap[$key]),
                    'has_negative_voted' => isset($negativeVoteMap[$key])
                ];
            }

            return response()->json([
                'success' => true,
                'results' => $results
            ]);
        } catch (\Exception $e) {
            Log::error('Error in batchCheckVote method: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'An error occurred while checking vote status'
            ], 500);
        }
    }
    public function vote(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            $notify[] = ['error', 'You must be logged in to vote for tokens'];
            return back()->withNotify($notify);
        }

        $chainId = $request->chain_id;
        $tokenAddress = $request->token_address;
        $ipAddress = $request->ip();
        $today = Carbon::now()->format('Y-m-d');

        // Verify token exists
        $token = DexscreenerToken::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->first();

        if (!$token) {
            $notify[] = ['error', 'Token not found'];
            return back()->withNotify($notify);
        }

        // Check if user has already voted today
        $existingVote = TokenVote::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->where(function($query) use ($ipAddress) {
                $query->where('ip_address', $ipAddress)
                      ->orWhere('ip_address', 'like', $ipAddress . '_pos_%');
            })
            ->where('voted_at', $today)
            ->where('used_trend_vote', false)
            ->where('is_negative', false)
            ->first();

        if ($existingVote) {
            $notify[] = ['error', 'You have already voted for this token today'];
            return back()->withNotify($notify);
        }

        try {
            // Record the vote with a unique suffix to avoid unique constraint issues
            // Since the unique constraint doesn't include is_negative, we need to make the IP unique
            // for each vote type (positive/negative)
            $modifiedIp = $ipAddress . '_pos_' . uniqid();

            $vote = TokenVote::create([
                'token_id' => $token->id,
                'chain_id' => $chainId,
                'token_address' => $tokenAddress,
                'user_id' => auth()->id(),
                'ip_address' => $modifiedIp, // Use modified IP to bypass unique constraint
                'voted_at' => $today,
                'used_trend_vote' => false,
                'is_negative' => false, // Explicitly set to false
            ]);

            $notify[] = ['success', 'Your vote has been counted.'];
            return back()->withNotify($notify);
        } catch (\Exception $e) {
            Log::error('Error in vote method: ' . $e->getMessage());

            $notify[] = ['error', 'An error occurred while processing your vote. Please try again.'];
            return back()->withNotify($notify);
        }
    }

    public function checkVote(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        $chainId = $request->chain_id;
        $tokenAddress = $request->token_address;
        $ipAddress = $request->ip();
        $today = Carbon::now()->format('Y-m-d');

        try {
            // Check if user has already voted today (positive vote)
            // Need to check for both regular IP and any IP with '_pos_' prefix
            $hasVoted = TokenVote::where('chain_id', $chainId)
                ->where('token_address', $tokenAddress)
                ->where(function($query) use ($ipAddress) {
                    $query->where('ip_address', $ipAddress)
                          ->orWhere('ip_address', 'like', $ipAddress . '_pos_%');
                })
                ->where('voted_at', $today)
                ->where('used_trend_vote', false)
                ->where('is_negative', false)
                ->exists();

            // Check if user has already voted negatively today
            // Need to check for both regular IP and any IP with '_neg' suffix
            $hasNegativeVoted = TokenVote::where('chain_id', $chainId)
                ->where('token_address', $tokenAddress)
                ->where(function($query) use ($ipAddress) {
                    $query->where('ip_address', $ipAddress)
                          ->orWhere('ip_address', 'like', $ipAddress . '_neg%');
                })
                ->where('voted_at', $today)
                ->where('used_trend_vote', false)
                ->where('is_negative', true)
                ->exists();

            return response()->json([
                'has_voted' => $hasVoted,
                'has_negative_voted' => $hasNegativeVoted
            ]);
        } catch (\Exception $e) {
            Log::error('Error in checkVote method: ' . $e->getMessage());

            // Return a default response to prevent errors
            return response()->json([
                'has_voted' => false,
                'has_negative_voted' => false,
                'error' => 'An error occurred while checking vote status'
            ]);
        }
    }

    public function negativeVote(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            $notify[] = ['error', 'You must be logged in to vote for tokens'];
            return back()->withNotify($notify);
        }

        $chainId = $request->chain_id;
        $tokenAddress = $request->token_address;
        $ipAddress = $request->ip();
        $today = Carbon::now()->format('Y-m-d');

        // Verify token exists
        $token = DexscreenerToken::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->first();

        if (!$token) {
            $notify[] = ['error', 'Token not found'];
            return back()->withNotify($notify);
        }

        // We allow both positive and negative votes for the same token
        // No need to check for positive votes

        // Check if user has already voted negatively today
        // Need to check for both regular IP and any IP with '_neg' suffix
        $hasNegativeVoted = TokenVote::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->where(function($query) use ($ipAddress) {
                $query->where('ip_address', $ipAddress)
                      ->orWhere('ip_address', 'like', $ipAddress . '_neg%');
            })
            ->where('voted_at', $today)
            ->where('used_trend_vote', false)
            ->where('is_negative', true)
            ->exists();

        if ($hasNegativeVoted) {
            $notify[] = ['error', 'You have already voted negatively for this token today'];
            return back()->withNotify($notify);
        }

        try {
            // Record the negative vote with a unique suffix to bypass the unique constraint
            // Add a suffix to the IP address to make it unique
            $modifiedIp = $ipAddress . '_neg_' . uniqid();

            $vote = TokenVote::create([
                'token_id' => $token->id,
                'chain_id' => $chainId,
                'token_address' => $tokenAddress,
                'user_id' => auth()->id(),
                'ip_address' => $modifiedIp,
                'voted_at' => $today,
                'used_trend_vote' => false,
                'is_negative' => true,
            ]);

            $notify[] = ['success', 'Your negative vote has been counted!'];
            return back()->withNotify($notify);
        } catch (\Exception $e) {
            Log::error('Error in negativeVote method: ' . $e->getMessage());

            $notify[] = ['error', 'An error occurred while processing your negative vote. Please try again.'];
            return back()->withNotify($notify);
        }
    }

    public function useTrendVote(Request $request)
    {
        // Check if the user is logged in
        if (!auth()->check()) {
            $notify[] = ['error', 'You must be logged in to use trend votes'];
            return back()->withNotify($notify);
        }

        $chainId = $request->chain_id;
        $tokenAddress = $request->token_address;
        $ipAddress = $request->ip();
        $today = Carbon::now()->format('Y-m-d');
        $user = auth()->user();

        // Get quantity of votes to use (default to 1 if not specified)
        $quantity = max(1, intval($request->quantity));

        // Verify token exists
        $token = DexscreenerToken::where('chain_id', $chainId)
            ->where('token_address', $tokenAddress)
            ->first();

        if (!$token) {
            $notify[] = ['error', 'Token not found'];
            return back()->withNotify($notify);
        }

        // Check if user has enough trend votes available
        if ($user->trend_votes < $quantity) {
            $notify[] = ['error', 'You don\'t have enough trend votes. You requested ' . $quantity . ' but have ' . $user->trend_votes . ' available.'];
            return back()->withNotify($notify);
        }

        try {
            $createdVotes = [];

            // Apply multiple votes at once
            for ($i = 0; $i < $quantity; $i++) {
                // Generate a unique random suffix for the IP to bypass the unique constraint
                $randomSuffix = '_tv_' . uniqid();
                $modifiedIp = $ipAddress . $randomSuffix;

                // Record the vote using trend vote - with modified IP to bypass unique constraint
                $vote = TokenVote::create([
                    'token_id' => $token->id,
                    'chain_id' => $chainId,
                    'token_address' => $tokenAddress,
                    'user_id' => $user->id,
                    'ip_address' => $modifiedIp, // Use modified IP to bypass unique constraint
                    'voted_at' => $today,
                    'used_trend_vote' => true,
                    'is_negative' => false, // Explicitly set to false
                ]);

                $createdVotes[] = $vote->id;
            }

            // Decrease user's trend votes
            $user->trend_votes -= $quantity;
            $user->save();

            $notify[] = ['success', 'Your ' . $quantity . ' trend vote(s) have been counted!.'];
            return back()->withNotify($notify);
        } catch (\Exception $e) {
            Log::error('Error in useTrendVote method: ' . $e->getMessage());

            $notify[] = ['error', 'An error occurred while processing your trend votes. Please try again.'];
            return back()->withNotify($notify);
        }
    }
}
