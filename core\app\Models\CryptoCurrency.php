<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CryptoCurrency extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'symbol',
        'price',
        'percent_change_24h',
        'market_cap',
        'volume_24h',
        'last_updated'
    ];

    protected $casts = [
        'price' => 'float',
        'percent_change_24h' => 'float',
        'market_cap' => 'float',
        'volume_24h' => 'float',
        'last_updated' => 'datetime'
    ];
} 