@extends('admin.layouts.app')
@section('panel')

    <div class="row mb-none-30">
        <div class="col-xl-3 col-lg-4 mb-30">

            <div class="card b-radius--5 overflow-hidden">
                <div class="card-body p-0">
                    <div class="d-flex p-3 bg--primary align-items-center">
                        <div class="avatar avatar--lg">
                            <img src="{{ getImage(getFilePath('adminProfile').'/'. $admin->image,getFileSize('adminProfile'))}}" alt="Image">
                        </div>
                        <div class="ps-3">
                            <h4 class="text--white">{{__($admin->name)}}</h4>
                        </div>
                    </div>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            @lang('Name')
                            <span class="fw-bold">{{__($admin->name)}}</span>
                        </li>

                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            @lang('Username')
                            <span  class="fw-bold">{{__($admin->username)}}</span>
                        </li>

                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            @lang('Email')
                            <span  class="fw-bold">{{$admin->email}}</span>
                        </li>

                    </ul>
                </div>
            </div>
        </div>

        <div class="col-xl-9 col-lg-8 mb-30">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-4 border-bottom pb-2">@lang('Profile Information')</h5>

                    <form action="{{ route('admin.profile.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-xxl-4 col-lg-6">
                                <div class="form-group">
                                    <label>@lang('Image')</label>
                                    <x-image-uploader image="{{ $admin->image }}" class="w-100" type="adminProfile" :required=false />
                                </div>
                            </div>
                            <div class="col-xxl-8 col-lg-6">
                                <div class="form-group ">
                                    <label>@lang('Name')</label>
                                    <input class="form-control" type="text" name="name" value="{{ $admin->name }}" required>
                                </div>
                                <div class="form-group">
                                    <label>@lang('Email')</label>
                                    <input class="form-control" type="email" name="email" value="{{ $admin->email }}" required>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn--primary h-45 w-100">@lang('Submit')</button>
                    </form>
                </div>
            </div>

            <div class="card mt-30">
                <div class="card-body">
                    <h5 class="card-title mb-4 border-bottom pb-2">@lang('Two Factor Authentication')</h5>

                    <div class="row">
                        @if(isset($admin->ts) && $admin->ts == 1)
                            <div class="col-md-12">
                                <div class="card border--primary">
                                    <div class="card-header bg--primary">
                                        <h5 class="card-title text-white">@lang('Disable 2FA Security')</h5>
                                    </div>
                                    <form action="{{ route('admin.twofactor.disable') }}" method="POST">
                                        <div class="card-body">
                                            @csrf
                                            <div class="form-group">
                                                <label class="form-label">@lang('Google Authenticator OTP')</label>
                                                <input class="form-control" name="code" required type="text">
                                            </div>
                                            <button class="btn btn--primary h-45 w-100" type="submit">@lang('Submit')</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        @else
                            <div class="col-md-6">
                                <div class="card border--primary">
                                    <div class="card-header bg--primary">
                                        <h5 class="card-title text-white">@lang('Add Your Account')</h5>
                                    </div>
                                    <div class="card-body">
                                        <h6 class="mb-3">
                                            @lang('Use the QR code or setup key on your Google Authenticator app to add your account.')
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-12 text-center">
                                                <img class="mx-auto" src="{{ $qrCodeUrl }}">
                                            </div>
                                            <div class="col-md-12 mt-3">
                                                <label class="form-label">@lang('Setup Key')</label>
                                                <div class="input-group">
                                                    <input class="form-control referralURL" name="key" readonly type="text" value="{{ $secret }}">
                                                    <button class="input-group-text copytext" id="copyBoard" type="button"> <i class="fa fa-copy"></i> </button>
                                                </div>
                                            </div>
                                        </div>
                                        <label class="mt-3"><i class="fa fa-info-circle"></i> @lang('Help')</label>
                                        <p>@lang('Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.') <a class="text--base" href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=en" target="_blank">@lang('Download')</a></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border--primary">
                                    <div class="card-header bg--primary">
                                        <h5 class="card-title text-white">@lang('Enable 2FA Security')</h5>
                                    </div>
                                    <form action="{{ route('admin.twofactor.enable') }}" method="POST">
                                        <div class="card-body">
                                            @csrf
                                            <input name="key" type="hidden" value="{{ $secret }}">
                                            <div class="form-group">
                                                <label class="form-label">@lang('Google Authenticator OTP')</label>
                                                <input class="form-control" name="code" required type="text">
                                            </div>
                                            <button class="btn btn--primary h-45 w-100" type="submit">@lang('Submit')</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{route('admin.password')}}" class="btn btn-sm btn-outline--primary"><i class="las la-key"></i>@lang('Password Setting')</a>
@endpush
@push('style')
    <style>
        .list-group-item:first-child{
            border-top-left-radius:unset;
            border-top-right-radius:unset;
        }
        .copied::after {
            background-color: #{{ gs('base_color') ?? '36454F' }};
            content: "Copied";
            position: absolute;
            top: -10px;
            right: 12px;
            padding: 3px 10px;
            border-radius: 3px;
            color: #fff;
        }
    </style>
@endpush

@push('script')
    <script>
        (function($) {
            "use strict";
            $('#copyBoard').click(function() {
                var copyText = document.getElementsByClassName("referralURL");
                copyText = copyText[0];
                copyText.select();
                copyText.setSelectionRange(0, 99999);
                /*For mobile devices*/
                document.execCommand("copy");
                copyText.blur();
                this.classList.add('copied');
                setTimeout(() => this.classList.remove('copied'), 1500);
            });
        })(jQuery);
    </script>
@endpush
