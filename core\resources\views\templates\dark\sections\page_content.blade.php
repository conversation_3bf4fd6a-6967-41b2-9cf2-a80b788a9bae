@php
    // Get current page slug from the route
    $currentPageSlug = '';
    $routeParams = request()->route()->parameters();
    
    if (isset($routeParams['slug'])) {
        $currentPageSlug = $routeParams['slug'];
    } elseif (url()->current() == route('home')) {
        // For homepage
        $currentPageSlug = '/';
    }
    
    // Get all content elements and filter by page_id (which now contains the slug)
    $allContents = getContent('page_content.element', false);
    $contents = [];
    
    foreach ($allContents as $content) {
        if (isset($content->data_values->page_id) && $content->data_values->page_id == $currentPageSlug) {
            $contents[] = $content;
        }
    }
@endphp

@if(count($contents) > 0)
<section class="blog-details py-50">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xl-12">
                <div class="blog-item">
                    <div class="blog-item__content">
                        @foreach($contents as $content)
                            @php echo $content->data_values->content @endphp
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endif 