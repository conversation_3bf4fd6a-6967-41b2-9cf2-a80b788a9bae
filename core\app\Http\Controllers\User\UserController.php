<?php

namespace App\Http\Controllers\User;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Lib\FormProcessor;
use App\Lib\GoogleAuthenticator;
use App\Models\Deposit;
use App\Models\DeviceToken;
use App\Models\Form;
use App\Models\Referral;
use App\Models\ReferralLog;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    public function home()
    {
        $pageTitle     = 'Dashboard';
        $user          = auth()->user();

        // Calculate actual referral bonus from referral logs
        $referralBonus = ReferralLog::where('referee_id', $user->id)->sum('amount');

        $transactions = Transaction::where('user_id', $user->id)->orderBy('id', 'desc')->paginate(5);

        return view('Template::user.dashboard', compact('pageTitle', 'referralBonus', 'transactions', 'user'));
    }

    public function paymentHistory(Request $request)
    {
        $pageTitle = 'Payment History';
        $user = auth()->user();

        // Check for any pending/initiated PayPal payments that need to be processed
        $pendingDeposits = Deposit::where('user_id', $user->id)
            ->where('status', Status::PAYMENT_INITIATE)
            ->where('created_at', '>', now()->subMinutes(30))
            ->whereHas('gateway', function($query) {
                $query->where('alias', 'PaypalExpress');
            })
            ->get();

        // Process any pending PayPal payments
        foreach ($pendingDeposits as $deposit) {
            // Only process if this is a plan purchase
            if ($deposit->order_id > 0) {
                $planPurchase = \App\Models\PlanPurchase::where('id', $deposit->order_id)
                    ->where('status', Status::PENDING)
                    ->first();

                if ($planPurchase && $planPurchase->plan) {
                    // Complete the purchase
                    $planPurchase->status = Status::COMPLETED;
                    $planPurchase->save();

                    // Add trend votes to user
                    $user->trend_votes += $planPurchase->plan->trend_votes;
                    $user->save();

                    // Update deposit status
                    $deposit->status = Status::PAYMENT_SUCCESS;
                    $deposit->save();

                    // Create transaction record if it doesn't exist
                    $transaction = Transaction::where('trx', $deposit->trx)->first();
                    if (!$transaction) {
                        $transaction = new Transaction();
                        $transaction->user_id = $user->id;
                        $transaction->amount = $deposit->amount;
                        $transaction->post_balance = $user->balance;
                        $transaction->charge = $deposit->charge;
                        $transaction->trx_type = '+';
                        $transaction->details = 'Purchase: ' . $planPurchase->plan->trend_votes . ' Trend Votes';
                        $transaction->trx = $deposit->trx;
                        $transaction->remark = 'plan_purchase';
                        $transaction->save();
                    }
                }
            }
        }

        $deposits = Deposit::where('user_id', $user->id)->searchable(['trx'])->with(['gateway'])->orderBy('id', 'desc')->paginate(5);
        return view('Template::user.payment_history', compact('pageTitle', 'deposits'));
    }

    public function show2faForm()
    {
        $ga = new GoogleAuthenticator();
        $user = auth()->user();
        $secret = $ga->createSecret();
        $qrCodeUrl = $ga->getQRCodeGoogleUrl($user->username . '@' . gs('site_name'), $secret);
        $pageTitle = '2FA Security';
        return view('Template::user.twofactor', compact('pageTitle', 'secret', 'qrCodeUrl'));
    }

    public function create2fa(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'key' => 'required',
            'code' => 'required',
        ]);
        $response = verifyG2fa($user, $request->code, $request->key);
        if ($response) {
            $user->tsc = $request->key;
            $user->ts = Status::ENABLE;
            $user->save();
            $notify[] = ['success', 'Two factor authenticator activated successfully'];
            return back()->withNotify($notify);
        } else {
            $notify[] = ['error', 'Wrong verification code'];
            return back()->withNotify($notify);
        }
    }

    public function disable2fa(Request $request)
    {
        $request->validate([
            'code' => 'required',
        ]);

        $user = auth()->user();
        $response = verifyG2fa($user, $request->code);
        if ($response) {
            $user->tsc = null;
            $user->ts = Status::DISABLE;
            $user->save();
            $notify[] = ['success', 'Two factor authenticator deactivated successfully'];
        } else {
            $notify[] = ['error', 'Wrong verification code'];
        }
        return back()->withNotify($notify);
    }


    public function transactions(Request $request)
    {
        $pageTitle    = 'Transactions';
        $remarks      = Transaction::distinct('remark')->orderBy('remark')->get('remark');
        $coins        = Transaction::distinct('currency')->orderBy('currency')->get('currency');
        $transactions = Transaction::where('user_id', auth()->id());

        if ($request->search) {
            $transactions = $transactions->where('trx', $request->search);
        }

        if ($request->type) {
            $transactions = $transactions->where('trx_type', $request->type);
        }

        if ($request->remark) {
            $transactions = $transactions->where('remark', $request->remark);
        }

        if ($request->coin_code) {
            $transactions = $transactions->where('currency', $request->coin_code);
        }

        $transactions = $transactions->orderBy('id', 'desc')->paginate(5);
        return view('Template::user.transactions', compact('pageTitle', 'transactions', 'remarks', 'coins'));
    }

    public function kycForm()
    {
        if (auth()->user()->kv == Status::KYC_PENDING) {
            $notify[] = ['error', 'Your KYC is under review'];
            return to_route('user.home')->withNotify($notify);
        }
        if (auth()->user()->kv == Status::KYC_VERIFIED) {
            $notify[] = ['error', 'You are already KYC verified'];
            return to_route('user.home')->withNotify($notify);
        }
        $pageTitle = 'KYC Form';
        $form = Form::where('act', 'kyc')->first();
        return view('Template::user.kyc.form', compact('pageTitle', 'form'));
    }

    public function kycData()
    {
        $user = auth()->user();
        $pageTitle = 'KYC Data';
        return view('Template::user.kyc.info', compact('pageTitle', 'user'));
    }

    public function kycSubmit(Request $request)
    {
        $form = Form::where('act', 'kyc')->firstOrFail();
        $formData = $form->form_data;
        $formProcessor = new FormProcessor();
        $validationRule = $formProcessor->valueValidation($formData);
        $request->validate($validationRule);
        $user = auth()->user();
        foreach (@$user->kyc_data ?? [] as $kycData) {
            if ($kycData->type == 'file') {
                fileManager()->removeFile(getFilePath('verify') . '/' . $kycData->value);
            }
        }
        $userData = $formProcessor->processFormData($request, $formData);
        $user->kyc_data = $userData;
        $user->kyc_rejection_reason = null;
        $user->kv = Status::KYC_PENDING;
        $user->save();

        $notify[] = ['success', 'KYC data submitted successfully'];
        return to_route('user.home')->withNotify($notify);
    }

    public function userData()
    {
        $user = auth()->user();

        if ($user->profile_complete == Status::YES) {
            return to_route('user.home');
        }

        $pageTitle  = 'User Data';
        $info       = json_decode(json_encode(getIpInfo()), true);
        $mobileCode = @implode(',', $info['code']);
        $countries  = json_decode(file_get_contents(resource_path('views/partials/country.json')));

        return view('Template::user.user_data', compact('pageTitle', 'user', 'countries', 'mobileCode'));
    }

    public function userDataSubmit(Request $request)
    {
        $user = auth()->user();

        if ($user->profile_complete == Status::YES) {
            return to_route('user.home');
        }

        $countryData  = (array)json_decode(file_get_contents(resource_path('views/partials/country.json')));
        $countryCodes = implode(',', array_keys($countryData));
        $mobileCodes  = implode(',', array_column($countryData, 'dial_code'));
        $countries    = implode(',', array_column($countryData, 'country'));

        $request->validate([
            'country_code' => 'required|in:' . $countryCodes,
            'country'      => 'required|in:' . $countries,
            'mobile_code'  => 'required|in:' . $mobileCodes,
            'username'     => 'required|unique:users|min:6',
            'mobile'       => ['required', 'regex:/^([0-9]*)$/', Rule::unique('users')->where('dial_code', $request->mobile_code)],
        ]);

        if (preg_match("/[^a-z0-9_]/", trim($request->username))) {
            $notify[] = ['info', 'Username can contain only small letters, numbers and underscore.'];
            $notify[] = ['error', 'No special character, space or capital letters in username.'];
            return back()->withNotify($notify)->withInput($request->all());
        }

        $user->country_code = $request->country_code;
        $user->mobile       = $request->mobile;
        $user->username     = $request->username;

        $user->address = $request->address;
        $user->city = $request->city;
        $user->state = $request->state;
        $user->zip = $request->zip;
        $user->country_name = @$request->country;
        $user->dial_code = $request->mobile_code;

        $user->profile_complete = Status::YES;
        $user->save();

        return to_route('user.home');
    }

    public function referral()
    {
        $general = gs();

        if (!$general->referral_system) {
            $notify[] = ['error', 'Sorry, the referral system is currently unavailable'];
            return back()->withNotify($notify);
        }

        $pageTitle = "Referrals";
        $maxLevel  = Referral::max('level');
        $relations = [];

        for ($label = 1; $label <= $maxLevel; $label++) {
            $relations[$label] = (@$relations[$label - 1] ? $relations[$label - 1] . '.allReferrals' : 'allReferrals');
        }

        $user = auth()->user()->load($relations);

        // Get the total earned from referrals
        $totalEarned = \App\Models\ReferralLog::where('referee_id', auth()->id())->sum('amount');

        return view('Template::user.referral.index', compact('pageTitle', 'user', 'maxLevel', 'general', 'totalEarned'));
    }

    public function referralLog()
    {
        $pageTitle = 'Referral Logs';
        $logs = ReferralLog::where('referee_id', auth()->id())
                ->where('amount', '>', 0) // Only show positive amounts (commission earned, not withdrawals)
                ->with('user')
                ->latest()
                ->paginate(getPaginate());
        $emptyMessage = 'No referral bonuses found';
        return view('Template::user.referral.logs', compact('logs', 'pageTitle', 'emptyMessage'));
    }


    public function addDeviceToken(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'token' => 'required',
        ]);

        if ($validator->fails()) {
            return ['success' => false, 'errors' => $validator->errors()->all()];
        }

        $deviceToken = DeviceToken::where('token', $request->token)->first();

        if ($deviceToken) {
            return ['success' => true, 'message' => 'Already exists'];
        }

        $deviceToken          = new DeviceToken();
        $deviceToken->user_id = auth()->user()->id;
        $deviceToken->token   = $request->token;
        $deviceToken->is_app  = Status::NO;
        $deviceToken->save();

        return ['success' => true, 'message' => 'Token saved successfully'];
    }

    public function downloadAttachment($fileHash)
    {
        $filePath = decrypt($fileHash);
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        $title = slug(gs('site_name')) . '- attachments.' . $extension;
        try {
            $mimetype = mime_content_type($filePath);
        } catch (\Exception $e) {
            $notify[] = ['error', 'File does not exists'];
            return back()->withNotify($notify);
        }
        header('Content-Disposition: attachment; filename="' . $title);
        header("Content-Type: " . $mimetype);
        return readfile($filePath);
    }

    public function submitCoinForm()
    {
        $pageTitle = 'Submit Coin';
        $blockchains = [
            'Binance Smart Chain (BSC)',
            'Ethereum (ETH)',
            'Polygon (MATIC)',
            'Solana (SOL)',
            'Avalanche (AVAX)',
            'Tron (TRX)',
            'Cardano (ADA)',
            'Polkadot (DOT)',
            'Arbitrum (ARB)',
            'Optimism (OP)',
            'Base (ETH on Base)',
            'Fantom (FTM)',
            'Cronos (CRO)',
            'Algorand (ALGO)',
            'Cosmos (ATOM)',
            'Near Protocol (NEAR)',
            'Aptos (APT)',
            'Sui (SUI)',
            'Hedera (HBAR)',
            'XRP Ledger (XRP)',
            'Stellar (XLM)',
            'Harmony (ONE)',
            'Moonbeam (GLMR)',
            'Gnosis Chain (GNO)',
            'Zilliqa (ZIL)',
            'zkSync Era (ZKS)',
            'Linea Mainnet (LINEA)',
            'Scroll (SCROLL)',
            'HECO (HT)',
            'ETHW (ETHW)',
            'KCC (KCS)',
            'FON (FON)',
            'Mantle (MNT)',
            'opBNB (OBNB)',
            'ZKFair (ZKF)',
            'Blast (BLAST)',
            'Manta Pacific (MANTA)',
            'Berachain (BERA)',
            'Abstract (Arcblock)',
            'Hashkey Chain (HSK)',
            'Sonic (SONIC)',
            'Story (STORY)',
            'Own Blockchain',
            'Other'
        ];
        $step = request()->step ?? 1;
        $coinData = session('coin_data') ?? [];

        return view('Template::user.submit_coin', compact('pageTitle', 'blockchains', 'step', 'coinData'));
    }

    public function submitCoinStep1(Request $request)
    {
        $validationRules = [
            'logo' => 'required|image|mimes:jpg,jpeg,png|max:1024|dimensions:width=512,height=512',
            'blockchain' => 'required|string',
            'blockchain_explorer_url' => 'nullable|url',
            'name' => 'required|string|max:255',
            'symbol' => 'required|string|max:20',
            'video_url' => 'nullable|url',
            'whitepaper' => 'nullable|url',
            'description' => 'required|string',
            'is_presale' => 'required|in:0,1',
            'is_fair_launch' => 'required|in:0,1',
            'presale_url' => 'nullable|url',
            'fair_launch_url' => 'nullable|url',
            'presale_end_date' => 'nullable|date_format:m/d/Y',
            'softcap' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    if ($value && !preg_match('/^(\d+)([A-Za-z]{1,4})$/', $value)) {
                        $fail('The ' . $attribute . ' must be a number followed by a currency symbol of maximum 4 letters (e.g., 50BNB, 100USDT).');
                    }
                }
            ],
            'hardcap' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    if ($value && !preg_match('/^(\d+)([A-Za-z]{1,4})$/', $value)) {
                        $fail('The ' . $attribute . ' must be a number followed by a currency symbol of maximum 4 letters (e.g., 100BNB, 200USDT).');
                    }
                }
            ]
        ];

        // If this is a presale or fair launch project, make start date required but contract address optional
        if ($request->is_presale == 1 || $request->is_fair_launch == 1) {
            $validationRules['presale_start_date'] = 'required|date_format:m/d/Y';
            $validationRules['fair_launch_start_date'] = 'nullable|date_format:m/d/Y';
            $validationRules['fair_launch_end_date'] = 'nullable|date_format:m/d/Y';
            $validationRules['contract_address'] = 'nullable|string';
        } else {
            $validationRules['presale_start_date'] = 'nullable|date_format:m/d/Y';
            $validationRules['fair_launch_start_date'] = 'nullable|date_format:m/d/Y';
            $validationRules['fair_launch_end_date'] = 'nullable|date_format:m/d/Y';
            $validationRules['contract_address'] = 'required|string';
        }

        $request->validate($validationRules);

        // Check if a token with the same contract address already exists (only if contract address is provided)
        if ($request->contract_address) {
            $existingToken = \App\Models\DexscreenerToken::where('token_address', $request->contract_address)->first();
            if ($existingToken) {
                return back()->withErrors(['contract_address' => 'A token with this contract address is already listed on our platform.'])->withInput();
            }
        }

        // Determine the start date based on project type
        $startDate = null;
        if ($request->is_presale == 1) {
            $startDate = $request->presale_start_date;
        } elseif ($request->is_fair_launch == 1) {
            // If fair_launch_start_date is provided, use it, otherwise fall back to presale_start_date
            $startDate = $request->fair_launch_start_date ?? $request->presale_start_date;
        }

        // Determine the end date based on project type
        $endDate = null;
        if ($request->is_presale == 1) {
            $endDate = $request->presale_end_date;
        } elseif ($request->is_fair_launch == 1) {
            // If fair_launch_end_date is provided, use it, otherwise fall back to presale_end_date
            $endDate = $request->fair_launch_end_date ?? $request->presale_end_date;
        }

        $coinData = [
            'blockchain' => $request->blockchain,
            'blockchain_explorer_url' => $request->blockchain_explorer_url,
            'contract_address' => $request->contract_address ?? '',  // Use empty string instead of null
            'name' => $request->name,
            'symbol' => $request->symbol,
            'video_url' => $request->video_url,
            'whitepaper' => $request->whitepaper,
            'description' => $request->description,
            'is_presale' => $request->is_presale,
            'is_fair_launch' => $request->is_fair_launch,
            'presale_start_date' => ($request->is_presale == 1 || $request->is_fair_launch == 1) ? $startDate : null,
            'presale_end_date' => ($request->is_presale == 1 || $request->is_fair_launch == 1) ? $endDate : null,
            'presale_url' => $request->is_presale == 1 ? $request->presale_url : null,
            'fair_launch_url' => $request->is_fair_launch == 1 ? $request->fair_launch_url : null,
            'softcap' => ($request->is_presale == 1 || $request->is_fair_launch == 1) ? $request->softcap : null,
            'hardcap' => ($request->is_presale == 1 || $request->is_fair_launch == 1) ? $request->hardcap : null,
        ];

        if ($request->hasFile('logo')) {
            $logoPath = fileUploader($request->logo, getFilePath('coinLogos'), getFileSize('coinLogos'));
            $coinData['logo'] = $logoPath;
        }

        session(['coin_data' => $coinData]);

        return redirect()->route('user.submit.coin.form', ['step' => 2]);
    }

    public function submitCoinStep2(Request $request)
    {
        $request->validate([
            'website' => 'nullable|url',
            'telegram' => 'nullable|string',
            'twitter' => 'nullable|string',
            'discord' => 'nullable|string',
            'facebook' => 'nullable|string',
            'reddit' => 'nullable|string',
            'linktree' => 'nullable|string',
        ]);

        $coinData = session('coin_data');

        if (!$coinData) {
            return redirect()->route('user.submit.coin.form');
        }

        $coinData = array_merge($coinData, [
            'website' => $request->website,
            'telegram' => $request->telegram,
            'twitter' => $request->twitter,
            'discord' => $request->discord,
            'facebook' => $request->facebook,
            'reddit' => $request->reddit,
            'linktree' => $request->linktree,
        ]);

        session(['coin_data' => $coinData]);

        return redirect()->route('user.submit.coin.form', ['step' => 3]);
    }

    public function submitCoinStep3(Request $request)
    {
        // Add any step 3 validations here
        $request->validate([
            // Add validation rules for step 3 fields
        ]);

        $coinData = session('coin_data');

        if (!$coinData) {
            return redirect()->route('user.submit.coin.form');
        }

        // Save the coin with all data from all steps
        $user = auth()->user();

        $submitCoin = new \App\Models\SubmitCoin();
        $submitCoin->user_id = $user->id;
        $submitCoin->blockchain = $coinData['blockchain'];
        $submitCoin->blockchain_explorer_url = $coinData['blockchain_explorer_url'] ?? null;
        $submitCoin->contract_address = $coinData['contract_address'] ?: '';  // Use empty string if null or empty
        $submitCoin->name = $coinData['name'];
        $submitCoin->symbol = $coinData['symbol'];
        $submitCoin->video_url = $coinData['video_url'] ?? null;
        $submitCoin->whitepaper = $coinData['whitepaper'] ?? null;
        $submitCoin->description = $coinData['description'];
        $submitCoin->is_presale = $coinData['is_presale'];
        $submitCoin->is_fair_launch = $coinData['is_fair_launch'];
        $submitCoin->presale_start_date = $coinData['presale_start_date'] ?? null;
        $submitCoin->presale_end_date = $coinData['presale_end_date'] ?? null;
        $submitCoin->presale_url = $coinData['is_fair_launch'] == 1 ? ($coinData['fair_launch_url'] ?? null) : ($coinData['presale_url'] ?? null);
        $submitCoin->softcap = $coinData['softcap'] ?? null;
        $submitCoin->hardcap = $coinData['hardcap'] ?? null;
        $submitCoin->logo = $coinData['logo'];
        $submitCoin->website = $coinData['website'] ?? null;
        $submitCoin->telegram = $coinData['telegram'] ?? null;
        $submitCoin->twitter = $coinData['twitter'] ?? null;
        $submitCoin->discord = $coinData['discord'] ?? null;
        $submitCoin->facebook = $coinData['facebook'] ?? null;
        $submitCoin->reddit = $coinData['reddit'] ?? null;
        $submitCoin->linktree = $coinData['linktree'] ?? null;

        // Add any step 3 fields to the model

        $submitCoin->status = 0; // Pending status
        $submitCoin->save();

        // Save social links to the new SocialLink table
        $chainIdMap = [
            'Binance Smart Chain (BSC)' => 'bsc',
            'Ethereum (ETH)' => 'ethereum',
            'Polygon (MATIC)' => 'polygon',
            'Solana (SOL)' => 'solana',
            'Avalanche (AVAX)' => 'avalanche',
            'Tron (TRX)' => 'tron',
            'Cardano (ADA)' => 'cardano',
            'Polkadot (DOT)' => 'polkadot',
            'Arbitrum (ARB)' => 'arbitrum',
            'Optimism (OP)' => 'optimism',
            'Base (ETH on Base)' => 'base',
            'Fantom (FTM)' => 'fantom',
            'Cronos (CRO)' => 'cronos',
            'Algorand (ALGO)' => 'algorand',
            'Cosmos (ATOM)' => 'cosmos',
            'Near Protocol (NEAR)' => 'near',
            'Aptos (APT)' => 'aptos',
            'Sui (SUI)' => 'sui',
            'Hedera (HBAR)' => 'hedera',
            'XRP Ledger (XRP)' => 'xrp',
            'Stellar (XLM)' => 'stellar',
            'Harmony (ONE)' => 'harmony',
            'Moonbeam (GLMR)' => 'moonbeam',
            'Gnosis Chain (GNO)' => 'gnosis',
            'Zilliqa (ZIL)' => 'zilliqa',
            'zkSync Era (ZKS)' => 'zksync',
            'Linea Mainnet (LINEA)' => 'linea',
            'Scroll (SCROLL)' => 'scroll',
            'HECO (HT)' => 'heco',
            'ETHW (ETHW)' => 'ethw',
            'KCC (KCS)' => 'kcc',
            'FON (FON)' => 'fon',
            'Mantle (MNT)' => 'mantle',
            'opBNB (OBNB)' => 'opbnb',
            'ZKFair (ZKF)' => 'zkfair',
            'Blast (BLAST)' => 'blast',
            'Manta Pacific (MANTA)' => 'manta',
            'Berachain (BERA)' => 'berachain',
            'Abstract (Arcblock)' => 'abstract',
            'Hashkey Chain (HSK)' => 'hashkey',
            'Sonic (SONIC)' => 'sonic',
            'Story (STORY)' => 'story',
            'Own Blockchain' => 'own',
            'Other' => 'other'
        ];

        $chainId = $chainIdMap[$coinData['blockchain']] ?? 'other';

        // Handle presale or fair launch tokens - ensure they are properly marked
        $tokenAddress = $coinData['contract_address'];

        // If it's a presale or fair launch token, we need to ensure it's properly marked
        if ($coinData['is_presale'] == 1 || $coinData['is_fair_launch'] == 1) {
            // If no contract address is provided, generate a temporary unique ID
            if (empty($tokenAddress)) {
                $prefix = $coinData['is_fair_launch'] == 1 ? 'fairlaunch_' : 'presale_';
                $tokenAddress = $prefix . uniqid() . '_' . $user->id;

                // Update the contract_address in the SubmitCoin record to match
                $submitCoin->contract_address = $tokenAddress;
                $submitCoin->save();
            }
            // If a contract address is provided, we'll keep it as is but ensure it's marked as presale/fair launch in metadata
        }

        // Create or update social links
        \App\Models\SocialLink::updateOrCreate(
            [
                'token_chain_id' => $chainId,
                'token_address' => $tokenAddress
            ],
            [
                'website' => $coinData['website'] ?? null,
                'telegram' => $coinData['telegram'] ?? null,
                'twitter' => $coinData['twitter'] ?? null,
                'discord' => $coinData['discord'] ?? null,
                'facebook' => $coinData['facebook'] ?? null,
                'reddit' => $coinData['reddit'] ?? null,
                'linktree' => $coinData['linktree'] ?? null,
                'presale_url' => $coinData['is_fair_launch'] == 1 ? ($coinData['fair_launch_url'] ?? null) : ($coinData['presale_url'] ?? null),
                'whitepaper' => $coinData['whitepaper'] ?? null,
            ]
        );

        // Always add to dexscreener_tokens table, even for presale tokens without a contract address

        // Create the dexscreener token record
        $dexToken = new \App\Models\DexscreenerToken();
        $dexToken->chain_id = $chainId;
        $dexToken->token_address = $tokenAddress; // Use the generated token address for presale tokens
        $dexToken->token_name = $coinData['name'];
        $dexToken->token_symbol = $coinData['symbol'];
        $dexToken->image_url = $coinData['logo']; // Use the uploaded logo
        $dexToken->is_verified = false; // Requires admin verification
        $dexToken->submitted_by_user_id = $user->id;

        // Set some metadata with social links
        $metadata = [
            'website' => $coinData['website'] ?? '',
            'telegram' => $coinData['telegram'] ?? '',
            'twitter' => $coinData['twitter'] ?? '',
            'discord' => $coinData['discord'] ?? '',
            'facebook' => $coinData['facebook'] ?? '',
            'reddit' => $coinData['reddit'] ?? '',
            'linktree' => $coinData['linktree'] ?? '',
            'presale_url' => $coinData['is_fair_launch'] == 1 ? ($coinData['fair_launch_url'] ?? '') : ($coinData['presale_url'] ?? ''),
            'description' => $coinData['description'] ?? '',
            'video_url' => $coinData['video_url'] ?? '',
            'whitepaper' => $coinData['whitepaper'] ?? '',
            'is_presale' => $coinData['is_presale'] ?? false,
            'presale_start_date' => $coinData['presale_start_date'] ?? '',
            'presale_end_date' => $coinData['presale_end_date'] ?? '',
            'softcap' => $coinData['softcap'] ?? '',
            'hardcap' => $coinData['hardcap'] ?? '',
            'blockchain_explorer_url' => $coinData['blockchain_explorer_url'] ?? '',
            'force_presale_display' => $coinData['is_presale'] == 1 ? true : false, // Force presale display regardless of contract address
            'force_fair_launch_display' => $coinData['is_fair_launch'] == 1 ? true : false // Force fair launch display regardless of contract address
        ];

        $dexToken->metadata = json_encode($metadata);

        try {
            $dexToken->save();
        } catch (\Exception $e) {
            // If there's an error saving (e.g., duplicate token), just continue
            // We've already saved to submit_coins so the admin can still review
        }

        // Clear the session data
        session()->forget('coin_data');

        // Store submitted token information for display on success page
        $submittedToken = [
            'name' => $coinData['name'],
            'symbol' => $coinData['symbol'],
            'logo' => $coinData['logo'],
            'blockchain' => $coinData['blockchain'],
            'chain_id' => $chainId,
            'token_address' => $tokenAddress
        ];

        session(['submitted_token' => $submittedToken]);

        // Check if there's a pending promotion to apply after submission
        $promoteAfterSubmit = session('promote_after_submit');
        if ($promoteAfterSubmit) {
            // Check if this was a pre-form promotion (before form fields were filled)
            $isPreForm = isset($promoteAfterSubmit['is_pre_form']) && $promoteAfterSubmit['is_pre_form'];

            // Log the promotion data for debugging
            \Illuminate\Support\Facades\Log::info('Processing promotion after submit', [
                'session_data' => $promoteAfterSubmit,
                'final_token_address' => $tokenAddress,
                'final_chain_id' => $chainId,
                'is_pre_form' => $isPreForm
            ]);

            // Check if the promotion has expired
            if (isset($promoteAfterSubmit['expires_at']) && $promoteAfterSubmit['expires_at'] < now()->timestamp) {
                // Clear the expired promotion data from session
                session()->forget('promote_after_submit');
                \Illuminate\Support\Facades\Log::warning('Promotion expired', ['expires_at' => $promoteAfterSubmit['expires_at']]);
            } else {
                // Get the user
                $user = auth()->user();

                // Verify the user has enough credits
                $days = (int) $promoteAfterSubmit['days']; // Explicitly cast to integer
                if ($user->promote_credits >= $days) {
                    try {
                        // Start a database transaction
                        DB::beginTransaction();

                        // Deduct credits now that we're sure the token is successfully submitted
                        $user->promote_credits -= $days;
                        $user->save();

                        // Create a new promotion record
                        $promotion = new \App\Models\TokenPromotion();
                        $promotion->user_id = $user->id;

                        // Always use the final chain ID and token address from submission
                        // This ensures we're using the correct values regardless of when the promotion was confirmed
                        $promotion->chain_id = $chainId; // Use the final chain ID from submission
                        $promotion->token_address = $tokenAddress; // Use the final token address from submission

                        $promotion->days = $days;
                        $promotion->start_date = now();
                        $promotion->end_date = now()->addDays($days);
                        $promotion->is_active = true;
                        $promotion->save();

                        // Commit the transaction
                        DB::commit();

                        // Log the promotion for debugging
                        \Illuminate\Support\Facades\Log::info('Token promotion created successfully', [
                            'user_id' => $user->id,
                            'token_address' => $tokenAddress,
                            'chain_id' => $chainId,
                            'days' => $days,
                            'promotion_id' => $promotion->id,
                            'credits_remaining' => $user->promote_credits
                        ]);
                    } catch (\Exception $e) {
                        // Rollback the transaction if something goes wrong
                        DB::rollBack();

                        // Log the error
                        \Illuminate\Support\Facades\Log::error('Failed to create token promotion', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                } else {
                    \Illuminate\Support\Facades\Log::warning('User does not have enough credits', [
                        'user_id' => $user->id,
                        'required_credits' => $days,
                        'available_credits' => $user->promote_credits
                    ]);
                }

                // Clear the promotion data from session
                session()->forget('promote_after_submit');
            }
        }

        return redirect()->route('user.submit.coin.success');
    }

    public function submitCoinSuccess()
    {
        $pageTitle = 'Submission Successful';
        $submittedToken = session('submitted_token');

        if (!$submittedToken) {
            return redirect()->route('user.home');
        }

        // Clear the session after displaying the success page
        session()->forget('submitted_token');

        return view('Template::user.submit_coin_success', compact('pageTitle', 'submittedToken'));
    }

    public function advertise()
    {
        $pageTitle = 'Advertise';
        return view('Template::user.advertise', compact('pageTitle'));
    }

    public function adBanners()
    {
        $pageTitle = 'Ad Banners';
        $adPositions = \App\Models\AdPosition::where('status', 1)->orderBy('id')->get();
        $userBanners = \App\Models\UserBanner::where('user_id', auth()->id())->with('adPosition')->get();
        return view('Template::user.ad_banners', compact('pageTitle', 'adPositions', 'userBanners'));
    }

    public function manageAds()
    {
        $pageTitle = 'Manage Ads';
        $emptyMessage = 'No ad banners found';
        $userBanners = \App\Models\UserBanner::where('user_id', auth()->id())
            ->with(['adPosition', 'impressions'])
            ->paginate(10);
        return view('Template::user.manage_ads', compact('pageTitle', 'userBanners', 'emptyMessage'));
    }

    public function adBannerUploadForm($id)
    {
        $adPosition = \App\Models\AdPosition::findOrFail($id);
        $pageTitle = 'Upload Banner for ' . $adPosition->name;

        // Check if user already has an active or pending banner for this position
        $activeAd = \App\Models\UserBanner::where('user_id', auth()->id())
            ->where('ad_position_id', $adPosition->id)
            ->where(function($query) {
                $query->where('status', 1) // Active
                    ->orWhere('status', 0); // Pending
            })
            ->first();

        if ($activeAd) {
            $notify[] = ['error', 'You already have an active or pending ad in this location. You cannot have multiple ads in the same location.'];
            return redirect()->route('user.ad.banners')->withNotify($notify);
        }

        // Check if user already has a banner for this position (for editing purposes)
        $existingBanner = \App\Models\UserBanner::where('user_id', auth()->id())
            ->where('ad_position_id', $adPosition->id)
            ->first();

        return view('Template::user.ad_banner_upload', compact('pageTitle', 'adPosition', 'existingBanner'));
    }

    public function adBannerUpload(Request $request, $id)
    {
        $adPosition = \App\Models\AdPosition::findOrFail($id);
        $user = auth()->user();

        // Check if user already has an active or pending banner for this position
        $activeAd = \App\Models\UserBanner::where('user_id', auth()->id())
            ->where('ad_position_id', $adPosition->id)
            ->where(function($query) {
                $query->where('status', 1) // Active
                    ->orWhere('status', 0); // Pending
            })
            ->first();

        if ($activeAd) {
            $notify[] = ['error', 'You already have an active or pending ad in this location. You cannot have multiple ads in the same location.'];
            return redirect()->route('user.ad.banners')->withNotify($notify);
        }

        // Parse dimensions from the ad position size
        $dimensionsValidation = $this->parseAdPositionDimensions($adPosition->size);

        $validationRules = [
            'image' => [
                'required',
                'image',
                'max:5120', // 5MB max file size
                new \App\Rules\FileTypeValidate(['jpg', 'jpeg', 'png', 'gif'])
            ],
            'redirect_url' => ['nullable', 'url'],
        ];

        // Add dimensions validation if we have specific dimensions to validate
        if ($dimensionsValidation) {
            $validationRules['image'][] = $dimensionsValidation;
        }

        $request->validate($validationRules);

        // Check if user already has a banner for this position
        $existingBanner = \App\Models\UserBanner::where('user_id', $user->id)
            ->where('ad_position_id', $adPosition->id)
            ->first();

        if ($existingBanner) {
            // Update existing banner
            $old = $existingBanner->image;
            try {
                $existingBanner->image = fileUploader($request->image, getFilePath('ads_images'), null, $old);
                $existingBanner->redirect_url = $request->redirect_url;
                $existingBanner->status = 0; // Set to pending
                $existingBanner->rejection_reason = null; // Clear any previous rejection reason
                $existingBanner->save();

                // Store banner info in session for success page
                session()->put('submitted_banner', [
                    'position_name' => $adPosition->name,
                    'position_id' => $adPosition->id
                ]);

                return redirect()->route('user.ad.banner.success');
            } catch (\Exception $exp) {
                $notify[] = ['error', 'Image could not be uploaded'];
                return back()->withNotify($notify);
            }
        } else {
            // Create new banner
            try {
                $banner = new \App\Models\UserBanner();
                $banner->user_id = $user->id;
                $banner->ad_position_id = $adPosition->id;
                $banner->image = fileUploader($request->image, getFilePath('ads_images'));
                $banner->redirect_url = $request->redirect_url;
                $banner->status = 0; // Set to pending
                $banner->start_date = now();
                $banner->save();

                // Store banner info in session for success page
                session()->put('submitted_banner', [
                    'position_name' => $adPosition->name,
                    'position_id' => $adPosition->id
                ]);

                return redirect()->route('user.ad.banner.success');
            } catch (\Exception $exp) {
                $notify[] = ['error', 'Image could not be uploaded'];
                return back()->withNotify($notify);
            }
        }
    }

    public function adBannerSuccess()
    {
        $pageTitle = 'Banner Submission Successful';
        $submittedBanner = session('submitted_banner');

        if (!$submittedBanner) {
            return redirect()->route('user.home');
        }

        // Clear the session after displaying the success page
        session()->forget('submitted_banner');

        return view('Template::user.ad_banner_success', compact('pageTitle', 'submittedBanner'));
    }

    public function pauseBanner($id)
    {
        $banner = \App\Models\UserBanner::where('id', $id)
            ->where('user_id', auth()->id())
            ->firstOrFail();

        // Toggle the banner status between active (1) and paused (3)
        if ($banner->status == 1) { // If active
            $banner->status = 3; // Set to paused
            $message = 'Banner has been paused successfully';
        } else if ($banner->status == 3) { // If paused
            $banner->status = 1; // Set to active
            $message = 'Banner has been activated successfully';
        } else {
            $message = 'Banner status cannot be changed at this time';
        }

        $banner->save();

        $notify[] = ['success', $message];
        return back()->withNotify($notify);
    }

    public function deleteBanner($id)
    {
        $banner = \App\Models\UserBanner::where('id', $id)
            ->where('user_id', auth()->id())
            ->firstOrFail();

        // Only allow deletion if the banner is paused or rejected
        if ($banner->status != 3 && $banner->status != 2) {
            $notify[] = ['error', 'You must pause the banner before deleting it'];
            return back()->withNotify($notify);
        }

        // Delete the banner image
        fileManager()->removeFile(getFilePath('ads_images') . '/' . $banner->image);

        // Delete the banner record
        $banner->delete();

        $notify[] = ['success', 'Banner has been deleted successfully'];
        return back()->withNotify($notify);
    }

    public function depositFunds()
    {
        $pageTitle = 'Deposit Funds';
        $user = auth()->user();

        // Allow only specific gateways
        $allowedGateways = [101, 104, 503, 506, 504]; // Paypal Express, 2Checkout, CoinPayments, Coinbase Commerce, CoinPayments Fiat
        $allowedAliases = ['paypal', 'twocheckout', 'coinpayments', 'coinbasecommerce', 'coinpaymentsfiat'];

        // Get available payment gateways
        $gatewayCurrency = \App\Models\GatewayCurrency::whereHas('method', function ($gate) use ($allowedGateways, $allowedAliases) {
            $gate->where('status', \App\Constants\Status::ENABLE)
                 ->where(function($query) use ($allowedGateways, $allowedAliases) {
                     $query->whereIn('code', $allowedGateways)
                           ->orWhereIn('alias', $allowedAliases);
                 });
        })->with('method')->orderby('method_code')->get();

        return view('Template::user.deposit_funds', compact('pageTitle', 'gatewayCurrency'));
    }

    public function checkActiveAd($id)
    {
        $adPosition = \App\Models\AdPosition::findOrFail($id);

        // Check if user already has an active ad in this position
        $activeAd = \App\Models\UserBanner::where('user_id', auth()->id())
            ->where('ad_position_id', $adPosition->id)
            ->where(function($query) {
                $query->where('status', 1) // Active
                    ->orWhere('status', 0); // Pending
            })
            ->first();

        if ($activeAd) {
            return response()->json([
                'success' => false,
                'message' => 'You already have an active or pending ad in this location. You cannot have multiple ads in the same location.'
            ]);
        }

        return response()->json([
            'success' => true
        ]);
    }

    /**
     * Parse dimensions from ad position size string and return Laravel validation rule
     *
     * @param string $sizeString The size string from ad position (e.g. "570x100 (Desktop), 350x100 (Tablet)")
     * @return string|null Laravel dimensions validation rule or null if no specific dimensions
     */
    private function parseAdPositionDimensions($sizeString)
    {
        // If the size contains "Responsive" or doesn't have specific dimensions, return null
        if (strpos($sizeString, 'Responsive') !== false || empty($sizeString)) {
            return null;
        }

        // Extract the first dimension (usually desktop) from the string
        // Format examples: "570x100 (Desktop)", "300x250 (All devices)"
        if (preg_match('/(\d+)x(\d+)/', $sizeString, $matches)) {
            $width = $matches[1];
            $height = $matches[2];

            // Return Laravel dimensions validation rule
            return "dimensions:width=$width,height=$height";
        }

        return null;
    }

    public function submitCoin(Request $request)
    {
        $step = $request->step ?? 1;

        switch ($step) {
            case 1:
                return $this->submitCoinStep1($request);
            case 2:
                return $this->submitCoinStep2($request);
            case 3:
                return $this->submitCoinStep3($request);
            default:
                return redirect()->route('user.submit.coin.form');
        }
    }
}
