<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('token_votes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('token_id')->comment('DexscreenerToken ID');
            $table->string('chain_id')->comment('Blockchain identifier');
            $table->string('token_address')->comment('Token contract address');
            $table->unsignedBigInteger('user_id')->nullable()->comment('User ID, null if guest vote');
            $table->string('ip_address', 40)->comment('IP address of the voter');
            $table->date('voted_at');
            $table->boolean('used_trend_vote')->default(false);
            $table->timestamps();
            
            // Unique constraint to prevent multiple votes from same user/IP for same token on same day
            $table->unique(['token_id', 'ip_address', 'voted_at', 'used_trend_vote']);
            
            // Add indexes for performance
            $table->index(['chain_id', 'token_address']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('token_votes');
    }
};
