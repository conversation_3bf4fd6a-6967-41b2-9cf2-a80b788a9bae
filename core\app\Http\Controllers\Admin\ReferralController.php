<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Referral;
use Illuminate\Http\Request;

class ReferralController extends Controller
{
    public function index()
    {
        $pageTitle = 'Referral Setting';
        $referrals = Referral::get();
        
        // Ensure at least one default referral level exists
        if ($referrals->isEmpty()) {
            $defaultReferral = new Referral();
            $defaultReferral->level = 1;
            $defaultReferral->percent = 10; // Default 10% commission
            $defaultReferral->save();
            
            // Refresh the collection
            $referrals = Referral::get();
            
            // Log the creation of default referral
            \Illuminate\Support\Facades\Log::info('Created default referral configuration');
        }
        
        $general = gs();
        return view('admin.referral.index', compact('pageTitle', 'referrals', 'general'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'min_withdrawal' => 'required|numeric|min:0'
        ]);

        // Update minimum withdrawal amount
        $general = gs();
        $general->referral_min_withdrawal = $request->min_withdrawal;
        $general->save();

        // If there are percent values, update the referral levels
        if (isset($request->percent) && is_array($request->percent)) {
            $request->validate([
                'percent*' => 'required|numeric'
            ]);

            Referral::truncate();

            for ($i = 0; $i < count($request->percent); $i++) {
                $referral = new Referral();
                $referral->level = $i + 1;
                $referral->percent = $request->percent[$i];
                $referral->save();
            }

            $notify[] = ['success', 'Referral commission setting updated successfully'];
        } else {
            $notify[] = ['success', 'Minimum withdrawal amount updated successfully'];
        }

        return back()->withNotify($notify);
    }
}
