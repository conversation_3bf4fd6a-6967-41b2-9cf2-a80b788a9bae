<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    public function profile()
    {
        $pageTitle = "Profile Setting";
        $user = auth()->user();
        return view('Template::user.profile_setting', compact('pageTitle', 'user'));
    }

    public function submitProfile(Request $request)
    {
        $request->validate([
            'username' => 'required|string|min:6',
        ]);

        $user = auth()->user();
        
        // Check if username changed and if it already exists
        if ($user->username != $request->username) {
            $usernameExists = \App\Models\User::where('username', $request->username)
                ->where('id', '!=', $user->id)
                ->exists();
                
            if ($usernameExists) {
                $notify[] = ['error', 'The username is already taken'];
                return back()->withNotify($notify);
            }
            
            // Check for invalid characters in username
            if (preg_match("/[^a-z0-9_]/", trim($request->username))) {
                $notify[] = ['info', 'Username can contain only small letters, numbers and underscore.'];
                $notify[] = ['error', 'No special character, space or capital letters in username.'];
                return back()->withNotify($notify)->withInput($request->all());
            }
            
            $user->username = $request->username;
        }

        $user->address = $request->address;
        $user->city = $request->city;
        $user->state = $request->state;
        $user->zip = $request->zip;

        $user->save();
        $notify[] = ['success', 'Profile updated successfully'];
        return back()->withNotify($notify);
    }

    public function changePassword()
    {
        $pageTitle = 'Change Password';
        return view('Template::user.password', compact('pageTitle'));
    }

    public function submitPassword(Request $request)
    {

        $passwordValidation = Password::min(6);
        if (gs('secure_password')) {
            $passwordValidation = $passwordValidation->mixedCase()->numbers()->symbols()->uncompromised();
        }

        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', $passwordValidation]
        ]);

        $user = auth()->user();
        if (Hash::check($request->current_password, $user->password)) {
            $password = Hash::make($request->password);
            $user->password = $password;
            $user->save();
            $notify[] = ['success', 'Password changed successfully'];
            return back()->withNotify($notify);
        } else {
            $notify[] = ['error', 'The password doesn\'t match!'];
            return back()->withNotify($notify);
        }
    }
}
