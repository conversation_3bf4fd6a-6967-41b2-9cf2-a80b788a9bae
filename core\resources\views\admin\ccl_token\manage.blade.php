@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10">
                <div class="card-body p-0">
                    <div class="table-responsive--md table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th>@lang('Section')</th>
                                    <th>@lang('Content')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>@lang('Token Information')</td>
                                    <td>
                                        <span>@lang('Token Name, Symbol, Supply, Blockchain')</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary editTokenInfo">
                                            <i class="la la-pencil"></i> @lang('Edit')
                                        </button>
                                    </td>
                                </tr>

                                <tr>
                                    <td>@lang('Token Utility')</td>
                                    <td>
                                        <span>@lang('Platform Governance, Fee Discounts, Premium Features, Rewards')</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary editTokenUtility">
                                            <i class="la la-pencil"></i> @lang('Edit')
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>@lang('Roadmap')</td>
                                    <td>
                                        <span>@lang('Project Roadmap Timeline')</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary editRoadmap">
                                            <i class="la la-pencil"></i> @lang('Edit')
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>@lang('Token Distribution')</td>
                                    <td>
                                        <span>@lang('Distribution Percentages for Chart')</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary editDistribution">
                                            <i class="la la-pencil"></i> @lang('Edit')
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>@lang('CCL Token Visibility')</td>
                                    <td>
                                        <span>@lang('Enable/Disable CCL Token links in user dashboard and menu')</span>
                                    </td>
                                    <td>
                                        <form action="{{ route('admin.manage.ccl.token.toggle') }}" method="POST">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-outline--{{ $cclToken->is_enabled ? 'danger' : 'success' }}">
                                                <i class="la {{ $cclToken->is_enabled ? 'la-toggle-on' : 'la-toggle-off' }}"></i> {{ $cclToken->is_enabled ? 'Disable' : 'Enable' }}
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>



    {{-- TOKEN INFO MODAL --}}
    <div id="tokenInfoModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('Update Token Information')</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="{{ route('admin.manage.ccl.token.info') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label>@lang('Token Name')</label>
                            <input type="text" class="form-control" name="token_name" value="{{ $cclToken->token_name }}" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Token Symbol')</label>
                            <input type="text" class="form-control" name="token_symbol" value="{{ $cclToken->token_symbol }}" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Total Supply')</label>
                            <input type="text" class="form-control" name="total_supply" value="{{ $cclToken->total_supply }}" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Blockchain')</label>
                            <input type="text" class="form-control" name="blockchain" value="{{ $cclToken->blockchain }}" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Buy Token URL')</label>
                            <input type="url" class="form-control" name="buy_token_url" value="{{ $cclToken->buy_token_url }}" placeholder="https://example.com/buy-token">
                            <small class="form-text text-muted">Enter the URL where users can buy the token. This will be used for the "Buy Token" buttons on the CCL Token page.</small>
                        </div>
                        <div class="form-group">
                            <label>@lang('Whitepaper URL')</label>
                            <input type="url" class="form-control" name="whitepaper_url" value="{{ $cclToken->whitepaper_url }}" placeholder="https://example.com/whitepaper.pdf">
                            <small class="form-text text-muted">Enter the URL to the whitepaper document. This will be displayed as a downloadable link on the Launchpad page.</small>
                        </div>
                        <div class="form-group">
                            <label>@lang('About Content')</label>
                            <textarea class="form-control" name="about_content" rows="8" placeholder="Enter content here. Use single line breaks for new lines within paragraphs. Use double line breaks to create new paragraphs.">{{ $cclToken->about_content }}</textarea>
                            <small class="form-text text-muted">
                                <ul class="mt-2 mb-0">
                                    <li>Single line break: Creates a new line within the same paragraph</li>
                                    <li>Double line break: Creates a new paragraph with proper spacing</li>
                                </ul>
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn--primary">@lang('Update')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- TOKEN UTILITY MODAL --}}
    <div id="tokenUtilityModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('Update Token Utility')</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="{{ route('admin.manage.ccl.token.utility') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="utility-items">
                            @forelse($cclToken->utility_items as $index => $item)
                                <div class="utility-item mb-3">
                                    <div class="form-group">
                                        <label>@lang('Title')</label>
                                        <input type="text" class="form-control" name="utility_title[]" value="{{ $item['title'] }}" required>
                                    </div>
                                    <div class="form-group">
                                        <label>@lang('Description')</label>
                                        <textarea class="form-control" name="utility_description[]" rows="3" required placeholder="Enter content here. Line breaks and spacing will be preserved.">{{ $item['description'] }}</textarea>
                                        <small class="form-text text-muted">Line breaks and spacing will be preserved when displayed.</small>
                                    </div>
                                    @if($index > 0)
                                        <button type="button" class="btn btn-sm btn--danger removeUtilityItem">
                                            <i class="la la-trash"></i> @lang('Remove')
                                        </button>
                                    @endif
                                </div>
                            @empty
                                <div class="utility-item mb-3">
                                    <div class="form-group">
                                        <label>@lang('Title')</label>
                                        <input type="text" class="form-control" name="utility_title[]" required>
                                    </div>
                                    <div class="form-group">
                                        <label>@lang('Description')</label>
                                        <textarea class="form-control" name="utility_description[]" rows="3" required placeholder="Enter content here. Line breaks and spacing will be preserved."></textarea>
                                        <small class="form-text text-muted">Line breaks and spacing will be preserved when displayed.</small>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                        <button type="button" class="btn btn-sm btn--success addUtilityItem">
                            <i class="la la-plus"></i> @lang('Add More')
                        </button>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn--primary">@lang('Update')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- ROADMAP MODAL --}}
    <div id="roadmapModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('Update Token Roadmap')</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="{{ route('admin.manage.ccl.token.roadmap') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="roadmap-items">
                            @forelse($cclToken->roadmap_items as $index => $item)
                                <div class="roadmap-item mb-3">
                                    <div class="form-group">
                                        <label>@lang('Title')</label>
                                        <input type="text" class="form-control" name="roadmap_title[]" value="{{ $item['title'] }}" required>
                                    </div>
                                    <div class="form-group">
                                        <label>@lang('Description')</label>
                                        <textarea class="form-control" name="roadmap_description[]" rows="3" required placeholder="Enter content here. Line breaks and spacing will be preserved.">{{ $item['description'] }}</textarea>
                                        <small class="form-text text-muted">Line breaks and spacing will be preserved when displayed.</small>
                                    </div>
                                    @if($index > 0)
                                        <button type="button" class="btn btn-sm btn--danger removeRoadmapItem">
                                            <i class="la la-trash"></i> @lang('Remove')
                                        </button>
                                    @endif
                                </div>
                            @empty
                                <div class="roadmap-item mb-3">
                                    <div class="form-group">
                                        <label>@lang('Title')</label>
                                        <input type="text" class="form-control" name="roadmap_title[]" required>
                                    </div>
                                    <div class="form-group">
                                        <label>@lang('Description')</label>
                                        <textarea class="form-control" name="roadmap_description[]" rows="3" required placeholder="Enter content here. Line breaks and spacing will be preserved."></textarea>
                                        <small class="form-text text-muted">Line breaks and spacing will be preserved when displayed.</small>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                        <button type="button" class="btn btn-sm btn--success addRoadmapItem">
                            <i class="la la-plus"></i> @lang('Add More')
                        </button>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn--primary">@lang('Update')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- DISTRIBUTION MODAL --}}
    <div id="distributionModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('Update Token Distribution')</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="{{ route('admin.manage.ccl.token.distribution') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="distribution-items">
                            @forelse($cclToken->distribution_items as $index => $item)
                                <div class="distribution-item mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>@lang('Label')</label>
                                                <input type="text" class="form-control" name="distribution_label[]" value="{{ $item['label'] }}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>@lang('Percentage')</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" name="distribution_percentage[]" value="{{ $item['percentage'] }}" required>
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label>@lang('Color')</label>
                                                <input type="color" class="form-control" name="distribution_color[]" value="{{ $item['color'] }}" required>
                                            </div>
                                        </div>
                                    </div>
                                    @if($index > 0)
                                        <button type="button" class="btn btn-sm btn--danger removeDistributionItem">
                                            <i class="la la-trash"></i> @lang('Remove')
                                        </button>
                                    @endif
                                </div>
                            @empty
                                <div class="distribution-item mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>@lang('Label')</label>
                                                <input type="text" class="form-control" name="distribution_label[]" required>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>@lang('Percentage')</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" name="distribution_percentage[]" required>
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label>@lang('Color')</label>
                                                <input type="color" class="form-control" name="distribution_color[]" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                        <button type="button" class="btn btn-sm btn--success addDistributionItem">
                            <i class="la la-plus"></i> @lang('Add More')
                        </button>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn--primary">@lang('Update')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('ccl.token') }}" target="_blank" class="btn btn-sm btn-outline--primary">
        <i class="las la-eye"></i> @lang('View CCL Token Page')
    </a>
@endpush

@push('style')
<style>
    .button-group {
        display: flex;
        gap: 5px;
    }

    @media (max-width: 767px) {
        .button-group {
            flex-direction: column;
        }
    }
</style>
@endpush

@push('script')
<script>
    (function($) {
        "use strict";

        $('.editTokenInfo').on('click', function() {
            var modal = $('#tokenInfoModal');
            modal.modal('show');
        });

        $('.editTokenUtility').on('click', function() {
            var modal = $('#tokenUtilityModal');
            modal.modal('show');
        });

        $('.editRoadmap').on('click', function() {
            var modal = $('#roadmapModal');
            modal.modal('show');
        });

        $('.editDistribution').on('click', function() {
            var modal = $('#distributionModal');
            modal.modal('show');
        });

        // Add more utility items
        $('.addUtilityItem').on('click', function() {
            var html = `
                <div class="utility-item mb-3">
                    <div class="form-group">
                        <label>@lang('Title')</label>
                        <input type="text" class="form-control" name="utility_title[]" required>
                    </div>
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="utility_description[]" rows="3" required placeholder="Enter content here. Line breaks and spacing will be preserved."></textarea>
                        <small class="form-text text-muted">Line breaks and spacing will be preserved when displayed.</small>
                    </div>
                    <button type="button" class="btn btn-sm btn--danger removeUtilityItem">
                        <i class="la la-trash"></i> @lang('Remove')
                    </button>
                </div>
            `;
            $('.utility-items').append(html);
        });

        // Remove utility item
        $(document).on('click', '.removeUtilityItem', function() {
            $(this).closest('.utility-item').remove();
        });

        // Add more roadmap items
        $('.addRoadmapItem').on('click', function() {
            var html = `
                <div class="roadmap-item mb-3">
                    <div class="form-group">
                        <label>@lang('Title')</label>
                        <input type="text" class="form-control" name="roadmap_title[]" required>
                    </div>
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="roadmap_description[]" rows="3" required placeholder="Enter content here. Line breaks and spacing will be preserved."></textarea>
                        <small class="form-text text-muted">Line breaks and spacing will be preserved when displayed.</small>
                    </div>
                    <button type="button" class="btn btn-sm btn--danger removeRoadmapItem">
                        <i class="la la-trash"></i> @lang('Remove')
                    </button>
                </div>
            `;
            $('.roadmap-items').append(html);
        });

        // Remove roadmap item
        $(document).on('click', '.removeRoadmapItem', function() {
            $(this).closest('.roadmap-item').remove();
        });

        // Add more distribution items
        $('.addDistributionItem').on('click', function() {
            var html = `
                <div class="distribution-item mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Label')</label>
                                <input type="text" class="form-control" name="distribution_label[]" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@lang('Percentage')</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="distribution_percentage[]" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>@lang('Color')</label>
                                <input type="color" class="form-control" name="distribution_color[]" required>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn--danger removeDistributionItem">
                        <i class="la la-trash"></i> @lang('Remove')
                    </button>
                </div>
            `;
            $('.distribution-items').append(html);
        });

        // Remove distribution item
        $(document).on('click', '.removeDistributionItem', function() {
            $(this).closest('.distribution-item').remove();
        });

    })(jQuery);
</script>
@endpush
