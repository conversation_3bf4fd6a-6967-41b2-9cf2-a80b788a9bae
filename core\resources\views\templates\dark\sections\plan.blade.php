@php
    $content = getContent('plan.content', true);
@endphp
<section class="plan py-100 section-bg">
    <div class="container">
        <div class="row">
            <div class="section-heading">
                <h3 class="section-heading__title">{{ __(@$content->data_values->heading) }}</h3>
                <p class="section-heading__desc">{{ __(@$content->data_values->description) }}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="alert alert-info">
                    <h4 class="alert-heading">@lang('Mining Plans')</h4>
                    <hr>
                    <p>@lang('Mining plans functionality is currently not available.')</p>
                </div>
            </div>
        </div>
    </div>
</section>
