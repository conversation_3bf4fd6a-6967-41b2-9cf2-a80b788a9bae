/* Copy Animation */

.copyInput {
  display: inline-block;
  line-height: 50px;
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  -webkit-transition: all .3s;
  -o-transition: all .3s;
  transition: all .3s;
}

.copied::after {
  position: absolute;
  top: 8px;
  right: 12%;
  width: 100px;
  display: block;
  content: "COPIED";
  font-size: 1em;
  padding: 5px 5px;
  color: #fff;
  background-color: #FF7000;
  border-radius: 3px;
  opacity: 0;
  will-change: opacity, transform;
  animation: showcopied 1.5s ease;
}

@keyframes showcopied {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }

  50% {
    opacity: 0.7;
    transform: translateX(40%);
  }

  70% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
  }
}

.cookies-card {
  width: 520px;
  padding: 30px;
  color: #1E2337;
  position: fixed;
  bottom: 15px;
  left: 15px;
  z-index: 999999;
  transition: all .5s;
  background: #d1d1d1;
  border-radius: 5px;
}

.cookies-card.hide {
  bottom: -500px !important;
}

.radius--10px {
  border-radius: 10px;
}

.cookies-card__icon {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  background-color: #6e6f70;
  color: #fff;
  font-size: 32px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.cookies-card__content {
  margin-bottom: 0;
}

.cookies-btn {
  color: #363636;
  text-decoration: none;
  padding: 10px 35px;
  margin: 3px 5px;
  display: inline-block;
  border-radius: 999px;
}

.cookies-btn:hover {
  color: #363636;
}

@media (max-width: 767px) {
  .cookies-card {
    width: 100%;
    left: 0;
    bottom: 0;
    font-size: 14px;
    padding: 15px;
  }
}

.hover-input-popup {
  position: relative;
}

.input-popup {
  display: none;
}

.hover-input-popup .input-popup {
  display: block;
  position: absolute;
  bottom: 130%;
  left: 50%;
  width: 280px;
  background-color: #1a1a1a;
  color: #fff;
  padding: 20px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.input-popup::after {
  position: absolute;
  content: '';
  bottom: -19px;
  left: 50%;
  margin-left: -5px;
  border-width: 10px 10px 10px 10px;
  border-style: solid;
  border-color: transparent transparent #1a1a1a transparent;
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.input-popup p {
  padding-left: 20px;
  position: relative;
}

.input-popup p::before {
  position: absolute;
  content: '';
  font-family: 'Line Awesome Free';
  font-weight: 900;
  left: 0;
  top: 4px;
  line-height: 1;
  font-size: 18px;
}

.input-popup p.error {
  text-decoration: line-through;
}

.input-popup p.error::before {
  content: "\f057";
  color: #ea5455;
}

.input-popup p.success::before {
  content: "\f058";
  color: #28c76f;
}

.show-filter {
  display: none;
}

@media(max-width:767px) {
  .responsive-filter-card {
    display: none;
    transition: none;
  }

  .show-filter {
    display: block;
    position: absolute;
    top: -44px;
    right: 15px;
  }
}

.close-button {
  border-radius: 3px;
  line-height: 1em;
  padding: 10px;
}

.maintenance-page {
  display: grid;
  place-content: center;
  width: 100vw;
  height: 100vh;
}

.maintenance-icon {
  width: 60px;
  height: 60px;
  display: grid;
  place-items: center;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #fff;
  font-size: 26px;
  color: #E73D3E;
}

/* ////////////////// select 2 css //////////////// */
.select2-dropdown {
  background-color: #2C2F3E !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.select2-dropdown {
  border: 1px solid #aaa;
  border-radius: 4px;
  box-sizing: border-box;
  display: block;
  position: absolute;
  left: -100000px;
  width: 100%;
  z-index: 1051;
}

.select2-search--dropdown {
  padding: 10px 10px !important;
  border-color: #ced4da !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: rgb(255 255 255 / 3%) !important;
  padding: 10px 20px;
  background-color: #2a313b !important;
  color: #fff;
  border-radius: 6px;
}

.select2-results__option.select2-results__option--selected,
.select2-results__option--selectable,
.select2-container--default .select2-results__option--disabled {
  padding: 12px 14px !important;
  border-bottom: 1px solid transparent;
}

.select2-results__option.select2-results__message {
  text-align: center !important;
  padding: 12px 14px !important;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar {
  width: 8px;
  border-radius: 5px;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb {
  background: #ddd;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb:hover {
  background: #ddd;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  display: none;
}

.select2 .selection {
  width: 100%;
}

.select2-container--default .select2-selection--single {
  background-color: rgb(255 255 255 / 3%) !important;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.03) !important;
  border-color: rgba(255, 255, 255, 0.03) !important;
  height: 50px !important;
  padding: .75rem .75rem !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #fff !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow:after {
  position: absolute;
  right: 10px;
  top: 0;
  content: "\f107";
  font-family: "Line Awesome Free";
  font-weight: 900;
  transition: .3s;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 12px !important;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow:after {
  transform: rotate(-180deg);
}

.select2-results__option:last-child {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.select2-results__option:first-child {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.select2-results__option.select2-results__option--selected,
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: #BE8400 !important;
  color: #fff !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field:focus {
  border-color: #BE8400 !important;
  box-shadow: 0 0 25px rgba(190, 132, 0, 0.2) !important;
  outline: 0 !important;
}

.select2-dropdown .country-flag {
  width: 25px;
  height: 25px;
  border-radius: 8px;
}

.select2-dropdown .gateway-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 0px !important;
}

.select2-dropdown .gateway-subtitle {
  font-size: 12px;
  margin-bottom: 0px !important;
}

.select2-container--open .select2-selection.select2-selection--single,
.select2-container--open .select2-selection.select2-selection--multiple {
  border-color: #BE8400 !important;
  border-radius: .375rem !important;
}

.gateway-card {
  padding: 15px;
}

.payment-card-title {
  padding: 13px 25px;
  text-align: center;
  background-color: var(--main);
  border-radius: 5px;
  border: 0;
  margin-bottom: 0px;
  color: #fff;
}

.payment-system-list {
  --thumb-width: 130px;
  --thumb-height: 60px;
  --radio-size: 12px;
  --border-color: #cccccf59;
  --hover-border-color: var(--main);
  background-color: transparent;
  border-radius: 5px;
  height: 100%;
}

.payment-system-list.is-scrollable {
  max-height: min(388px, 70vh);
  overflow-x: auto;
  padding-block: 4px;
}

.payment-system-list.is-scrollable::-webkit-scrollbar {
  width: 5px;
}

.payment-system-list.is-scrollable::-webkit-scrollbar {
  width: 5px;
}

.payment-system-list.is-scrollable::-webkit-scrollbar-thumb {
  background-color: #BE8400;
  border-radius: 10px;
}

.payment-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 15px 18px;
  transition: all 0.3s;
  border-left: 3px solid transparent;
  cursor: pointer;
}

.payment-item:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.payment-item:first-child {
  border-radius: 5px 5px 0 0;
}

.payment-item:has(.payment-item__radio:checked) {
  border-left: 3px solid #BE8400;
  border-radius: 0px;
}

.payment-item__check {
  border: 2px solid #BE8400 !important;
}

.payment-item:has(.payment-item__radio:checked) .payment-item__check {
  border: 3px solid #BE8400;
}

.payment-item__info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  max-width: calc(100% - var(--thumb-width))
}

.payment-item__check {
  width: var(--radio-size);
  height: var(--radio-size);
  border: 2px solid #BE8400 !important;
  display: inline-block;
  border-radius: 100%;
}

.payment-item__name {
  padding-left: 10px;
  width: calc(100% - var(--radio-size));
  transition: all 0.3s;
}

.payment-item__thumb {
  width: var(--thumb-width);
  height: var(--thumb-height);
  text-align: right;
  padding-left: 10px;

  &:has(.text) {
    width: fit-content;
  }
}

.payment-item__thumb img {
  max-width: var(--thumb-width);
  max-height: var(--thumb-height);
  object-fit: cover;
}

.deposit-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 12px;
}

.deposit-info__title {
  max-width: 50%;
  margin-bottom: 0px;
  text-align: left;
}

.deposit-info__input {
  max-width: 50%;
  text-align: right;
  width: 100%;
}

.deposit-info__input-select {
  border: 1px solid var(--border-color);
  width: 100%;
  border-radius: 5px;
  padding-block: 6px;
}

.deposit-info__input-group {
  border: 1px solid var(--border-color);
  border-radius: 5px;

  .deposit-info__input-group-text {
    align-self: center;
    padding-left: 5px;
  }
}

.deposit-info__input-group .form--control {
  padding: 5px;
  border: 0;
  height: 35px;
  text-align: right;
}

.deposit-info__input-group .form--control:focus {
  box-shadow: unset;
}

.info-text .text,
.deposit-info__input .text {
  font-size: 14px;
}

.deposit-info__title .text.has-icon {
  display: flex;
  align-items: center;
  gap: 5px
}

.total-amount {
  border-top: 1px solid var(--border-color);
}

.total-amount .deposit-info__title {
  font-weight: 600;
}

.payment-item__btn {
  border: 0;
  border-bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 13px 15px;
  font-weight: 500;
}

.payment-item:hover+.payment-item__btn {
  border-top-color: #fff;
}

button .spinner-border {
  --bs-spinner-width: 1.5rem;
  --bs-spinner-height: 1.5rem;
}

.adjust-height .payment-system-list.is-scrollable {
  max-height: 461px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.input-group-text {
  border-radius: 3px;
}

/* Breadcrumb styling without background image */
.breadcumb {
  background-color: #171f2a;
  padding: 40px 0;
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.breadcumb__title {
  color: #fff;
  font-size: 32px;
  text-align: center;
  margin: 0;
}

/* Page Content Styles */
.page-content {
    background-color: var(--section-bg);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.page-content__title {
    color: var(--base-color);
    margin-bottom: 20px;
    font-weight: 600;
}

.page-content__body {
    color: var(--body-color);
    line-height: 1.7;
}

.page-content__body p {
    margin-bottom: 15px;
}

.page-content__body img {
    max-width: 100%;
    height: auto;
    margin: 15px 0;
    border-radius: 5px;
}

.page-content__body ul,
.page-content__body ol {
    margin-left: 20px;
    margin-bottom: 15px;
}

.page-content__body h1,
.page-content__body h2,
.page-content__body h3,
.page-content__body h4,
.page-content__body h5,
.page-content__body h6 {
    margin-bottom: 15px;
    margin-top: 25px;
    color: var(--base-color);
}

.page-content__body a {
    color: var(--base-color);
    text-decoration: underline;
}

/* Additional Blog Item Content Styles for Page Content */
.blog-item__content {
    padding: 30px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    margin-bottom: 30px;
}

.blog-item__content img {
    max-width: 100%;
    height: auto;
    margin: 15px 0;
    border-radius: 5px;
}

/* Article date button styling */
.blog-item__date {
    background-color: var(--main);
    color: #fff;
    padding: 6px 20px;
    position: absolute;
    right: 0;
    bottom: 0;
    border-radius: 8px 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.blog-item__date-icon {
    font-size: 13px;
    margin-right: 5px;
    display: inline-flex;
    align-items: center;
}

/* Responsive styles for blog-item (single article card) */
@media (max-width: 1199px) {
    .blog-item {
        width: 100%;
    }

    .blog-item__content {
        padding: 25px;
    }

    .blog-item__date {
        padding: 5px 15px;
        font-size: 16px;
    }
}

@media (max-width: 991px) {
    .blog-item__content {
        padding: 20px;
    }

    .blog-item__thumb {
        max-height: 400px;
    }

    .blog-item__date {
        padding: 5px 12px;
        font-size: 15px;
    }
}

@media (max-width: 767px) {
    .blog-item__content {
        padding: 15px;
    }

    .blog-item__thumb {
        max-height: 350px;
    }

    .blog-item__date {
        padding: 4px 10px;
        font-size: 14px;
        border-radius: 6px 0;
    }

    .blog-item__date-icon {
        font-size: 12px;
        margin-right: 4px;
    }
}

@media (max-width: 575px) {
    .blog-item__content {
        padding: 15px 12px;
    }

    .blog-item__thumb {
        max-height: 300px;
    }

    .blog-item__date {
        padding: 3px 8px;
        font-size: 13px;
        border-radius: 5px 0;
    }

    .blog-item__date-icon {
        font-size: 11px;
        margin-right: 3px;
    }
}

/* Extra small devices */
@media (max-width: 400px) {
    .blog-item__date {
        padding: 3px 6px;
        font-size: 12px;
    }

    .blog-item__date-icon {
        font-size: 10px;
        margin-right: 2px;
    }
}

/* Fix for very small devices */
@media (max-width: 320px) {
    .blog-item__date {
        padding: 2px 5px;
        font-size: 11px;
        border-radius: 4px 0;
    }
}

/* Tablet-specific adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
    .blog-item {
        display: flex;
        flex-direction: column;
    }

    .blog-item__content {
        padding: 20px;
    }
}

.blog-item__content h1,
.blog-item__content h2,
.blog-item__content h3,
.blog-item__content h4,
.blog-item__content h5,
.blog-item__content h6 {
    margin-bottom: 15px;
    margin-top: 20px;
    color: #fff;
}

.blog-item__content p {
    margin-bottom: 15px;
    line-height: 1.7;
}

.blog-item__content ul,
.blog-item__content ol {
    margin-left: 20px;
    margin-bottom: 15px;
}

.blog-item__content a {
    color: var(--main);
    text-decoration: underline;
}

.blog-item__content a:hover {
    color: var(--main);
    text-decoration: none;
}

.blog-item__content table {
    width: 100%;
    margin-bottom: 15px;
    border-collapse: collapse;
}

.blog-item__content table,
.blog-item__content th,
.blog-item__content td {
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px;
}

.blog-item__content th {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    text-align: left;
}

.select2-container--default .select2-results__option {
  background-color: #2C2F3E !important;
  color: #fff !important;
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: #31D7A9 !important;
  color: #fff !important;
}

/* Direct fix for dropdown highlights */
select option:checked,
select option:hover,
select option:focus,
select option:active {
    background-color: #BE8400 !important;
    color: #fff !important;
}

select {
    background-color: #2C2F3E !important;
    color: #fff !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Blog sidebar responsive styles */
.blog-sidebar {
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
}

.blog-sidebar__title {
    margin-bottom: 20px;
    color: #fff;
    font-size: 18px;
    position: relative;
    padding-bottom: 10px;
}

.blog-sidebar__title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 2px;
    background-color: var(--main);
}

.latest-blog {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.latest-blog:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.latest-blog__thumb {
    flex: 0 0 80px;
    max-width: 80px;
    margin-right: 15px;
    border-radius: 5px;
    overflow: hidden;
}

.latest-blog__thumb img {
    width: 100%;
    height: auto;
    border-radius: 5px;
}

.latest-blog__content {
    flex: 1;
}

.latest-blog__title {
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 1.4;
}

.latest-blog__title a {
    color: #fff;
    transition: all 0.3s;
}

.latest-blog__title a:hover {
    color: var(--main);
}

.latest-blog__date {
    font-size: 12px;
    color: #aaa;
}

/* Presale headline text color */
.custom--card .card-header .card-title {
    color: #BE8400;
}

/* Responsive styles for blog sidebar */
@media (max-width: 1199px) {
    .blog-sidebar {
        padding: 15px;
    }

    .latest-blog__thumb {
        flex: 0 0 70px;
        max-width: 70px;
        margin-right: 12px;
    }
}

@media (max-width: 991px) {
    .blog-sidebar {
        margin-top: 30px;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .blog-sidebar {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }

    .blog-sidebar__title {
        flex: 0 0 100%;
    }

    .latest-blog {
        flex: 0 0 calc(50% - 8px);
        max-width: calc(50% - 8px);
        margin-bottom: 10px;
        padding-bottom: 10px;
    }
}

@media (max-width: 767px) {
    .blog-sidebar {
        padding: 15px;
    }

    .latest-blog__thumb {
        flex: 0 0 60px;
        max-width: 60px;
        margin-right: 10px;
    }

    .latest-blog__title {
        font-size: 13px;
    }
}

/* Firefox specific */
@-moz-document url-prefix() {
    select option:checked,
    select option:hover {
        background-color: #BE8400 !important;
        color: #fff !important;
        box-shadow: 0 0 10px 100px #BE8400 inset !important;
    }
}

/* Webkit specific */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    select option:checked,
    select option:hover {
        background-color: #BE8400 !important;
        color: #fff !important;
    }
}
