@extends($activeTemplate . 'layouts.app')

@section('panel')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card bg--base-two">
                <div class="card-header bg-danger text-white">
                    <h5 class="m-0">{{ $pageTitle ?? 'Token Not Found' }}</h5>
                </div>
                <div class="card-body text-center text--white">
                    <div class="mb-4">
                        <i class="las la-exclamation-triangle fa-4x text-warning"></i>
                    </div>

                    <h3 class="mb-3 text-white">Token Not Found</h3>
                    <p class="mb-4">We couldn't find the token you were looking for with the following details:</p>

                    <div class="alert alert-info bg--dark text--white border-info">
                        <p><strong>Chain ID:</strong> {{ $chainId }}</p>
                        <p class="mb-0"><strong>Token Address:</strong> {{ $tokenAddress }}</p>
                    </div>

                    <div class="mt-4">
                        <p>Please check the token address and chain ID and try again.</p>
                        <p>If you believe this is an error, please contact support or try searching for the token.</p>
                    </div>

                    <div class="mt-4">
                        <a href="{{ url('/') }}" class="btn btn-primary mr-2">
                            <i class="las la-home"></i> Return to Home
                        </a>
                        <a href="{{ url('/') }}" class="btn btn-info">
                            <i class="las la-search"></i> Browse Tokens
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
    body {
        background-color: #0E1621;
        color: #B9BABB;
    }

    .card {
        background-color: #17212B;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .card-header {
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .text--white {
        color: #B9BABB !important;
    }

    .bg--dark {
        background-color: #1C2631 !important;
    }

    .border-info {
        border-color: rgba(23, 162, 184, 0.5) !important;
    }
</style>
@endpush