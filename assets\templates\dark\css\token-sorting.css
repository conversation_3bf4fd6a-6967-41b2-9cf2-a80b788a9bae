/**
 * Styles for token table sorting functionality
 */

/* Sortable header styling */
th.sortable {
    cursor: pointer;
    position: relative;
    user-select: none;
}

th.sortable:hover {
    background-color: rgba(190, 132, 0, 0.1);
}

/* Sort indicator styling */
.sort-indicator {
    display: inline-block;
    margin-left: 5px;
    font-size: 12px;
}

/* Active sort styling */
th.sorting-asc, th.sorting-desc {
    background-color: rgba(190, 132, 0, 0.2);
}

th.sorting-asc .sort-indicator {
    color: #BE8400;
}

th.sorting-desc .sort-indicator {
    color: #BE8400;
}
