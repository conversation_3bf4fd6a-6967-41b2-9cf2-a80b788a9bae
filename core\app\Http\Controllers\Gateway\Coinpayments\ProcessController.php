<?php

namespace App\Http\Controllers\Gateway\Coinpayments;

use App\Constants\Status;
use App\Models\Deposit;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Gateway\Coinpayments\CoinPaymentHosted;
use App\Http\Controllers\Gateway\PaymentController;
use Illuminate\Http\Request;

class ProcessController extends Controller
{
    /*
     * CoinPaymentHosted Gateway
     */

    public static function process($deposit)
    {

        $coinPayAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);

        if ($deposit->btc_amount == 0 || $deposit->btc_wallet == "") {
            try {
                $cps = new CoinPaymentHosted();
            } catch (\Exception $e) {
                $send['error'] = true;
                $send['message'] = $e->getMessage();
                return json_encode($send);
            }

            $cps->Setup($coinPayAcc->private_key, $coinPayAcc->public_key);
            $callbackUrl = route('ipn.'.$deposit->gateway->alias);

            // Log the callback URL for debugging
            \Illuminate\Support\Facades\Log::channel('daily')->info('Coinpayments callback URL', [
                'url' => $callbackUrl,
                'trx' => $deposit->trx
            ]);

            $req = array(
                'amount' => $deposit->final_amount,
                'currency1' => 'USD',
                'currency2' => $deposit->method_currency,
                'custom' => $deposit->trx,
                'buyer_email' => auth()->user()->email,
                'ipn_url' => $callbackUrl,
                'ipn_mode' => 'httppost', // Ensure IPN is sent as HTTP POST
            );

            $result = $cps->CreateTransaction($req);
            if ($result['error'] == 'ok') {
                $bcoin = sprintf('%.08f', $result['result']['amount']);
                $sendadd = $result['result']['address'];
                $deposit['btc_amount'] = $bcoin;
                $deposit['btc_wallet'] = $sendadd;
                $deposit->update();
            } else {
                $send['error'] = true;
                $send['message'] = $result['error'];
            }
        }

        $send['amount'] = $deposit->btc_amount;
        $send['sendto'] = $deposit->btc_wallet;
        $send['img'] = cryptoQR($deposit->btc_wallet);
        $send['currency'] = "$deposit->method_currency";
        $send['view'] = 'user.payment.crypto';
        return json_encode($send);
    }

    public function ipn(Request $request)
    {
        // Log the IPN request for debugging
        \Illuminate\Support\Facades\Log::channel('daily')->info('Coinpayments IPN received', [
            'request' => $request->all()
        ]);

        $track = $request->custom;
        $status = $request->status;
        $amount2 = floatval($request->amount2);
        $deposit = Deposit::where('trx', $track)->orderBy('id', 'DESC')->first();

        if (!$deposit) {
            \Illuminate\Support\Facades\Log::channel('daily')->error('Coinpayments IPN: Deposit not found', [
                'track' => $track
            ]);
            return response('Deposit not found', 404);
        }

        \Illuminate\Support\Facades\Log::channel('daily')->info('Coinpayments IPN: Deposit found', [
            'deposit_id' => $deposit->id,
            'status' => $deposit->status,
            'method_currency' => $deposit->method_currency,
            'btc_amount' => $deposit->btc_amount,
            'request_status' => $status,
            'request_amount2' => $amount2,
            'request_currency2' => $request->currency2
        ]);

        // Status 100 = complete, 2 = pending
        if ($status >= 100 || $status == 2) {
            $coinPayAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);

            // Check if merchant ID matches and deposit is in initiated state
            $merchantCheck = ($coinPayAcc->merchant_id == $request->merchant);
            $currencyCheck = ($deposit->method_currency == $request->currency2);
            $amountCheck = ($deposit->btc_amount <= $amount2);
            $statusCheck = ($deposit->status == Status::PAYMENT_INITIATE);

            \Illuminate\Support\Facades\Log::channel('daily')->info('Coinpayments IPN: Validation checks', [
                'merchant_check' => $merchantCheck,
                'currency_check' => $currencyCheck,
                'amount_check' => $amountCheck,
                'status_check' => $statusCheck
            ]);

            if ($currencyCheck && $amountCheck && $merchantCheck && $statusCheck) {
                \Illuminate\Support\Facades\Log::channel('daily')->info('Coinpayments IPN: Updating payment status', [
                    'deposit_id' => $deposit->id
                ]);

                PaymentController::userDataUpdate($deposit);
                return response('Payment updated successfully', 200);
            }
        }

        return response('IPN received but no action taken', 200);
    }
}
