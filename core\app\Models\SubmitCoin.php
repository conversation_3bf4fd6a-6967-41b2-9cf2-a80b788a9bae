<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubmitCoin extends Model
{
    use HasFactory;

    protected $table = 'submit_coins';

    protected $fillable = [
        'user_id',
        'blockchain',
        'blockchain_explorer_url',
        'contract_address',
        'name',
        'symbol',
        'description',
        'video_url',
        'logo',
        'is_presale',
        'is_fair_launch',
        'presale_start_date',
        'presale_end_date',
        'presale_url',
        'softcap',
        'hardcap',
        'status',
        'website',
        'telegram',
        'twitter',
        'discord',
        'facebook',
        'reddit',
        'linktree',
        'whitepaper',
    ];

    protected $casts = [
        'is_presale' => 'boolean',
        'is_fair_launch' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}