<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\AdPosition;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create initial ad positions
        $adPositions = [
            [
                'name' => 'Header Left Ad',
                'key' => 'header_left',
                'size' => '570x100 (Desktop), 350x100 (Tablet), 160x100 (Mobile)',
                'description' => 'Left side ad in the header section',
                'status' => 1,
            ],
            [
                'name' => 'Header Right Ad',
                'key' => 'header_right',
                'size' => '570x100 (Desktop), 350x100 (Tablet), 160x100 (Mobile)',
                'description' => 'Right side ad in the header section',
                'status' => 1,
            ],
            [
                'name' => 'Footer Full Ad',
                'key' => 'footer_full',
                'size' => '970x90 (Desktop), 728x90 (Tablet), 320x100 (Mobile)',
                'description' => 'Full width ad in the footer section',
                'status' => 1,
            ],
            [
                'name' => 'Token Details Ad',
                'key' => 'token_details',
                'size' => '728x90 (Desktop), 468x60 (Tablet), 320x100 (Mobile)',
                'description' => 'Ad between token data and buttons on token details page',
                'status' => 1,
            ],
            [
                'name' => 'Blog Sidebar Ad',
                'key' => 'blog_sidebar',
                'size' => '300x250 (All devices)',
                'description' => 'Ad in the blog sidebar between Latest Articles',
                'status' => 1,
            ],
        ];

        foreach ($adPositions as $position) {
            AdPosition::create($position);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the seeded ad positions
        AdPosition::whereIn('key', [
            'header_left',
            'header_right',
            'footer_full',
            'token_details',
            'blog_sidebar',
        ])->delete();
    }
};
