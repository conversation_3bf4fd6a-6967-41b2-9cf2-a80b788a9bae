<?php

namespace App\Http\Controllers;

use App\Models\DexscreenerToken;
use Illuminate\Http\Request;
use App\Services\DexscreenerService;
use App\Services\GoPlusSecurityService;
use Illuminate\Support\Carbon;

class TokenController extends Controller
{
    protected $dexscreenerService;
    protected $goPlusSecurityService;
    protected $activeTemplate;

    public function __construct(DexscreenerService $dexscreenerService, GoPlusSecurityService $goPlusSecurityService)
    {
        $this->dexscreenerService = $dexscreenerService;
        $this->goPlusSecurityService = $goPlusSecurityService;
        $this->activeTemplate = activeTemplate();
    }

    /**
     * Display the token details page
     *
     * @param string $chainId
     * @param string $tokenAddress
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\Response
     */
    public function details($chainId, $tokenAddress)
    {
        try {
            // Get the token from the database
            $token = DexscreenerToken::where('chain_id', $chainId)
                ->where('token_address', $tokenAddress)
                ->first();

            if (!$token) {
                $pageTitle = 'Token Not Found';
                return view($this->activeTemplate . 'token.not_found', compact('pageTitle', 'chainId', 'tokenAddress'));
            }

            // Get fresh token data from the API if available
            // try {
            //     $freshData = $this->dexscreenerService->fetchTokenDetails($chainId, $tokenAddress);

            //     if ($freshData) {
            //         // Update the token with fresh data
            //         $token->price_usd = (string)($freshData['price_usd'] ?? $token->price_usd);
            //         $token->price_change_24h = (string)($freshData['price_change_24h'] ?? $token->price_change_24h);
            //         $token->price_change_5m = (string)($freshData['price_change_5m'] ?? $token->price_change_5m);
            //         $token->price_change_1h = (string)($freshData['price_change_1h'] ?? $token->price_change_1h);
            //         $token->price_change_6h = (string)($freshData['price_change_6h'] ?? $token->price_change_6h);
            //         $token->volume_24h = $freshData['volume_24h'] ?? $token->volume_24h;
            //         $token->market_cap = $freshData['market_cap'] ?? $token->market_cap;
            //         $token->liquidity_usd = $freshData['liquidity_usd'] ?? $token->liquidity_usd;
            //         $token->txn_24h = $freshData['txn_24h'] ?? $token->txn_24h;
            //         $token->buy_count = $freshData['buy_count'] ?? $token->buy_count;
            //         $token->sell_count = $freshData['sell_count'] ?? $token->sell_count;
            //         $token->metadata = $freshData['metadata'] ?? $token->metadata;
            //         $token->save();
            //     }
            // } catch (\Exception $apiError) {
            //     // Log the error but continue with existing data
            //     \Log::error('Error fetching fresh token data: ' . $apiError->getMessage());
            // }

            // Process chart data
            $chartData = $this->processChartData($this->dexscreenerService->getTokenPairs($chainId, $tokenAddress));

            // Make sure chart data is in array format (serializable)
            $chartData = json_decode(json_encode($chartData), true);

            // Try to get ATH data
            $allTimeHigh = null;
            if ($token->all_time_high) {
                $allTimeHigh = [
                    'price' => $token->all_time_high,
                    'timestamp' => $token->ath_date ? \Carbon\Carbon::parse($token->ath_date)->format('M d, Y') : 'Unknown',
                    'change' => $token->price_usd > 0 && $token->all_time_high > 0 ?
                        (($token->price_usd - $token->all_time_high) / $token->all_time_high) * 100 : 0
                ];
            }

            // Get social links from the new social_links table
            $socialLinks = \App\Models\SocialLink::findByToken($chainId, $tokenAddress);

            // Get user-submitted token information if available
            // Always get the most recent submission for this token address (regardless of who submitted it)
            $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token->token_address)
                ->orderBy('created_at', 'desc')
                ->first();

            // If no SubmitCoin record was found, check if this is a presale or fair launch token with a generated ID
            if (!$submitCoin) {
                if (strpos($token->token_address, 'presale_') === 0) {
                    $submitCoin = \App\Models\SubmitCoin::where('contract_address', 'like', 'presale_%')
                        ->orderBy('created_at', 'desc')
                        ->first();
                } elseif (strpos($token->token_address, 'fairlaunch_') === 0) {
                    $submitCoin = \App\Models\SubmitCoin::where('contract_address', 'like', 'fairlaunch_%')
                        ->orderBy('created_at', 'desc')
                        ->first();
                }
            }

            // Check if this is a presale or fair launch token
            $isPresale = false;
            $isFairLaunch = false;

            // Check if there's a force_presale_display flag in the metadata
            if (is_object($token->metadata)) {
                $metadata = $token->metadata;
            } elseif (is_string($token->metadata)) {
                $metadata = json_decode($token->metadata ?? '{}');
            } else {
                $metadata = new \stdClass();
            }

            if (isset($metadata->force_presale_display) && $metadata->force_presale_display === true) {
                $isPresale = true;
            }

            // Check if this is a presale token based on SubmitCoin record
            if (!$isPresale && $submitCoin && $submitCoin->is_presale == 1) {
                $isPresale = true;
            }

            // Check if this is a fair launch token based on SubmitCoin record
            if (!$isFairLaunch && $submitCoin && $submitCoin->is_fair_launch == 1) {
                $isFairLaunch = true;
            }

            // Also check if this is a presale token with a presale_ prefix
            if (!$isPresale && strpos($token->token_address, 'presale_') === 0) {
                $isPresale = true;
            }

            // Also check if this is a fair launch token with a fairlaunch_ prefix
            if (!$isFairLaunch && strpos($token->token_address, 'fairlaunch_') === 0) {
                $isFairLaunch = true;
            }

            // If this is a presale or fair launch token, modify the token data to show appropriate information
            if ($isPresale || $isFairLaunch) {
                // Store original data for reference
                $token->original_price_usd = $token->price_usd;
                $token->original_token_age = $token->token_age;

                // Set token-specific data
                $token->is_presale_token = $isPresale;
                $token->is_fair_launch_token = $isFairLaunch;

                // Get presale/fair launch dates from SubmitCoin only
                $presaleStartDate = $submitCoin->presale_start_date;
                $presaleEndDate = $submitCoin->presale_end_date;

                // Format the presale start date for display
                if ($presaleStartDate) {
                    try {
                        $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', $presaleStartDate);
                        $token->presale_start_date_formatted = $startDate->format('M d, Y');

                        // Check if presale has an end date and if it has passed
                        if ($presaleEndDate) {
                            try {
                                $endDate = \Carbon\Carbon::createFromFormat('m/d/Y', $presaleEndDate);
                                $now = \Carbon\Carbon::now();

                                // If current date is past the end date, show "Ended"
                                if ($now->gt($endDate)) {
                                    $token->presale_countdown = 'Ended';
                                } else {
                                    // Calculate days until presale
                                    $daysUntil = (int)$now->diffInDays($startDate, false);

                                    if ($daysUntil > 0) {
                                        // Future date
                                        $dayText = $daysUntil == 1 ? 'Day' : 'Days';
                                        $token->presale_countdown = 'Starts in ' . $daysUntil . ' ' . $dayText;
                                    } elseif ($daysUntil == 0) {
                                        // Today
                                        $token->presale_countdown = 'Started Today';
                                    } else {
                                        // Past date
                                        $dayText = abs($daysUntil) == 1 ? 'Day' : 'Days';
                                        $token->presale_countdown = 'Started ' . abs($daysUntil) . ' ' . $dayText . ' ago';
                                    }
                                }
                            } catch (\Exception $endDateError) {
                                // If end date format is invalid, fall back to start date logic
                                // Calculate days until presale
                                $now = \Carbon\Carbon::now();
                                $daysUntil = (int)$now->diffInDays($startDate, false);

                                if ($daysUntil > 0) {
                                    // Future date
                                    $dayText = $daysUntil == 1 ? 'Day' : 'Days';
                                    $token->presale_countdown = 'Starts in ' . $daysUntil . ' ' . $dayText;
                                } elseif ($daysUntil == 0) {
                                    // Today
                                    $token->presale_countdown = 'Started Today';
                                } else {
                                    // Past date
                                    $dayText = abs($daysUntil) == 1 ? 'Day' : 'Days';
                                    $token->presale_countdown = 'Started ' . abs($daysUntil) . ' ' . $dayText . ' ago';
                                }
                            }
                        } else {
                            // No end date, use start date logic
                            // Calculate days until presale
                            $now = \Carbon\Carbon::now();
                            $daysUntil = (int)$now->diffInDays($startDate, false);

                            if ($daysUntil > 0) {
                                // Future date
                                $dayText = $daysUntil == 1 ? 'Day' : 'Days';
                                $token->presale_countdown = 'Starts in ' . $daysUntil . ' ' . $dayText;
                            } elseif ($daysUntil == 0) {
                                // Today
                                $token->presale_countdown = 'Started Today';
                            } else {
                                // Past date
                                $dayText = abs($daysUntil) == 1 ? 'Day' : 'Days';
                                $token->presale_countdown = 'Started ' . abs($daysUntil) . ' ' . $dayText . ' ago';
                            }
                        }
                    } catch (\Exception $e) {
                        // If date format is invalid, just use the raw value
                        $token->presale_start_date_formatted = $presaleStartDate;
                        $token->presale_countdown = 'Coming soon';
                    }
                } else {
                    $token->presale_start_date_formatted = 'TBA';
                    $token->presale_countdown = 'Date TBA';
                }

                // Store the raw dates
                $token->presale_start_date = $presaleStartDate;
                $token->presale_end_date = $presaleEndDate;
            } else {
                $token->is_presale_token = false;
                $token->is_fair_launch_token = false;
            }

            // Calculate global rank based on vote count
            $globalRank = $this->calculateGlobalRank($token);

            $pageTitle = $token->token_name . ' (' . $token->token_symbol . ') Price';

            return view($this->activeTemplate . 'token.details', compact('pageTitle', 'token', 'chartData', 'allTimeHigh', 'socialLinks', 'submitCoin', 'globalRank'));
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error displaying token details', [
                'message' => $e->getMessage(),
                'chainId' => $chainId,
                'tokenAddress' => $tokenAddress
            ]);

            // Return a user-friendly error page
            $pageTitle = 'Token Not Found';
            return view($this->activeTemplate . 'token.not_found', compact('pageTitle', 'chainId', 'tokenAddress'));
        }
    }

    /**
     * Calculate the global rank of a token based on positive vote count only
     *
     * @param DexscreenerToken $token
     * @return int
     */
    private function calculateGlobalRank($token)
    {
        // Get all tokens that are verified or added by admin
        $allTokens = DexscreenerToken::where(function($query) {
                $query->where('is_verified', true)
                      ->orWhereNull('submitted_by_user_id'); // Admin added tokens have null user_id
            })
            ->get();

        // Calculate vote counts for all tokens
        foreach ($allTokens as $t) {
            // Get total positive vote count (no deduction for negative votes)
            $t->vote_count = \App\Models\TokenVote::getNetTotalVoteCountByAddress($t->chain_id, $t->token_address);
        }

        // Sort tokens by vote count (highest first)
        $allTokens = $allTokens->sortByDesc('vote_count');

        // Find the position of the current token
        $rank = 1;
        foreach ($allTokens as $t) {
            if ($t->id === $token->id) {
                return $rank;
            }
            $rank++;
        }

        return $rank; // Default to last position if not found
    }

    /**
     * Process pair data to generate chart data for ApexCharts
     *
     * @param array $pairs
     * @return array
     */
    protected function processChartData($pairs)
    {
        $chartData = [];

        if (empty($pairs)) {
            // Generate sample data if no pairs available
            for ($i = 0; $i < 30; $i++) {
                $time = Carbon::now()->subMinutes(30 - $i)->timestamp * 1000;
                $open = rand(30, 40) / 10000;
                $high = rand(40, 50) / 10000;
                $low = rand(20, 30) / 10000;
                $close = rand(30, 40) / 10000;

                $chartData[] = [
                    'x' => $time,
                    'y' => [$open, $high, $low, $close]
                ];
            }

            return $chartData;
        }

        // Find the main pair (the one with the most liquidity)
        $mainPair = null;
        $highestLiquidity = 0;

        foreach ($pairs as $pair) {
            if (isset($pair['liquidity']['usd']) && $pair['liquidity']['usd'] > $highestLiquidity) {
                $highestLiquidity = $pair['liquidity']['usd'];
                $mainPair = $pair;
            }
        }

        if (!$mainPair) {
            // Use the first pair if no pair with liquidity is found
            $mainPair = $pairs[0] ?? null;
        }

        if (!$mainPair || !isset($mainPair['priceUsd'])) {
            // Generate sample data if no valid pair
            return $this->processChartData([]);
        }

        // Generate chart data based on the current price
        // Note: DexScreener doesn't provide historical price data in their API
        // In a real implementation, you would need to use a different API or WebSocket for historical data
        $currentPrice = floatval($mainPair['priceUsd']);
        $priceVariation = $currentPrice * 0.1; // 10% variation

        for ($i = 0; $i < 30; $i++) {
            $time = Carbon::now()->subMinutes(30 - $i)->timestamp * 1000;
            $open = $currentPrice - ($priceVariation / 2) + (rand(-100, 100) / 100) * ($priceVariation / 2);
            $close = $currentPrice - ($priceVariation / 2) + (rand(-100, 100) / 100) * ($priceVariation / 2);
            $high = max($open, $close) + (rand(0, 100) / 100) * ($priceVariation / 4);
            $low = min($open, $close) - (rand(0, 100) / 100) * ($priceVariation / 4);

            $chartData[] = [
                'x' => $time,
                'y' => [$open, $high, $low, $close]
            ];
        }

        return $chartData;
    }



    /**
     * Get token security information from GoPlus API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTokenSecurity(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'chain_id' => 'required|string',
                'token_address' => 'required|string'
            ]);

            $chainId = $request->chain_id;
            $tokenAddress = $request->token_address;

            // Get token security information from GoPlus API
            $securityInfo = $this->goPlusSecurityService->getTokenSecurity($chainId, $tokenAddress);

            // If first attempt failed, try with numeric chain ID
            if (!$securityInfo) {
                // The service already handles chain ID conversion, but we'll try one more time
                // with a lowercase token address as a fallback
                $securityInfo = $this->goPlusSecurityService->getTokenSecurity($chainId, strtolower($tokenAddress));

                // If still no success, return error
                if (!$securityInfo) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Unable to scan contract at this time. The contract may be invalid or not yet available on the blockchain.',
                        'error' => 'API_ERROR'
                    ], 400);
                }
            }

            return response()->json([
                'success' => true,
                'data' => $securityInfo
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching token security information', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching security information',
                'error' => 'SYSTEM_ERROR'
            ], 500);
        }
    }
}