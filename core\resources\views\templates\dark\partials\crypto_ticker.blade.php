@php
    $cryptoSetting = \App\Models\CryptoSetting::first();
    $currencies = [];
    if ($cryptoSetting && $cryptoSetting->is_active) {
        $currencies = \App\Models\CryptoCurrency::latest()->take($cryptoSetting->display_count)->get();
    }
@endphp

@if($cryptoSetting && $cryptoSetting->is_active && count($currencies) > 0)
<div class="header-top bg--body">
    <div class="container-fluid">
        <div class="header-top-wrapper">
            <div class="header-top-left">
                <div class="header-top-left-cont">
                    <marquee behavior="scroll" direction="left">
                        @foreach ($currencies as $currency)
                            <span>{{ __($currency->name) }}:
                                <a href="javascript:void(0)">{{ gs('cur_sym') . number_format($currency->price, 2) }}
                                    @if ($currency->percent_change_24h < 0)
                                        <small class="text-danger"><i class="fas fa-caret-down"></i>
                                            {{ number_format($currency->percent_change_24h, 2) . '%' }}</small>
                                    @else
                                        <small class="text-success"><i class="fas fa-caret-up"></i>
                                            {{ number_format($currency->percent_change_24h, 2) . '%' }}</small>
                                    @endif
                                </a>
                            </span>
                        @endforeach
                    </marquee>
                </div>
            </div>
        </div>
    </div>
</div>
@endif 