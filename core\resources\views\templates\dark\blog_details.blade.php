@extends($activeTemplate . 'layouts.frontend')
@section('content')
    <section class="blog-details py-100">
        <div class="container">
            <div class="row gy-4">
                <div class="col-xl-9 col-lg-8 pe-xl-5">
                    <div class="blog-item">
                        <div class="blog-item__thumb blog-details">
                            <img class="transform-unset" src="{{ frontendImage('blog', @$blog->data_values->blog_image, '900x600') }}" alt="">
                            <span class="blog-item__date"> <span class="blog-item__date-icon"><i class="fas fa-calendar-alt"></i></span> {{ showDateTime($blog->created_at, 'd, M') }} </span>
                        </div>
                        <div class="blog-item__content">
                            @php
                                use App\Models\ArticleVote;
                                // Get regular user votes
                                $regularVotes = ArticleVote::getVoteCount($blog->id);

                                // Get admin trend votes
                                $adminTrendVotes = 0;
                                if (isset($blog->data_values->trend_votes) && is_numeric($blog->data_values->trend_votes)) {
                                    $adminTrendVotes = (int)$blog->data_values->trend_votes;
                                }

                                // Calculate total votes
                                $totalVoteCount = $regularVotes + $adminTrendVotes;

                                $hasVoted = auth()->check() ? ArticleVote::hasVotedToday($blog->id, request()->ip()) : false;
                                $isTrending = isset($blog->data_values->trending) &&
                                              ($blog->data_values->trending == true ||
                                               $blog->data_values->trending == 1 ||
                                               $blog->data_values->trending == '1');
                                $trendVotes = auth()->check() ? auth()->user()->trend_votes : 0;
                            @endphp
                            <div class="vote-section">
                                @if(auth()->check())
                                    <form action="{{ route('article.vote') }}" method="POST" class="d-inline">
                                        @csrf
                                        <input type="hidden" name="article_id" value="{{ $blog->id }}">
                                        <button type="submit" class="btn-vote {{ $hasVoted ? 'disabled' : '' }}" {{ $hasVoted ? 'disabled' : '' }} title="Votes help this article trend on the homepage">
                                            <span class="fire-emoji">🔥</span>
                                            <span class="vote-text">{{ $hasVoted ? 'You voted' : ($isTrending ? 'Already trending' : 'Vote to trend') }}</span>
                                            <span class="vote-count">({{ $totalVoteCount }})</span>
                                        </button>
                                    </form>

                                    @if($trendVotes > 0)
                                        <form action="{{ route('article.trend-vote') }}" method="POST" class="d-inline">
                                            @csrf
                                            <input type="hidden" name="article_id" value="{{ $blog->id }}">
                                            <div class="trend-vote-container">
                                                <input type="text" name="quantity" class="trend-vote-input" min="1" max="{{ $trendVotes }}" placeholder="" onchange="validateVoteAmount(this, {{ $trendVotes }})" onkeyup="validateVoteAmount(this, {{ $trendVotes }})" />
                                                <button type="submit" class="btn-trend-vote" title="Use your purchased trend votes to boost this article">
                                                    <span class="fire-emoji">🔥</span>
                                                    <span class="vote-text">Use Trend Votes</span>
                                                    <span class="trend-vote-count">({{ $trendVotes }} available)</span>
                                                </button>
                                            </div>
                                        </form>
                                    @elseif($hasVoted && $trendVotes == 0)
                                        <a href="{{ route('user.plans.buy.trend.votes') }}" class="btn-trend-vote-buy no-underline">
                                            <span class="fire-emoji">💰</span>
                                            <span class="vote-text">Buy Trend Votes</span>
                                        </a>
                                    @endif
                                @else
                                    <a href="{{ route('user.login') }}?redirect={{ urlencode(url()->current()) }}" class="btn-vote" title="Login to vote and help this article trend on the homepage">
                                        <span class="fire-emoji">🔥</span>
                                        <span class="vote-text">Login to vote</span>
                                        <span class="vote-count">({{ $totalVoteCount }})</span>
                                    </a>
                                @endif
                            </div>
                            <h4 class="blog-item__title">{{ $blog->data_values->title }}</h4>

                            @php echo $blog->data_values->description @endphp

                            <div class="follow-us d-flex align-items-center mt-4 flex-wrap gap-2">
                                <h4 class="follow-title me-2 mb-0"> @lang('Share On') </h4>
                                <ul class="social-list">
                                    <li class="social-list__item">
                                        <a class="social-list__link" href="http://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}"><i class="lab la-facebook-f"></i></a>
                                    </li>

                                    <li class="social-list__item">
                                        <a class="social-list__link" href="https://twitter.com/intent/tweet?text=my share text&amp;url={{ urlencode(url()->current()) }}"><i class="lab la-twitter"></i></a>
                                    </li>

                                    <li class="social-list__item">
                                        <a class="social-list__link" href="http://www.linkedin.com/shareArticle?mini=true&amp;url={{ urlencode(url()->current()) }}"><i class="lab la-linkedin-in"></i></a>
                                    </li>

                                    <li class="social-list__item">
                                        <a class="social-list__link" href="https://www.instagram.com/?url={{ urlencode(url()->current()) }}"><i class="lab la-instagram"></i></a>
                                    </li>
                                </ul>
                            </div>

                            <div class="fb-comments-container mt-4">
                                <div class="fb-comments" data-href="{{ url()->current() }}" data-width="100%" data-numposts="5" data-mobile="true" data-order-by="social" data-colorscheme="dark"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-4">
                    <div class="blog-sidebar">
                        <h5 class="blog-sidebar__title"> @lang('Latest Articles') </h5>

                        <!-- Ad in blog sidebar -->
                        <div class="blog-sidebar-ad mb-4">
                            <x-ad position="blog_sidebar" />
                        </div>

                        @foreach ($latestBlogs as $latestBlog)
                            <div class="latest-blog">
                                <div class="latest-blog__thumb">
                                    <a href="{{ route('blog.details', $latestBlog->slug) }}"> <img src="{{ frontendImage('blog', 'thumb_' . @$latestBlog->data_values->blog_image, '300x200') }}" alt="@lang('Blog')"></a>
                                </div>
                                <div class="latest-blog__content">
                                    <h6 class="latest-blog__title"><a href="{{ route('blog.details', $latestBlog->slug) }}">{{ strLimit($latestBlog->data_values->title, 40) }}</a></h6>
                                    <span class="latest-blog__date">{{ showDateTime(@$latestBlog->created_at, $format = 'd F, Y') }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('style')
<style>
    /* Force no underlines globally except for buttons */
    a:not(.btn--base):not(.btn):not(.register-btn),
    a:not(.btn--base):not(.btn):not(.register-btn):hover,
    a:not(.btn--base):not(.btn):not(.register-btn):focus,
    a:not(.btn--base):not(.btn):not(.register-btn):active {
        text-decoration: none !important;
        border-bottom: none !important;
    }

    .vote-text, .vote-text:hover,
    span.vote-text, span.vote-text:hover {
        text-decoration: none !important;
        border-bottom: none !important;
    }

    /* Special class to override any underlines */
    .no-underline, .no-underline * {
        text-decoration: none !important;
        border-bottom: none !important;
    }

    .vote-section {
        margin: 0 0 20px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 15px;
        flex-wrap: wrap;
    }

    .btn-vote {
        display: inline-flex;
        align-items: center;
        background-color: #333;
        border: 1px solid #444;
        color: #fff;
        border-radius: 5px;
        padding: 6px 12px;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
        font-size: 14px;
        white-space: nowrap;
    }

    .btn-vote:hover:not(.disabled) {
        background-color: #444;
        border-color: #555;
    }

    .btn-vote.disabled {
        cursor: not-allowed;
        opacity: 0.7;
        background-color: #333;
    }

    .btn-trend-vote {
        display: inline-flex;
        align-items: center;
        background-color: #6419c7;
        border: 1px solid #7827d8;
        color: #fff;
        border-radius: 5px;
        padding: 6px 12px;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
        height: auto;
        vertical-align: middle;
        font-size: 14px;
        white-space: nowrap;
    }

    .btn-trend-vote:hover {
        background-color: #7a31e6;
        border-color: #8c44f7;
    }

    .btn-trend-vote-buy {
        display: inline-flex;
        align-items: center;
        background-color: #c99c00;
        border: 1px solid #eab900;
        color: #fff;
        border-radius: 5px;
        padding: 6px 15px;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none !important;
        white-space: nowrap;
    }

    .btn-trend-vote-buy:hover {
        background-color: #daa900;
        border-color: #fac800;
        color: #fff;
        text-decoration: none !important;
    }

    .btn-trend-vote-buy .vote-text {
        color: #fff;
        text-decoration: none !important;
    }

    .fire-emoji {
        font-size: 16px;
        margin-right: 4px;
    }

    .vote-count, .trend-vote-count {
        margin-left: 4px;
        font-size: 13px;
        color: #aaa;
    }

    .trend-vote-count {
        color: #d0b8ff;
    }

    .trend-vote-container {
        display: flex;
        align-items: center;
        flex-direction: row;
        white-space: nowrap;
        gap: 8px;
    }

    .trend-vote-input {
        background-color: #333;
        color: #fff;
        border: 1px solid #555;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 14px;
        height: 32px;
        width: 60px;
        margin-right: 0;
        display: inline-block;
        vertical-align: middle;
        box-sizing: border-box;
    }

    /* Comprehensive responsive styles for different screen sizes */
    /* Large desktop */
    @media (min-width: 1200px) {
        .vote-section {
            flex-direction: row;
            align-items: center;
            gap: 15px;
        }

        .btn-vote, .btn-trend-vote, .btn-trend-vote-buy {
            font-size: 14px;
            padding: 6px 12px;
        }

        .trend-vote-container {
            display: flex;
            align-items: center;
        }
    }

    /* Small desktop and large tablets */
    @media (min-width: 992px) and (max-width: 1199px) {
        .vote-section {
            flex-direction: row;
            flex-wrap: wrap;
            gap: 12px;
        }

        .btn-vote, .btn-trend-vote, .btn-trend-vote-buy {
            font-size: 14px;
            padding: 6px 12px;
        }

        .trend-vote-container {
            display: flex;
            align-items: center;
        }
    }

    /* Medium tablets */
    @media (min-width: 768px) and (max-width: 991px) {
        .vote-section {
            flex-direction: row;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn-vote, .btn-trend-vote, .btn-trend-vote-buy {
            font-size: 13px;
            padding: 6px 10px;
        }

        .fire-emoji {
            font-size: 14px;
        }

        .vote-count, .trend-vote-count {
            font-size: 12px;
        }

        .trend-vote-input {
            height: 32px;
            width: 55px;
            font-size: 13px;
        }

        .trend-vote-container {
            display: flex;
            align-items: center;
        }
    }

    /* Small tablets and large phones */
    @media (min-width: 576px) and (max-width: 767px) {
        .vote-section {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn-vote, .btn-trend-vote, .btn-trend-vote-buy {
            width: 100%;
            justify-content: center;
            font-size: 13px;
            padding: 6px 10px;
        }

        .fire-emoji {
            font-size: 14px;
        }

        .vote-count, .trend-vote-count {
            font-size: 12px;
        }

        .trend-vote-container {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .trend-vote-input {
            width: 60px;
            height: 32px;
        }
    }

    /* Small phones */
    @media (max-width: 575px) {
        .vote-section {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn-vote, .btn-trend-vote, .btn-trend-vote-buy {
            width: 100%;
            justify-content: center;
            font-size: 12px;
            padding: 5px 8px;
        }

        .fire-emoji {
            font-size: 13px;
        }

        .trend-vote-input {
            width: 50px;
            height: 30px;
            font-size: 12px;
        }

        .trend-vote-container {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }

    /* Extra small phones - fix for trend votes button */
    @media (max-width: 400px) {
        .trend-vote-container {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        .trend-vote-input {
            width: 100%;
            max-width: 100%;
            margin-bottom: 2px;
        }

        .trend-vote-container {
            padding: 0 2px;
        }

        .btn-trend-vote {
            width: 100%;
            justify-content: center;
            font-size: 11px;
            padding: 5px 6px;
        }

        .trend-vote-count {
            font-size: 10px;
        }

        .vote-text {
            font-size: 11px;
        }
    }

    /* Universal tablet support - ensures all tablet devices are covered */
    @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
        .vote-section {
            flex-direction: row;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn-vote, .btn-trend-vote, .btn-trend-vote-buy {
            font-size: 13px;
            padding: 6px 12px;
        }

        .trend-vote-container {
            display: flex;
            align-items: center;
        }
    }

    /* iPad Pro specific */
    @media only screen and (min-device-width: 1024px) and (max-device-width: 1366px) {
        .vote-section {
            flex-direction: row;
            flex-wrap: wrap;
            gap: 12px;
        }

        .btn-vote, .btn-trend-vote, .btn-trend-vote-buy {
            font-size: 14px;
            padding: 6px 12px;
        }
    }

    .trend-vote-input:focus {
        outline: none;
        border-color: #7827d8;
        box-shadow: 0 0 0 2px rgba(120, 39, 216, 0.2);
    }

    /* Hide number input spinners */
    .trend-vote-input::-webkit-outer-spin-button,
    .trend-vote-input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    .trend-vote-input[type=number] {
        -moz-appearance: textfield;
    }

    .blog-item__content {
        padding-top: 20px;
    }

    .blog-item__title {
        margin-bottom: 20px;
        margin-top: 0;
    }

    /* Social sharing section responsive styles */
    .follow-us {
        margin-top: 20px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .follow-title {
        margin-bottom: 0;
        font-size: 16px;
    }

    .social-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    .social-list__item {
        margin-right: 5px;
    }

    .social-list__link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        color: #fff;
        font-size: 16px;
        transition: all 0.3s;
    }

    .social-list__link:hover {
        background-color: var(--main);
        color: #fff;
    }

    /* Responsive styles for social sharing */
    @media (max-width: 767px) {
        .follow-us {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .follow-title {
            margin-bottom: 5px;
        }

        .social-list {
            width: 100%;
        }

        .social-list__link {
            width: 32px;
            height: 32px;
            font-size: 14px;
        }
    }

    @media (min-width: 768px) and (max-width: 991px) {
        .follow-us {
            flex-wrap: wrap;
        }

        .social-list__link {
            width: 34px;
            height: 34px;
        }
    }

    /* Blog sidebar ad styles */
    .blog-sidebar-ad {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        overflow: hidden;
    }

    .blog-sidebar-ad .ad-container {
        margin: 0;
    }

    /* Facebook comments responsive container */
    .fb-comments-container {
        width: 100%;
        overflow: hidden;
    }

    .fb-comments,
    .fb-comments iframe[style],
    .fb-comments span {
        width: 100% !important;
    }

    @media (max-width: 767px) {
        .fb-comments-container {
            padding: 0;
        }
    }
</style>
@endpush

@push('script')
<script>
    function validateVoteAmount(input, maxVotes) {
        const value = parseInt(input.value);

        // If empty, allow it
        if (input.value === '') {
            return;
        }

        // If not a number or less than 1, set to empty
        if (isNaN(value) || value < 1) {
            input.value = '';
        }

        // If greater than max votes, set to max votes
        if (value > maxVotes) {
            input.value = maxVotes;
        }
    }
</script>
@endpush
