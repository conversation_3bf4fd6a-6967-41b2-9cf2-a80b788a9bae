<?php

namespace App\Providers;

use App\Constants\Status;
use App\Lib\Searchable;
use App\Models\AdminNotification;
use App\Models\Deposit;
use App\Models\Frontend;
use App\Models\SupportTicket;
use App\Models\User;
use App\Models\Withdrawal;
use App\Models\Page;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\View;
use App\Services\DexscreenerService;
use App\Services\AdminTokenService;
use Mobile_Detect;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        Builder::mixin(new Searchable);

        // Register the DexscreenerService as a singleton
        $this->app->singleton('App\Services\DexscreenerService', function ($app) {
            return new \App\Services\DexscreenerService();
        });

        // Register the AdminTokenService as a singleton
        $this->app->singleton('App\Services\AdminTokenService', function ($app) {
            return new \App\Services\AdminTokenService();
        });

        $this->app->bind('mobile', function () {
            return new Mobile_Detect();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Enable better error reporting for debugging
        if (config('app.debug')) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        }

        if (!cache()->get('SystemInstalled')) {
            $envFilePath = base_path('.env');
            if (!file_exists($envFilePath)) {
                header('Location: install');
                exit;
            }
            $envContents = file_get_contents($envFilePath);
            if (empty($envContents)) {
                header('Location: install');
                exit;
            } else {
                cache()->put('SystemInstalled', true);
            }
        }


        $activeTemplate = activeTemplate();
        $viewShare['activeTemplate'] = $activeTemplate;
        $viewShare['activeTemplateTrue'] = activeTemplate(true);
        $viewShare['emptyMessage'] = 'Data not found';
        view()->share($viewShare);


        view()->composer('admin.partials.sidenav', function ($view) {
            $view->with([
                'bannedUsersCount'           => User::banned()->count(),
                'emailUnverifiedUsersCount' => User::emailUnverified()->count(),
                'mobileUnverifiedUsersCount'   => User::mobileUnverified()->count(),
                'kycUnverifiedUsersCount'   => User::kycUnverified()->count(),
                'kycPendingUsersCount'   => User::kycPending()->count(),
                'pendingTicketCount'         => SupportTicket::whereIN('status', [Status::TICKET_OPEN, Status::TICKET_REPLY])->count(),
                'pendingPaymentCount'    => Deposit::pending()->count(),
                'pendingWithdrawCount'    => Withdrawal::pending()->count(),
            ]);
        });

        view()->composer('admin.partials.topnav', function ($view) {
            $view->with([
                'adminNotifications' => AdminNotification::where('is_read', Status::NO)->with('user')->orderBy('id', 'desc')->take(10)->get(),
                'adminNotificationCount' => AdminNotification::where('is_read', Status::NO)->count(),
            ]);
        });

        view()->composer('partials.seo', function ($view) {
            $seo = Frontend::where('data_keys', 'seo.data')->first();
            $view->with([
                'seo' => $seo ? $seo->data_values : $seo,
            ]);
        });

        if (gs('force_ssl')) {
            \URL::forceScheme('https');
        }


        Paginator::useBootstrapFive();

        View::addNamespace('Template', resource_path('views/templates/' . activeTemplateName()));

        // Add view composer for page selection in page_content section
        View::composer('admin.frontend.section', function ($view) {
            $key = request()->route('key');
            if ($key == 'page_content') {
                $pages = Page::where('tempname', activeTemplate())->get()->map(function($page) {
                    return [
                        'slug' => $page->slug,
                        'name' => $page->name
                    ];
                })->pluck('name', 'slug')->toArray();

                $view->with('pageOptions', $pages);
            }
        });

        View::composer('admin.frontend.element', function ($view) {
            $key = request()->route('key');
            if ($key == 'page_content') {
                $pages = Page::where('tempname', activeTemplate())->get()->map(function($page) {
                    return [
                        'slug' => $page->slug,
                        'name' => $page->name
                    ];
                })->pluck('name', 'slug')->toArray();

                $view->with('pageOptions', $pages);
            }
        });
    }
}
