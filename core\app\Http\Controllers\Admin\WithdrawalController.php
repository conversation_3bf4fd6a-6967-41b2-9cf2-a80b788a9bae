<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Withdrawal;
use Illuminate\Http\Request;

class WithdrawalController extends Controller
{
    public function pending()
    {
        $pageTitle   = 'Pending Withdrawals';
        $withdrawals = $this->withdrawalData('pending');
        $currencies = Withdrawal::select('currency')->distinct()->get()->pluck('currency')->toArray();
        return view('admin.withdraw.withdrawals', compact('pageTitle', 'withdrawals', 'currencies'));
    }

    public function approved()
    {
        $pageTitle   = 'Approved Withdrawals';
        $withdrawals = $this->withdrawalData('approved');
        $currencies = Withdrawal::select('currency')->distinct()->get()->pluck('currency')->toArray();
        return view('admin.withdraw.withdrawals', compact('pageTitle', 'withdrawals', 'currencies'));
    }

    public function rejected()
    {
        $pageTitle   = 'Rejected Withdrawals';
        $withdrawals = $this->withdrawalData('rejected');
        $currencies = Withdrawal::select('currency')->distinct()->get()->pluck('currency')->toArray();
        return view('admin.withdraw.withdrawals', compact('pageTitle', 'withdrawals', 'currencies'));
    }

    public function all()
    {
        $pageTitle      = 'All Withdrawals';
        $withdrawalData = $this->withdrawalData($scope = null, $summery = true);
        $currencies = Withdrawal::select('currency')->distinct()->get()->pluck('currency')->toArray();
        $withdrawals    = $withdrawalData['data'];
        $summery        = $withdrawalData['summery'];
        $successful     = $summery['successful'];
        $pending        = $summery['pending'];
        $rejected       = $summery['rejected'];

        return view('admin.withdraw.withdrawals', compact('pageTitle', 'withdrawals', 'successful', 'pending', 'rejected', 'currencies'));
    }

    protected function withdrawalData($scope = null, $summery = false)
    {
        if ($scope) {
            $withdrawals = Withdrawal::$scope();
        } else {
            $withdrawals = Withdrawal::where('status', '!=', Status::PAYMENT_INITIATE);
        }

        $withdrawals = $withdrawals->searchable(['trx', 'user:username'])->dateFilter()->filter(['currency']);

        if (!$summery) {
            return $withdrawals->with(['user'])->orderBy('id', 'desc')->paginate(getPaginate());
        } else {

            $successful = clone $withdrawals;
            $pending    = clone $withdrawals;
            $rejected   = clone $withdrawals;

            $successfulSummery = $successful->where('status', Status::PAYMENT_SUCCESS)->sum('amount');
            $pendingSummery    = $pending->where('status', Status::PAYMENT_PENDING)->sum('amount');
            $rejectedSummery   = $rejected->where('status', Status::PAYMENT_REJECT)->sum('amount');

            return [
                'data'    => $withdrawals->with(['user'])->orderBy('id', 'desc')->paginate(getPaginate()),
                'summery' => [
                    'successful' => $successfulSummery,
                    'pending'    => $pendingSummery,
                    'rejected'   => $rejectedSummery,
                ],
            ];
        }
    }

    public function details($id)
    {
        $general    = gs();
        $withdrawal = Withdrawal::where('status', '!=', Status::PAYMENT_INITIATE)->with(['user'])->findOrFail($id);
        $pageTitle  = $withdrawal->user->username . ' Withdraw Requested ' . showAmount($withdrawal->amount);
        $details    = $withdrawal->withdraw_information ? json_encode($withdrawal->withdraw_information) : null;
        return view('admin.withdraw.detail', compact('pageTitle', 'withdrawal', 'details'));
    }

    public function approve(Request $request)
    {
        $request->validate(['id' => 'required|integer']);

        $withdraw = Withdrawal::where('status', Status::PAYMENT_PENDING)->with('user')->findOrFail($request->id);

        $withdrawInfo = json_decode($withdraw->withdraw_information);
        $walletAddress = $withdrawInfo->wallet_address ?? 'N/A';

        // Check if this is a referral earnings withdrawal
        $isReferralWithdrawal = isset($withdrawInfo->withdrawal_type) && $withdrawInfo->withdrawal_type === 'referral_earnings';

        // If this is a referral withdrawal, create a negative referral log entry to deduct the amount
        if ($isReferralWithdrawal) {
            $referralLog = new \App\Models\ReferralLog();
            $referralLog->user_id = $withdraw->user_id; // Same user (for record keeping)
            $referralLog->referee_id = $withdraw->user_id; // Deduct from this user's referral balance
            $referralLog->amount = -$withdraw->amount; // Negative amount to deduct
            $referralLog->level = 0; // Level 0 indicates a withdrawal
            $referralLog->percent = 0; // No percentage for withdrawals
            $referralLog->save();
        }

        // Update withdrawal information to include transaction URL if provided
        if ($request->transaction_url) {
            $withdrawInfo->transaction_url = $request->transaction_url;
            $withdraw->withdraw_information = json_encode($withdrawInfo);
        }

        $withdraw->status         = Status::PAYMENT_SUCCESS;
        $withdraw->admin_feedback = $request->details;
        $withdraw->save();

        $coinCode = $withdraw->currency;

        notify($withdraw->user, 'WITHDRAW_APPROVE', [
            'wallet'         => $walletAddress,
            'amount'         => showAmount($withdraw->amount),
            'coin_code'      => $coinCode,
            'trx'            => $withdraw->trx,
            'admin_feedback' => $withdraw->admin_feedback,
            'transaction_url' => $withdrawInfo->transaction_url ?? '',
        ]);

        $notify[] = ['success', 'Withdrawal approved successfully'];
        return to_route('admin.withdraw.data.pending')->withNotify($notify);
    }

    public function reject(Request $request)
    {
        $request->validate(['id' => 'required|integer']);
        $withdraw = Withdrawal::where('status', Status::PAYMENT_PENDING)->with('user')->findOrFail($request->id);

        $withdrawInfo = json_decode($withdraw->withdraw_information);
        $walletAddress = $withdrawInfo->wallet_address ?? 'N/A';
        $coinCode = $withdraw->currency;

        $user = User::find($withdraw->user_id);

        $withdraw->status         = Status::PAYMENT_REJECT;
        $withdraw->admin_feedback = $request->details;
        $withdraw->save();

        // Create transaction record for the refund
        $transaction               = new Transaction();
        $transaction->user_id      = $withdraw->user_id;
        $transaction->amount       = $withdraw->amount;
        $transaction->post_balance = $user->balance;
        $transaction->currency     = $withdraw->currency;
        $transaction->charge       = 0;
        $transaction->trx_type     = '+';
        $transaction->remark       = 'withdraw_reject';
        $transaction->details      = showAmount($withdraw->amount) . ' ' . $withdraw->currency . ' Refunded from withdrawal rejection';
        $transaction->trx          = $withdraw->trx;
        $transaction->save();

        notify($user, 'WITHDRAW_REJECT', [
            'wallet'         => $walletAddress,
            'amount'         => showAmount($withdraw->amount),
            'post_balance'   => showAmount($user->balance),
            'coin_code'      => $coinCode,
            'trx'            => $withdraw->trx,
            'admin_feedback' => $withdraw->admin_feedback,
        ]);

        $notify[] = ['success', 'Withdrawal rejected successfully'];
        return to_route('admin.withdraw.data.pending')->withNotify($notify);
    }
}
