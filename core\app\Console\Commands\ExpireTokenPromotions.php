<?php

namespace App\Console\Commands;

use App\Models\TokenPromotion;
use Illuminate\Console\Command;

class ExpireTokenPromotions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tokens:expire-promotions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire token promotions that have passed their end date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $expiredPromotions = TokenPromotion::where('is_active', true)
            ->where('end_date', '<', now())
            ->update(['is_active' => false]);

        $this->info("Expired {$expiredPromotions} token promotions.");
        
        return Command::SUCCESS;
    }
}
