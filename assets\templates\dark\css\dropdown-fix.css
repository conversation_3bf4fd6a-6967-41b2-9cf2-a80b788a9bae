/* Override default browser dropdown styling for all pages */

/* Force global dropdown styling */
select,
.form-select,
.form-control,
.custom--select,
select.form-control {
    background-color: #2C2F3E !important;
    color: #fff !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    -webkit-appearance: menulist !important;
    appearance: menulist !important;
}

/* Stronger override for option styling */
select option,
.form-select option,
.form-control option,
.custom--select option,
option {
    background-color: #2C2F3E !important;
    color: #fff !important;
    border: none !important;
}

/* Active/selected state */
select option:checked,
.form-select option:checked,
.form-control option:checked,
.custom--select option:checked,
option:checked {
    background-color: #BE8400 !important;
    background: #BE8400 !important;
    color: #fff !important;
    -webkit-appearance: menulist !important;
    appearance: menulist !important;
}

/* Hover state */
select option:hover,
.form-select option:hover,
.form-control option:hover,
.custom--select option:hover,
option:hover {
    background-color: #BE8400 !important;
    background: #BE8400 !important;
    color: #fff !important;
}

/* Focus state */
select:focus,
.form-select:focus,
.form-control:focus,
.custom--select:focus {
    border-color: #BE8400 !important;
    box-shadow: 0 0 0 0.25rem rgba(190, 132, 0, 0.25) !important;
}

/* Firefox specific */
@-moz-document url-prefix() {
    select option:checked,
    .form-select option:checked,
    .form-control option:checked,
    .custom--select option:checked,
    option:checked {
        background-color: #2C2F3E !important;
        background: #2C2F3E !important;
        color: #fff !important;
        -moz-appearance: none !important;
    }

    select option:hover,
    .form-select option:hover,
    .form-control option:hover,
    .custom--select option:hover,
    option:hover {
        background-color: #BE8400 !important;
        background: #BE8400 !important;
        color: #fff !important;
        box-shadow: 0 0 10px 100px #BE8400 inset !important;
        -moz-appearance: none !important;
    }
}

/* Chrome/Safari specific */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    select option:checked,
    .form-select option:checked,
    .form-control option:checked,
    .custom--select option:checked,
    option:checked {
        background-color: #2C2F3E !important;
        background: #2C2F3E !important;
        color: #fff !important;
        -webkit-appearance: menulist !important;
    }

    select option:hover,
    .form-select option:hover,
    .form-control option:hover,
    .custom--select option:hover,
    option:hover {
        background-color: #BE8400 !important;
        background: #BE8400 !important;
        color: #fff !important;
        -webkit-appearance: menulist !important;
    }
}

/* Select2 specific overrides */
.select2-results__option.select2-results__option--selected {
    background-color: #2C2F3E !important;
    background: #2C2F3E !important;
    color: #fff !important;
}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    background-color: #BE8400 !important;
    background: #BE8400 !important;
    color: #fff !important;
}