<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\DexscreenerService;

class UpdatePopularTokensDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-popular-tokens-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refreshes popular token data from Dexscreener.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(DexscreenerService $dexscreenerService)
    {
        Log::info('==== UpdatePopularTokensDataCommand: DEXSCREENER POPULAR TOKEN REFRESH STARTED ====');

        try {
            // Clear all token-related caches
            Cache::forget('dexscreener_tokens');
            Cache::forget('admin_tokens');
            Cache::forget('admin_tokens_active');
            Cache::forget('admin_tokens_all');
            Log::info('UpdatePopularTokensDataCommand: Cleared token caches.');

            $dexResult = $dexscreenerService->refreshPopularTokens();

            if ($dexResult) {
                Log::info('==== UpdatePopularTokensDataCommand: DEXSCREENER POPULAR TOKEN REFRESH COMPLETED SUCCESSFULLY ====');
                $this->info('Popular token data refreshed successfully.');
                return Command::SUCCESS;
            } else {
                Log::error('==== UpdatePopularTokensDataCommand: DEXSCREENER POPULAR TOKEN REFRESH FAILED ====');
                $this->error('Failed to refresh popular token data.');
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            Log::error('UpdatePopularTokensDataCommand: Dexscreener API Error: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            $this->error('Error refreshing popular tokens: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
} 