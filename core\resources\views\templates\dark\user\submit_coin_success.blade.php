@extends($activeTemplate . 'layouts.master')
@section('content')
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card bg-dark">
            <div class="card-header text-center py-3" style="background-color: #BE8400;">
                <h3 class="text-white">@lang('Submission Successful')</h3>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="success-icon my-3">
                        <i class="las la-check-circle" style="font-size: 80px; color: #BE8400;"></i>
                    </div>
                    <h4 class="text-white mb-3">@lang('Your token has been submitted successfully!')</h4>
                    <p class="mb-4">@lang('Thank you for submitting your token. Our team will review your submission and verify it before displaying it on the homepage.')</p>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6 mx-auto">
                        <div class="card bg--base-two mb-4">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="token-icon-wrapper me-3">
                                        <img src="{{ asset('assets/images/coin_logos/'.$submittedToken['logo']) }}" alt="{{ $submittedToken['symbol'] }}" class="token-icon" width="64" height="64" style="border-radius: 50%;">
                                    </div>
                                    <div class="token-info">
                                        <h5 class="mb-1 text-white">{{ $submittedToken['name'] }} ({{ $submittedToken['symbol'] }})</h5>
                                        <p class="mb-0">{{ $submittedToken['blockchain'] }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="token-status-info mb-4">
                    <div class="alert text-white" role="alert" style="background-color: #232a35;">
                        <h5 class="alert-heading text-white"><i class="las la-info-circle me-2"></i> @lang('What happens next?')</h5>
                        <p>@lang('Your submission is now pending review by our administrators. Once verified, your token will be listed on our platform.')</p>
                        <hr class="border-light opacity-25">
                    </div>
                </div>

                <div class="text-center mt-4">
                    <div class="row">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <a href="{{ route('user.coin.my.submissions') }}" class="btn px-5 py-3 w-100" style="background-color: #BE8400; color: white;">@lang('My Submitted Coins')</a>
                        </div>
                        @if(isset($submittedToken['chain_id']) && isset($submittedToken['token_address']))
                        <div class="col-md-6">
                            <a href="{{ route('token.details', ['chainId' => $submittedToken['chain_id'], 'tokenAddress' => $submittedToken['token_address']]) }}" class="btn px-5 py-3 w-100" style="background-color: #232a35; color: white; border: 1px solid #BE8400;">@lang('View Token')</a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
    .token-icon {
        border-radius: 50%;
        object-fit: cover;
        background-color: #2c2f3e;
    }

    .success-icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(0.95);
            opacity: 0.7;
        }
        70% {
            transform: scale(1);
            opacity: 1;
        }
        100% {
            transform: scale(0.95);
            opacity: 0.7;
        }
    }
</style>
@endpush