<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\CclToken;
use App\Models\Presale;
use App\Models\Transaction;
use App\Models\UserTokenPurchase;
use App\Constants\Status;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CclTokenController extends Controller
{
    /**
     * Display the Buy Tokens page
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function buyTokens()
    {
        $pageTitle = 'Buy CCL Tokens';
        $cclToken = CclToken::getData();

        // Get active presales
        $presales = Presale::where('is_active', true)
            ->orderBy('position', 'asc')
            ->get();

        // If no presales are available, redirect to home
        if ($presales->isEmpty()) {
            return redirect()->route('user.home');
        }

        // Sort presales to display 'Active' status first, 'Upcoming' second, then others in chronological order
        $activePresales = collect();
        $upcomingPresales = collect();
        $otherPresales = collect();

        foreach ($presales as $presale) {
            if ($presale->getStatus() == 'Active') {
                $activePresales->push($presale);
            } elseif ($presale->getStatus() == 'Upcoming') {
                $upcomingPresales->push($presale);
            } else {
                $otherPresales->push($presale);
            }
        }

        // Combine the collections with active presales first, upcoming second, then others
        $sortedPresales = $activePresales->merge($upcomingPresales)->merge($otherPresales);
        $presales = $sortedPresales;

        return view('Template::user.launchpad.buy', compact('pageTitle', 'cclToken', 'presales'));
    }

    /**
     * Display a specific presale
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\View
     */
    public function viewPresale($id)
    {
        $presale = Presale::findOrFail($id);
        $pageTitle = $presale->title;

        return view('Template::user.launchpad.presale', compact('pageTitle', 'presale'));
    }

    /**
     * Process token purchase
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function purchase(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:20',
            'tokens' => 'required|numeric|min:0',
            'payment_method' => 'required|in:1', // 1 for balance
        ]);

        $user = auth()->user();

        // Get all active presales
        $presales = Presale::where('is_active', true)
            ->orderBy('position', 'asc')
            ->get();

        // Find the presale with 'Active' status
        $presale = null;
        foreach ($presales as $p) {
            if ($p->getStatus() == 'Active') {
                $presale = $p;
                break;
            }
        }

        if (!$presale) {
            $notify[] = ['error', 'No active presale found'];
            return back()->withNotify($notify);
        }

        $amount = $request->amount;
        $tokens = $request->tokens;

        // Check if presale is active
        if ($presale->getStatus() !== 'Active') {
            $notify[] = ['error', 'This presale is not active'];
            return back()->withNotify($notify);
        }

        // Check if user has enough balance
        if ($user->balance < $amount) {
            $notify[] = ['error', 'Insufficient balance'];
            return back()->withNotify($notify);
        }

        // Check if there are enough tokens available
        if ($presale->quantity) {
            // Ensure proper numeric conversion by removing any non-numeric characters
            $availableQuantity = (float)preg_replace('/[^0-9.]/', '', $presale->quantity);
            if ($tokens > $availableQuantity) {
                $notify[] = ['error', 'Not enough tokens available in this presale'];
                return back()->withNotify($notify);
            }
        }

        DB::beginTransaction();
        try {
            // Update user balance
            $user->balance -= $amount;
            $user->save();

            // Update presale sold amount with USD amount
            $presale->sold += $amount;

            // Update the quantity field to reduce available tokens
            $tokensFloat = (float) $tokens;
            if ($presale->quantity) {
                // Ensure proper numeric conversion by removing any non-numeric characters
                $availableQuantity = (float)preg_replace('/[^0-9.]/', '', $presale->quantity);
                $presale->quantity = $availableQuantity - $tokensFloat;
            }
            $presale->save();

            // Create transaction record
            $transaction = new Transaction();
            $transaction->user_id = $user->id;
            $transaction->amount = $amount;
            $transaction->post_balance = $user->balance;
            $transaction->charge = 0;
            $transaction->trx_type = '-';
            $transaction->details = 'Purchase of ' . $tokens . ' ' . $presale->token_symbol . ' tokens';
            $transaction->trx = getTrx();
            $transaction->currency = gs('cur_text');
            $transaction->save();

            // Record the token purchase
            $tokenPurchase = new UserTokenPurchase();
            $tokenPurchase->user_id = $user->id;
            $tokenPurchase->presale_id = $presale->id;
            $tokenPurchase->token_name = $presale->token_name;
            $tokenPurchase->token_symbol = $presale->token_symbol;
            $tokenPurchase->amount = $amount;
            $tokenPurchase->tokens = $tokens;
            $tokenPurchase->price = $presale->price;
            $tokenPurchase->transaction_id = $transaction->id;
            $tokenPurchase->save();

            // Process referral commission on token purchase
            if (gs()->referral_system && $user->ref_by) {
                \Illuminate\Support\Facades\Log::channel('daily')->info('CCL TOKEN PURCHASE: Processing referral commission', [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'ref_by' => $user->ref_by,
                    'amount' => $amount,
                    'trx' => $transaction->trx
                ]);
                levelCommission($user, $amount, $transaction->trx);
            }

            DB::commit();

            $notify[] = ['success', 'Token purchase successful'];
            return redirect()->route('user.ccl.token.buy')->withNotify($notify);
        } catch (\Exception $e) {
            DB::rollback();
            $notify[] = ['error', 'Something went wrong: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Display the My Tokens page
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function myTokens()
    {
        $pageTitle = 'My CCL Tokens';
        $cclToken = CclToken::getData();

        // Get user's token purchases
        $user = auth()->user();
        $allPurchases = UserTokenPurchase::where('user_id', $user->id)->get();

        // Group purchases by token symbol
        $groupedPurchases = collect();
        $purchasesBySymbol = $allPurchases->groupBy('token_symbol');

        foreach ($purchasesBySymbol as $symbol => $purchases) {
            // Get the most recent purchase for this token
            $latestPurchase = $purchases->sortByDesc('created_at')->first();

            // Create a new purchase object with consolidated data
            $consolidatedPurchase = clone $latestPurchase;
            $consolidatedPurchase->tokens = $purchases->sum('tokens');
            $consolidatedPurchase->amount = $purchases->sum('amount');

            $groupedPurchases->push($consolidatedPurchase);
        }

        // Sort by most recent purchase
        $groupedPurchases = $groupedPurchases->sortByDesc('created_at');

        // Paginate the results
        $perPage = getPaginate(10);
        $page = request()->get('page', 1);
        $offset = ($page - 1) * $perPage;

        $tokenPurchases = new \Illuminate\Pagination\LengthAwarePaginator(
            $groupedPurchases->slice($offset, $perPage),
            $groupedPurchases->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        // Calculate total tokens owned (only Pending tokens)
        $pendingPurchases = $allPurchases->where('token_transfer_status', UserTokenPurchase::STATUS_PENDING);
        $totalTokens = $pendingPurchases->sum('tokens');

        // Get the token symbol of the pending tokens (if any)
        $pendingTokenSymbol = null;
        if ($pendingPurchases->isNotEmpty()) {
            $pendingTokenSymbol = $pendingPurchases->first()->token_symbol;
        }

        return view('Template::user.launchpad.my_tokens', compact('pageTitle', 'cclToken', 'tokenPurchases', 'totalTokens', 'pendingTokenSymbol'));
    }

    /**
     * Save the user's CCL token address
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function saveTokenAddress(Request $request)
    {
        $request->validate([
            'token_address' => 'required|string|min:10',
        ]);

        $user = auth()->user();

        // Check if user already has a token address set
        if ($user->ccl_token_address) {
            $notify[] = ['error', 'You have already set your token address. This cannot be changed.'];
            return back()->withNotify($notify);
        }

        // Save the token address
        $user->ccl_token_address = $request->token_address;
        $user->save();

        $notify[] = ['success', 'Your token address has been saved successfully.'];
        return back()->withNotify($notify);
    }
}
