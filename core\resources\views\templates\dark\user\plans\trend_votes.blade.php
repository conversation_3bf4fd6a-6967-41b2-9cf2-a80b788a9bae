@extends($activeTemplate . 'layouts.master')

@include('templates.dark.user.plans.custom_style')
@section('content')
@if(count($plans) > 0)
<div style="background-color: rgba(49, 215, 169, 0.1); padding: 15px; border-radius: 5px; border-left: 4px solid #31D7A9; margin-bottom: 20px;" class="d-flex justify-content-between align-items-center trend-votes-header">
    <h4 style="color: #31D7A9; margin-bottom: 0;">🔥 @lang('Buy Trend Votes')</h4>
    <span class="text-white">@lang('Current Trend Votes'): <strong>{{ auth()->user()->trend_votes }}</strong></span>
</div>

<div class="row gy-4">
    @foreach($plans as $plan)
    <div class="col-xl-4 col-md-6">
        <div class="price-item">
            <div class="price-item__header">
                <h2 class="price-item__price">{{ gs('cur_sym') . showAmount($plan->price, currencyFormat: false) }}</h2>
            </div>
            <div class="price-item__content">
                <div class="price-item__body">
                    <ul class="text-list">
                        <li class="text-list__item">
                            <i class="las la-fire text--base"></i> @lang('Trend Votes'): {{ $plan->trend_votes }}
                        </li>
                        @if($plan->description)
                        <li class="text-list__item">{{ $plan->description }}</li>
                        @endif
                        @foreach ($plan->features ?? [] as $feature)
                        <li class="text-list__item">{{ $feature }}</li>
                        @endforeach
                    </ul>
                </div>
                <div class="price-item__button">
                    <button class="btn--base buy-plan" data-bs-toggle="modal" data-bs-target="#buyPlanModal"
                        data-id="{{ $plan->id }}"
                        data-price="{{ showAmount($plan->price, currencyFormat: false) }}"
                        data-votes="{{ $plan->trend_votes }}">@lang('Buy Now')</button>
                </div>
            </div>
        </div>
    </div>
    @endforeach
</div>
@else
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info text-center">
            @lang('No trend vote plans available at the moment.')
        </div>
    </div>
</div>
@endif

@if($plans->hasPages())
<div class="mt-4">
    {{ paginateLinks($plans) }}
</div>
@endif

<!-- Buy Plan Modal -->
<div class="modal custom--modal fade" id="buyPlanModal" tabindex="-1" aria-labelledby="buyPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="buyPlanModalLabel">@lang('Buy Trend Votes')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('user.plans.purchase') }}" method="POST">
                    @csrf
                    <input type="hidden" name="plan_id" id="plan_id">
                    <div class="form-group">
                        <label>@lang('Trend Votes')</label>
                        <div class="input-group">
                            <span class="plan-votes form-control"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>@lang('Price')</label>
                        <div class="input-group">
                            <span class="plan-price form-control"></span>
                            <span class="input-group-text">{{ gs('cur_text') }}</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>@lang('Current Balance')</label>
                        <div class="input-group">
                            <span class="form-control">{{ showAmount(auth()->user()->balance) }}</span>
                            <span class="input-group-text">{{ gs('cur_text') }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <input type="hidden" name="payment_method" value="1">
                    </div>
                    <div class="form-group mt-3 text-end">
                        <button type="submit" class="btn btn--base w-100">@lang('Confirm Purchase')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    (function($) {
        "use strict";

        $('.buy-plan').on('click', function() {
            var modal = $('#buyPlanModal');
            modal.find('#plan_id').val($(this).data('id'));
            modal.find('.plan-price').text($(this).data('price'));
            modal.find('.plan-votes').text($(this).data('votes'));
        });

        // Override default styling for dropdowns
        $('select').on('focus', function() {
            // Apply theme colors to all option elements
            $(this).find('option').each(function() {
                this.style.backgroundColor = '#2C2F3E';
                this.style.color = '#fff';
            });

            // Apply styling to the selected option
            $(this).find('option:selected').css({
                'background-color': '#2C2F3E',
                'color': '#fff'
            });
        });

        // When dropdown changes, apply styles to selected option
        $('select').on('change', function() {
            $(this).find('option').each(function() {
                this.style.backgroundColor = '#2C2F3E';
            });
        });
    })(jQuery);
</script>
@endpush

@push('style')
<style>
    /* Responsive styles for the 'Buy Trend Votes' card */
    @media (max-width: 767px) {
        .trend-votes-header {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 10px;
        }

        .trend-votes-header h4 {
            font-size: 18px;
            margin-bottom: 5px !important;
        }

        .trend-votes-header span {
            font-size: 14px;
        }
    }

    /* Tablet specific styles */
    @media (min-width: 768px) and (max-width: 991px) {
        .trend-votes-header h4 {
            font-size: 20px;
        }
    }

    /* Specific tablet dimensions */
    @media only screen and (width: 728px) and (height: 1024px),
           only screen and (width: 820px) and (height: 1180px),
           only screen and (width: 912px) and (height: 1368px),
           only screen and (width: 853px) and (height: 1280px),
           only screen and (width: 1024px) and (height: 1366px) {
        .trend-votes-header {
            display: flex !important;
            flex-direction: row !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        .trend-votes-header h4 {
            font-size: 18px;
            margin-bottom: 0 !important;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .trend-votes-header {
            padding: 12px !important;
        }

        .trend-votes-header h4 {
            font-size: 16px;
        }

        .trend-votes-header span {
            font-size: 13px;
        }
    }

    /* Ensure the modal title text is white */
    #buyPlanModalLabel {
        color: #fff !important;
        text-decoration: none !important;
    }

    .custom--modal select.form-control {
        background-color: #2C2F3E !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #fff !important;
        -webkit-appearance: menulist !important;
        -moz-appearance: menulist !important;
        appearance: menulist !important;
        background-image: none !important;
    }

    .custom--modal select.form-control option {
        background-color: #2C2F3E;
        color: #fff;
    }

    .custom--modal select.form-control option:hover,
    .custom--modal select.form-control option:focus,
    .custom--modal select.form-control option:active {
        background: linear-gradient(#BE8400, #BE8400) !important;
        background-color: #BE8400 !important;
        color: #fff !important;
    }

    .custom--modal select.form-control option:checked {
        background: linear-gradient(#2C2F3E, #2C2F3E) !important;
        background-color: #2C2F3E !important;
        color: #fff !important;
    }

    .custom--modal select.form-control:focus {
        box-shadow: 0 0 0 0.25rem rgba(190, 132, 0, 0.25) !important;
        border-color: #BE8400 !important;
    }

    /* Override browser default styling */
    .custom--modal select.form-control {
        outline: none !important;
    }

    /* This is needed to override browser's built-in styling */
    .form-control:focus {
        border-color: #BE8400 !important;
        box-shadow: 0 0 0 0.25rem rgba(190, 132, 0, 0.25) !important;
    }

    /* Specifically for Firefox */
    .custom--modal select.form-control:-moz-focusring {
        color: transparent !important;
        text-shadow: 0 0 0 #fff !important;
    }

    /* For webkit browsers */
    .custom--modal select.form-control::-webkit-focus-inner {
        border: 0 !important;
    }

    /* Additional overrides for the dropdown */
    select:focus-visible {
        outline-color: #BE8400 !important;
    }

    /* Global override for select options */
    select option {
        background-color: #2C2F3E !important;
        color: #fff !important;
    }

    select option:hover,
    select option:focus,
    select option:active {
        background: #BE8400 !important;
        color: #fff !important;
    }

    select option:checked,
    select option:selected {
        background: #2C2F3E !important;
        color: #fff !important;
    }

    /* Direct override for blue highlight */
    option:hover, option:focus {
        background-color: #BE8400 !important;
    }

    /* -webkit-appearance: none causes issues with Chrome - restore but with theme colors */
    select option:hover { background-color: #BE8400 !important; }

    /* Additional attempt for webkit */
    @supports (-webkit-appearance:none) {
        select option:checked {
            background: linear-gradient(#2C2F3E, #2C2F3E) !important;
            color: #fff !important;
        }

        select option:hover {
            background: linear-gradient(#BE8400, #BE8400) !important;
            color: #fff !important;
        }
    }

    /* !IMPORTANT FIX FOR ALL NATIVE DROPDOWNS */
    select {
        background-color: #2C2F3E !important;
        color: #fff !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    select option {
        background-color: #2C2F3E !important;
        color: #fff !important;
    }

    select option:hover,
    select option:focus,
    select option:active,
    select option:checked {
        background-color: #BE8400 !important;
        color: #fff !important;
    }

    /* Force the browser to use our colors */
    select:-internal-list-box option:checked {
        background-color: #2C2F3E !important;
        color: #fff !important;
    }

    select:-internal-list-box option:hover {
        background-color: #BE8400 !important;
        color: #fff !important;
    }
</style>

<!-- Inject special script to override dropdown styling -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Create a style element
        var style = document.createElement('style');
        // WebKit hack
        style.appendChild(document.createTextNode(''));
        // Add the style element to the document
        document.head.appendChild(style);

        // Force all dropdown options to have theme colors
        var sheet = style.sheet;
        sheet.insertRule('select { background-color: #2C2F3E !important; color: #fff !important; }', 0);
        sheet.insertRule('select option { background-color: #2C2F3E !important; color: #fff !important; }', 1);
        sheet.insertRule('select option:hover, select option:focus, select option:active { background-color: #BE8400 !important; color: #fff !important; }', 2);
        sheet.insertRule('select option:checked, select option:selected { background-color: #2C2F3E !important; color: #fff !important; }', 3);
    });
</script>
@endpush