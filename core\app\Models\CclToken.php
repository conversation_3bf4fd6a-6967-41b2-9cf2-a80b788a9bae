<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CclToken extends Model
{
    use HasFactory;

    protected $table = 'ccl_token_data';

    protected $fillable = [
        'token_name',
        'token_symbol',
        'total_supply',
        'blockchain',
        'about_content',
        'utility_items',
        'roadmap_items',
        'distribution_items',
        'is_enabled',
        'buy_token_url',
        'whitepaper_url',
        'presale_start_date',
        'presale_end_date',
        'presale_quantity',
        'presale_price',
        'presale_sold'
    ];

    protected $casts = [
        'utility_items' => 'array',
        'roadmap_items' => 'array',
        'distribution_items' => 'array',
        'is_enabled' => 'boolean',
        'presale_start_date' => 'date',
        'presale_end_date' => 'date',
        'presale_price' => 'decimal:8',
        'presale_sold' => 'decimal:8'
    ];

    /**
     * Get a single instance of the CCL token data
     *
     * @return CclToken
     */
    public static function getData()
    {
        $data = self::first();

        if (!$data) {
            // Create default data if none exists
            $data = self::create([
                'token_name' => 'CCL Token',
                'token_symbol' => 'CCL',
                'total_supply' => '100,000,000 CCL',
                'blockchain' => 'Ethereum (ERC-20)',
                'about_content' => 'CCL Token is the native utility token of our platform. It powers the ecosystem and provides various benefits to holders.',
                'utility_items' => [
                    [
                        'title' => 'Platform Governance',
                        'description' => 'CCL holders can participate in governance decisions and vote on platform updates.'
                    ],
                    [
                        'title' => 'Fee Discounts',
                        'description' => 'Holding CCL tokens provides discounts on platform fees and services.'
                    ],
                    [
                        'title' => 'Premium Features',
                        'description' => 'Unlock premium features and services by staking CCL tokens.'
                    ],
                    [
                        'title' => 'Rewards',
                        'description' => 'Earn rewards for contributing to the platform ecosystem.'
                    ]
                ],
                'roadmap_items' => [
                    [
                        'title' => 'Q1 2023',
                        'description' => 'Token Launch and Initial Exchange Listings'
                    ],
                    [
                        'title' => 'Q2 2023',
                        'description' => 'Platform Integration and Utility Implementation'
                    ],
                    [
                        'title' => 'Q3 2023',
                        'description' => 'Governance Features and Community Voting'
                    ],
                    [
                        'title' => 'Q4 2023',
                        'description' => 'Staking Rewards and Ecosystem Expansion'
                    ]
                ],
                'distribution_items' => [
                    [
                        'label' => 'Public Sale',
                        'percentage' => 40,
                        'color' => '#BE8400'
                    ],
                    [
                        'label' => 'Team',
                        'percentage' => 20,
                        'color' => '#31D7A9'
                    ],
                    [
                        'label' => 'Marketing',
                        'percentage' => 15,
                        'color' => '#8A2BE2'
                    ],
                    [
                        'label' => 'Development',
                        'percentage' => 10,
                        'color' => '#FF6B6B'
                    ],
                    [
                        'label' => 'Reserves',
                        'percentage' => 10,
                        'color' => '#4D4D4D'
                    ],
                    [
                        'label' => 'Airdrop',
                        'percentage' => 5,
                        'color' => '#1E90FF'
                    ]
                ],
                'is_enabled' => true,
                'buy_token_url' => null
            ]);
        }

        return $data;
    }
}
