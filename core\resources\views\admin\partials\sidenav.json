{"dashboard": {"keyword": ["Dashboard", "Home", "Panel", "Admin", "Control center", "Overview", "Main hub", "Management hub", "Administrative hub", "Central hub", "Command center", "Administrator portal", "Centralized interface", "Admin console", "Management dashboard", "Main screen", "Administrative dashboard", "Command dashboard", "Main control panel"], "title": "Dashboard", "icon": "las la-home", "route_name": "admin.dashboard", "menu_active": "admin.dashboard"}, "manage_ccl_token": {"keyword": ["Manage CCL Token", "CCL Token", "Token Management", "CCL", "<PERSON><PERSON>s", "Token Configuration"], "title": "Manage CCL Token", "icon": "las la-coins", "menu_active": ["admin.manage.ccl.token", "admin.presales*"], "submenu": [{"keyword": ["CCL Token", "Token Management", "<PERSON><PERSON>s"], "title": "CCL Token", "route_name": "admin.manage.ccl.token", "menu_active": "admin.manage.ccl.token"}, {"keyword": ["Manage Presale", "Presale Management", "Presale Settings"], "title": "Manage Presale", "route_name": "admin.presales.index", "menu_active": "admin.presales*"}, {"keyword": ["Presale Purchase", "Token Purchase", "CCL Token Purchase", "Purchase History"], "title": "Presale Purchase", "route_name": "admin.presales.purchases", "menu_active": "admin.presales.purchases"}, {"keyword": ["Completed Presale", "Completed Token Purchase", "Completed CCL Token Purchase", "Completed Purchase History"], "title": "Completed Presale", "route_name": "admin.presales.purchases.completed", "menu_active": "admin.presales.purchases.completed"}]}, "advertise": {"segment": "promotion", "keyword": ["advertise", "advertisements", "ad management", "ad positions", "banner ads", "ad placements"], "title": "Advertise", "icon": "las la-ad", "menu_active": "admin.advertise*", "submenu": [{"keyword": ["pending ads", "ad approval", "banner approval", "ad moderation"], "title": "Pending Ads", "route_name": "admin.advertise.pending", "menu_active": "admin.advertise.pending*"}, {"keyword": ["manage ads", "all ads", "ad management", "banner management", "advertisement management"], "title": "Manage Ads", "route_name": "admin.advertise.manage", "menu_active": "admin.advertise.manage*"}, {"keyword": ["manage ad positions", "ad positions", "ad placements", "advertisement locations"], "title": "Manage Ad Positions", "route_name": "admin.advertise.positions", "menu_active": "admin.advertise.positions*"}]}, "token_submissions": {"segment": "promotion", "keyword": ["token submissions", "user tokens", "submitted tokens", "coin listings", "token verification"], "title": "User <PERSON>s", "icon": "las la-coins", "menu_active": ["admin.tokens*", "admin.dexscreener*"], "submenu": [{"keyword": ["tokens", "coins", "crypto", "token list"], "title": "Tokens List", "route_name": "admin.dexscreener.index", "menu_active": ["admin.dexscreener.index", "admin.dexscreener.*"]}, {"keyword": ["submitted tokens", "token listings", "token verification", "pending tokens"], "title": "Submitted Tokens", "route_name": "admin.tokens.submitted", "menu_active": "admin.tokens.submitted"}, {"keyword": ["trending tokens", "token votes", "token ranking", "token trending"], "title": "Trending Tokens", "route_name": "admin.tokens.trending.index", "menu_active": "admin.tokens.trending.index"}]}, "manage_users": {"title": "Manage Users", "icon": "las la-users", "counters": ["bannedUsersCount", "emailUnverifiedUsersCount", "mobileUnverifiedUsersCount", "kycUnverifiedUsersCount", "kycPendingUsersCount"], "menu_active": "admin.users*", "submenu": [{"keyword": ["active users", "Manage Users", "User management", "User control", "User status", "User activity", "User analytics"], "title": "Active Users", "route_name": "admin.users.active", "menu_active": "admin.users.active"}, {"keyword": ["banned users", "Manage Users", "User management", "Account bans", "User activity"], "title": "Banned Users", "route_name": "admin.users.banned", "menu_active": "admin.users.banned", "counter": "bannedUsersCount"}, {"keyword": ["email unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "Email Unverified", "route_name": "admin.users.email.unverified", "menu_active": "admin.users.email.unverified", "counter": "emailUnverifiedUsersCount"}, {"keyword": ["mobile unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "Mobile Unverified", "route_name": "admin.users.mobile.unverified", "menu_active": "admin.users.mobile.unverified", "counter": "mobileUnverifiedUsersCount"}, {"keyword": ["kyc unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "KYC Unverified", "route_name": "admin.users.kyc.unverified", "menu_active": "admin.users.kyc.unverified", "counter": "kycUnverifiedUsersCount"}, {"keyword": ["kyc pending users", "Manage Users", "User verification", "User authentication", "User management"], "title": "KYC Pending", "route_name": "admin.users.kyc.pending", "menu_active": "admin.users.kyc.pending", "counter": "kycPendingUsersCount"}, {"keyword": ["with balance users", "Manage Users", "User management", "User activity", "Account management"], "title": "With Balance", "route_name": "admin.users.with.balance", "menu_active": "admin.users.with.balance"}, {"keyword": ["all users users", "Manage Users", "User management", "User control", "User activity", "User analytics"], "title": "All Users", "route_name": "admin.users.all", "menu_active": "admin.users.all"}, {"keyword": ["send notification users", "Manage Users", "User notifications", "User management", "User activity"], "title": "Send Notification", "route_name": "admin.users.notification.all", "menu_active": "admin.users.notification.all"}]}, "payments": {"segment": "Finance", "title": "Payments", "icon": "las la-file-invoice-dollar", "counters": ["pendingPaymentCount"], "menu_active": "admin.payment*", "submenu": [{"keyword": ["Pending Payments", "Payments", "Payment management", "Payment control", "Payment status", "Payment activity"], "title": "Pending Payments", "route_name": "admin.payment.pending", "menu_active": "admin.payment.pending", "counter": "pendingPaymentCount", "params": [{"user_id": ""}]}, {"keyword": ["Approved Payments", "Payments", "Payment management", "Payment activity"], "title": "Approved Payments", "route_name": "admin.payment.approved", "menu_active": "admin.payment.approved", "params": [{"user_id": ""}]}, {"keyword": ["Successful Payments", "Payments", "Payment management", "Payment activity"], "title": "Successful Payments", "route_name": "admin.payment.successful", "menu_active": "admin.payment.successful", "params": [{"user_id": ""}]}, {"keyword": ["Rejected Payments", "Payments", "Payment management", "Payment activity"], "title": "Rejected Payments", "route_name": "admin.payment.rejected", "menu_active": "admin.payment.rejected", "params": [{"user_id": ""}]}, {"keyword": ["Initiated Payments", "Payments", "Payment management", "Payment activity"], "title": "Initiated Payments", "route_name": "admin.payment.initiated", "menu_active": "admin.payment.initiated", "params": [{"user_id": ""}]}, {"keyword": ["All Payments", "Payments", "Payment management", "Payment control", "Payment activity"], "title": "All Payments", "route_name": "admin.payment.list", "menu_active": "admin.payment.list", "params": [{"user_id": ""}]}]}, "plans": {"segment": "Finance", "title": "Plans", "icon": "las la-shopping-cart", "menu_active": "admin.plans*", "keyword": ["Plans", "Plan management", "Pricing plans", "Subscription plans", "User plans", "Trend vote plans"], "submenu": [{"keyword": ["All Plans", "Plan list", "Plan management", "Manage plans"], "title": "All Plans", "route_name": "admin.plans.index", "menu_active": "admin.plans.index"}, {"keyword": ["Create Plan", "Add Plan", "New Plan", "Plan creation"], "title": "Create Plan", "route_name": "admin.plans.create", "menu_active": "admin.plans.create"}]}, "withdrawals": {"segment": "Finance", "title": "<PERSON><PERSON><PERSON><PERSON>", "icon": "la la-bank", "counters": ["pendingWithdrawCount"], "menu_active": "admin.withdraw*", "submenu": [{"keyword": ["Pending Withdrawals", "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal management", "Withdrawal control", "Withdrawal status", "Withdrawal activity"], "title": "Pending Withdrawals", "route_name": "admin.withdraw.data.pending", "menu_active": "admin.withdraw.data.pending", "counter": "pendingWithdrawCount", "params": [{"user_id": ""}]}, {"keyword": ["Approved Withdrawals", "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal management", "Withdrawal activity"], "title": "Approved Withdrawals", "route_name": "admin.withdraw.data.approved", "menu_active": "admin.withdraw.data.approved", "params": [{"user_id": ""}]}, {"keyword": ["Rejected <PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal management", "Withdrawal activity"], "title": "Rejected <PERSON>s", "route_name": "admin.withdraw.data.rejected", "menu_active": "admin.withdraw.data.rejected", "params": [{"user_id": ""}]}, {"keyword": ["All Withdrawals", "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal management", "Withdrawal control", "Withdrawal activity"], "title": "All Withdrawals", "route_name": "admin.withdraw.data.all", "menu_active": "admin.withdraw.data.all", "params": [{"user_id": ""}]}]}, "system_setting": {"segment": "settings", "keyword": ["System Setting", "setting", "System configuration", "System preferences", "Configuration management", "System setup"], "title": "System Setting", "icon": "las la-life-ring", "route_name": "admin.setting.system", "menu_active": ["admin.setting.system", "admin.setting.general", "admin.setting.socialite.credentials", "admin.cron*", "admin.setting.system.configuration", "admin.setting.logo.icon", "admin.extensions.index", "admin.language.manage", "admin.language.key", "admin.seo", "admin.kyc.setting", "admin.crypto.index", "admin.frontend.templates", "admin.frontend.manage.*", "admin.maintenance.mode", "admin.setting.cookie", "admin.setting.custom.css", "admin.setting.sitemap", "admin.setting.robot", "admin.setting.notification.global.email", "admin.setting.notification.global.sms", "admin.setting.notification.global.push", "admin.setting.notification.email", "admin.setting.notification.sms", "admin.setting.notification.push", "admin.setting.notification.templates", "admin.setting.notification.template.edit", "admin.frontend.index", "admin.frontend.sections*", "admin.gateway*", "admin.withdraw.data.method*", "admin.setting.app.purchase"]}, "reports": {"segment": "report", "title": "Report", "icon": "la la-list", "menu_active": "admin.report*", "submenu": [{"keyword": ["Transaction Log", "Report", "Transaction report", "Transaction history", "Transaction activity", "balance sheet", "balance log", "balance history"], "title": "Transaction History", "route_name": "admin.report.transaction", "menu_active": ["admin.report.transaction", "admin.report.transaction.search"], "params": [{"user_id": ""}]}, {"keyword": ["Login History", "Report", "Login report", "Login history", "Login activity"], "title": "Login History", "route_name": "admin.report.login.history", "menu_active": ["admin.report.login.history", "admin.report.login.ipHistory"]}, {"keyword": ["Notification History", "Report", "Notification report", "Notification history", "Notification activity", "email log", "email history", "sms log", "sms history", "push notification log", "push notification history"], "title": "Notification History", "route_name": "admin.report.notification.history", "menu_active": "admin.report.notification.history"}, {"keyword": ["Referral", "Bonus", "Referral Bonus", "Referral Log", "Referral Bonus Log"], "title": "Referral Log", "route_name": "admin.report.referral.bonus.log", "menu_active": "admin.report.referral.bonus.log"}]}, "support_ticket": {"segment": "support", "title": "Support Ticket", "icon": "la la-ticket", "counters": ["pendingTicketCount"], "menu_active": "admin.ticket*", "submenu": [{"keyword": ["Pending Ticket", "Support Ticket", "Ticket management", "Ticket control", "Ticket status", "Ticket activity"], "title": "Pending Ticket", "route_name": "admin.ticket.pending", "menu_active": "admin.ticket.pending", "counter": "pendingTicketCount"}, {"keyword": ["Closed Ticket", "Support Ticket", "Ticket management", "Ticket activity"], "title": "Closed Ticket", "route_name": "admin.ticket.closed", "menu_active": "admin.ticket.closed"}, {"keyword": ["Answered Ticket", "Support Ticket", "Ticket management", "Ticket activity"], "title": "Answered Ticket", "route_name": "admin.ticket.answered", "menu_active": "admin.ticket.answered"}, {"keyword": ["All Ticket", "Support Ticket", "Ticket management", "Ticket control", "Ticket activity"], "title": "All Ticket", "route_name": "admin.ticket.index", "menu_active": "admin.ticket.index"}]}, "extra": {"segment": "support", "title": "System Info", "icon": "la la-server", "menu_active": "admin.system*", "counters": [], "submenu": [{"keyword": ["Application", "System", "Application management", "Application settings", "System information", "version", "laravel", "timezone"], "title": "Application", "route_name": "admin.system.info", "menu_active": "admin.system.info"}, {"keyword": ["Server", "System", "Server management", "Server settings", "System information", "version", "php version", "software", "ip address", "server ip address", "server port", "http host"], "title": "Server", "route_name": "admin.system.server.info", "menu_active": "admin.system.server.info"}, {"keyword": ["<PERSON><PERSON>", "System", "Cache management", "Cache optimization", "System performance", "clear cache"], "title": "<PERSON><PERSON>", "route_name": "admin.system.optimize", "menu_active": "admin.system.optimize"}]}, "subscriber": {"segment": "promotion", "keyword": ["subscriber", "subscribers", "Subscription management", "Subscriber list", "Subscriber activity"], "title": "Subscribers", "icon": "las la-thumbs-up", "route_name": "admin.subscriber.index", "menu_active": "admin.subscriber.*"}}