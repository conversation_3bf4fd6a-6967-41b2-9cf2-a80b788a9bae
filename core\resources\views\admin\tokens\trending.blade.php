@extends('admin.layouts.app')
@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10">
                <div class="card-header d-flex justify-content-center">
                    <div class="col-md-6">
                        <form action="{{ route('admin.tokens.trending.index') }}" method="GET" class="d-flex">
                            <div class="input-group w-100">
                                <input type="text" name="search" class="form-control" placeholder="Search by token name or contract address" value="{{ $search ?? '' }}">
                                <button class="btn btn--primary input-group-text"><i class="la la-search"></i></button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive--md table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th>@lang('Rank')</th>
                                    <th>@lang('Token')</th>
                                    <th>@lang('Chain')</th>
                                    <th>@lang('User Votes')</th>
                                    <th>@lang('Admin Trend Votes')</th>
                                    <th>@lang('Total Votes')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($tokens as $index => $token)
                                <tr>
                                    <td>{{ $token->global_rank }}</td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div class="d-flex align-items-center">
                                                <img src="@if(isset($token->image_url) && !filter_var($token->image_url, FILTER_VALIDATE_URL) && strpos($token->image_url, 'http') !== 0){{ asset('assets/images/coin_logos/'.$token->image_url) }}@else{{ $token->image_url ?? asset('assets/images/default.png') }}@endif" alt="{{ $token->token_symbol }}" class="token-icon me-2" width="32" height="32">
                                                <div>
                                                    <span class="fw-bold">{{ $token->token_symbol }}</span>
                                                    <small class="d-block">{{ Str::limit($token->token_name, 30) }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ formatBlockchainName($token->chain_id) }}</td>
                                    <td>{{ $token->regular_votes }}</td>
                                    <td>{{ $token->admin_trend_votes }}</td>
                                    <td>{{ $token->vote_count }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary editBtn me-1"
                                            data-token_id="{{ $token->id }}"
                                            data-token_name="{{ $token->token_name }}"
                                            data-token_symbol="{{ $token->token_symbol }}"
                                            data-trend_votes="{{ $token->trend_votes ?? 0 }}"
                                        >
                                            <i class="las la-fire"></i> @lang('Vote')
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline--success promoteBtn"
                                            data-token_id="{{ $token->id }}"
                                            data-token_name="{{ $token->token_name }}"
                                            data-token_symbol="{{ $token->token_symbol }}"
                                            data-chain_id="{{ $token->chain_id }}"
                                            data-token_address="{{ $token->token_address }}"
                                        >
                                            <i class="la la-rocket"></i> @lang('Promote')
                                        </button>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage ?? 'No tokens found') }}</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if ($tokens->hasPages())
                <div class="card-footer py-4">
                    <div class="d-flex justify-content-center">
                        {{ paginateLinks($tokens) }}
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    {{-- VOTE MODAL --}}
    <div id="editModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('Add Token Trend Votes')</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="{{ route('admin.tokens.trending.update') }}" method="POST">
                    @csrf
                    <input type="hidden" name="token_id" id="token_id">
                    <div class="modal-body">
                        <div class="form-group">
                            <label>@lang('Token')</label>
                            <input type="text" class="form-control" id="token_name" readonly>
                        </div>
                        <div class="form-group">
                            <label>@lang('Admin Trend Votes')</label>
                            <input type="number" class="form-control" name="trend_votes" id="trend_votes" min="0" required>
                            <small class="text-muted">@lang('Set the number of admin trend votes for this token. These votes are added to user votes to determine the token\'s position in the trending list.')</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn--primary w-100">@lang('Save')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- PROMOTE MODAL --}}
    <div id="promoteModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('Promote Token')</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="{{ route('admin.tokens.trending.promote') }}" method="POST">
                    @csrf
                    <input type="hidden" name="token_id" id="promote_token_id">
                    <div class="modal-body">
                        <div class="form-group">
                            <label>@lang('Token')</label>
                            <input type="text" class="form-control" id="promote_token_name" readonly>
                        </div>
                        <div class="form-group">
                            <label>@lang('Chain')</label>
                            <input type="text" class="form-control" id="promote_chain_id" readonly>
                        </div>
                        <div class="form-group">
                            <label>@lang('Promotion Days')</label>
                            <input type="number" class="form-control" name="days" id="promote_days" min="1" max="100" value="1" required>
                            <small class="text-muted">@lang('Set the number of days to promote this token. The token will be moved to the Promoted Coins section for this duration.')</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn--success w-100">@lang('Promote Token')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
<div class="d-flex flex-wrap justify-content-end">
    <a href="{{ route('admin.dexscreener.index') }}" class="btn btn-sm btn-outline--primary me-2">
        <i class="las la-list"></i> @lang('Dexscreener Tokens')
    </a>
</div>
@endpush

@push('style')
<style>
    .token-icon {
        border-radius: 50%;
        object-fit: cover;
    }
</style>
@endpush

@push('script')
<script>
    (function($) {
        "use strict";

        $('.editBtn').on('click', function() {
            var modal = $('#editModal');
            modal.find('#token_id').val($(this).data('token_id'));
            modal.find('#token_name').val($(this).data('token_name') + ' (' + $(this).data('token_symbol') + ')');
            modal.find('#trend_votes').val($(this).data('trend_votes'));
            modal.modal('show');
        });

        $('.promoteBtn').on('click', function() {
            var modal = $('#promoteModal');
            modal.find('#promote_token_id').val($(this).data('token_id'));
            modal.find('#promote_token_name').val($(this).data('token_name') + ' (' + $(this).data('token_symbol') + ')');
            modal.find('#promote_chain_id').val($(this).data('chain_id'));
            modal.modal('show');
        });
    })(jQuery);
</script>
@endpush
