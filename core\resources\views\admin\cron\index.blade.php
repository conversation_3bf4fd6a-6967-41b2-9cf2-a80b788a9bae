@extends('admin.layouts.app')
@section('panel')

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive--sm table-responsive">
                        <table class="table table--light style--two bg-white">
                            <thead>
                                <tr>
                                    <th>@lang('Name')</th>
                                    <th>@lang('Schedule')</th>
                                    <th>@lang('Next Run')</th>
                                    <th>@lang('Last Run')</th>
                                    <th>@lang('Is Running')</th>
                                    <th>@lang('Type')</th>
                                    <th>@lang('Actions')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($crons as $cron)
                                    @php
                                        $dateTime = now()->parse($cron->next_run);
                                        $formattedDateTime = showDateTime($dateTime, 'Y-m-d\TH:i');
                                    @endphp
                                    <tr>
                                        <td>
                                            {{ __($cron->name) }} @if ($cron->logs->where('error', '!=', null)->count())
                                                <i class="fas fa-exclamation-triangle text--danger"></i>
                                            @endif <br>
                                            <code>{{ __($cron->alias) }}</code>
                                        </td>
                                        <td>
                                            @if ($cron->schedule)
                                                {{ __($cron->schedule->name) }}
                                            @else
                                                <span class="text-danger">@lang('No Schedule')</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($cron->next_run)
                                                {{ __($cron->next_run) }} @if ($cron->next_run > now())
                                                    <br> {{ diffForHumans($cron->next_run) }}
                                                @endif
                                            @else
                                                --
                                            @endif
                                        </td>
                                        <td>
                                            @if ($cron->last_run)
                                                {{ __($cron->last_run) }}
                                                <br> {{ diffForHumans($cron->last_run) }}
                                            @else
                                                --
                                            @endif
                                        </td>
                                        <td>
                                            @if ($cron->is_running)
                                                <span class="badge badge--success">@lang('Running')</span>
                                            @else
                                                <span class="badge badge--dark">@lang('Pause')</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($cron->is_default)
                                                <span class="badge badge--success">@lang('Default')</span>
                                            @else
                                                <span class="badge badge--primary">@lang('Customizable')</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline--primary" id="actionButton" data-bs-toggle="dropdown">
                                                    <i class="las la-ellipsis-v"></i>@lang('Action')
                                                </button>
                                                <div class="dropdown-menu p-0">
                                                    <a href="{{ route('admin.cron.run.now', $cron->id) }}" class="dropdown-item"><i class="las la-check-circle"></i> @lang('Run Now')</a>
                                                    @if ($cron->is_running)
                                                        <a href="{{ route('admin.cron.schedule.pause', $cron->id) }}" class="dropdown-item"><i class="las la-pause"></i> @lang('Pause')</a>
                                                    @else
                                                        <a href="{{ route('admin.cron.schedule.pause', $cron->id) }}" class="dropdown-item"><i class="las la-play"></i> @lang('Play')</a>
                                                    @endif
                                                    <a href="" data-id="{{ $cron->id }}" data-name="{{ $cron->name }}" data-url="{{ $cron->url }}" data-next_run="{{ $formattedDateTime }}" data-interval="{{ $cron->schedule ? $cron->schedule->interval : 3600 }}" data-default="{{ $cron->is_default }}" class="dropdown-item updateCron"><i class="las la-pen"></i> @lang('Edit')</a>
                                                    <a href="{{ route('admin.cron.schedule.logs', $cron->id) }}" class="dropdown-item"><i class="las la-history"></i> @lang('Logs')</a>
                                                    @if (!$cron->is_default)
                                                        <a href="javascript:void(0)" data-action="{{ route('admin.cron.delete', $cron->id) }}" data-question="@lang('Are you sure to delete this cron?')" class="dropdown-item confirmationBtn"><i class="las la-trash"></i> @lang('Delete')</a>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage) }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table><!-- table end -->
                    </div>
                </div>
            </div><!-- card end -->
        </div>
    </div>

    <x-confirmation-modal />

    <div class="modal fade" id="addCron" tabindex="-1" role="dialog" a aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">@lang('Add Cron Job')</h4>
                    <button type="button" class="close" data-bs-dismiss="modal"><i class="las la-times"></i></button>
                </div>
                <form class="form-horizontal disableSubmission resetForm" method="post" action="{{ route('admin.cron.store') }}">
                    @csrf

                    <div class="modal-body">
                        <div class="form-group">
                            <label>@lang('Name')</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Next Run')</label>
                            <input type="datetime-local" name="next_run" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Schedule') (@lang('seconds'))</label>
                            <input type="number" class="form-control" name="interval" min="60" value="3600" required>
                            <small class="text-muted">
                                @lang('Common intervals'): 
                                @lang('Hourly') = 3600, 
                                @lang('Daily') = 86400, 
                                @lang('Weekly') = 604800
                            </small>
                        </div>
                        <div class="form-group urlInputGroup">
                            <label>@lang('Url')</label>
                            <input type="text" name="url" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="is_crypto_fetch" id="is_crypto_fetch">
                                <label class="form-check-label" for="is_crypto_fetch">@lang('Crypto Fetch Job')</label>
                            </div>
                            <small class="text-muted">@lang('Check this to create a controller-based job for fetching crypto data that will work with auto scheduling')</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn--primary h-45 w-100">@lang('Submit')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade" id="updateCron" tabindex="-1" role="dialog" a aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">@lang('Edit Cron Job')</h4>
                    <button type="button" class="close" data-bs-dismiss="modal"><i class="las la-times"></i></button>
                </div>
                <form class="form-horizontal resetForm" method="post" action="{{ route('admin.cron.update') }}">
                    @csrf
                    <input type="hidden" name="id">
                    <div class="modal-body">
                        <div class="form-group">
                            <label>@lang('Name')</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Next Run')</label>
                            <input type="datetime-local" name="next_run" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>@lang('Schedule') (@lang('seconds'))</label>
                            <input type="number" class="form-control" name="interval" min="60" value="3600" required>
                            <small class="text-muted">
                                @lang('Common intervals'): 
                                @lang('Hourly') = 3600, 
                                @lang('Daily') = 86400, 
                                @lang('Weekly') = 604800
                            </small>
                        </div>
                        <div class="form-group urlGroup">
                            <label>@lang('Url')</label>
                            <input type="text" name="url" class="form-control">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn--primary h-45 w-100">@lang('Submit')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <button type="btn" class="btn btn-outline--primary addCron"><i class="las la-plus"></i>@lang('Add')</button>
    <a href="{{ route('admin.cron.schedule') }}" class="btn btn-outline--info"><i class="las la-clock"></i>@lang('Cron Schedule')</a>
    <a href="{{ route('admin.cron.fix.schedules') }}" class="btn btn-outline--warning"><i class="las la-tools"></i>@lang('Fix Schedules')</a>
@endpush

@push('script')
    <script>
        (function($) {
            "use strict";

            $('.addCron').on('click', function() {
                let modal = $('#addCron');
                $('.resetForm').trigger('reset');
                modal.modal('show');
                
                // Initialize the crypto fetch toggle
                $('#is_crypto_fetch').prop('checked', false);
                $('.urlInputGroup').show();
                $('[name=url]').attr('required', true);
            });

            // Handle crypto fetch checkbox toggle
            $('#is_crypto_fetch').on('change', function() {
                if ($(this).is(':checked')) {
                    $('.urlInputGroup').hide();
                    $('[name=url]').attr('required', false);
                    // Pre-fill name field
                    $('[name=name]').val('Fetch Crypto Data');
                } else {
                    $('.urlInputGroup').show();
                    $('[name=url]').attr('required', true);
                }
            });

            $('.updateCron').on('click', function(e) {
                e.preventDefault();
                var modal = $('#updateCron');
                let id = $(this).data('id');
                let name = $(this).data('name');
                let next_run = $(this).data('next_run');
                let interval = $(this).data('interval');
                let isDefault = $(this).data('default');
                if (isDefault) {
                    modal.find('[name=url]').attr('required', false);
                    $('.urlGroup').hide();
                } else {
                    modal.find('[name=url]').parent().find('label').addClass('required');
                    modal.find('[name=url]').attr('required', true);
                    modal.find('[name=url]').val($(this).data('url'));
                    $('.urlGroup').show();
                }
                modal.find('input[name=id]').val(id);
                modal.find('input[name=name]').val(name);
                modal.find('input[name=next_run]').val(next_run);
                modal.find('input[name=interval]').val(interval);
                modal.modal('show');
            });

        })(jQuery);
    </script>
@endpush

@push('style')
    <style>
        .dropdown-toggle::after {
            display: inline-block;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }
    </style>
@endpush
