@extends($activeTemplate . 'layouts.frontend')

@php
    use App\Models\TokenVote;

    // Get vote counts
    $userVotes = TokenVote::getVoteCountByAddress($token->chain_id, $token->token_address);
    $adminTrendVotes = $token->trend_votes ?? 0;
    $totalVotes = $userVotes + $adminTrendVotes;

    // Get negative vote count
    $negativeVotes = TokenVote::getNegativeVoteCountByAddress($token->chain_id, $token->token_address);

    // Check if user has already voted today
    $hasVoted = auth()->check() ? TokenVote::where('chain_id', $token->chain_id)
        ->where('token_address', $token->token_address)
        ->where('ip_address', request()->ip())
        ->where('voted_at', now()->format('Y-m-d'))
        ->where('used_trend_vote', false)
        ->where('is_negative', false)
        ->exists() : false;

    // Check if user has already voted negatively today
    $hasNegativeVoted = auth()->check() ? TokenVote::where('chain_id', $token->chain_id)
        ->where('token_address', $token->token_address)
        ->where(function($query) {
            $ipAddress = request()->ip();
            $query->where('ip_address', $ipAddress)
                  ->orWhere('ip_address', $ipAddress . '_neg');
        })
        ->where('voted_at', now()->format('Y-m-d'))
        ->where('used_trend_vote', false)
        ->where('is_negative', true)
        ->exists() : false;

    // Get user's trend votes count
    $trendVotes = auth()->check() ? auth()->user()->trend_votes : 0;
@endphp

@section('content')
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg--base-two">
                <div class="card-body text--white">
                    <div class="d-flex align-items-center mb-3">
                        <div class="token-icon-wrapper me-3">
                            <img src="@if(isset($token->image_url) && !filter_var($token->image_url, FILTER_VALIDATE_URL) && strpos($token->image_url, 'http') !== 0){{ asset('assets/images/coin_logos/'.$token->image_url) }}@else{{ $token->image_url ?? asset('assets/images/default.png') }}@endif" alt="{{ $token->token_symbol }}" class="token-icon" width="64" height="64">
                        </div>
                        <div class="token-info">
                            <h2 class="mb-0 text-white">{{ $token->token_name }} ({{ $token->token_symbol }}) <span class="token-rank">#{{ $globalRank ?? 'N/A' }}</span></h2>
                            <div class="d-flex align-items-center flex-wrap social-links-container">
                                <span class="badge chain-badge me-2 mb-1">{{ formatBlockchainName($token->chain_id) }}</span>
                                <button class="btn btn-sm btn-outline-warning copy-btn mb-1" data-clipboard-text="{{ $token->token_address }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Copy">
                                    <i class="las la-copy"></i> {{ substr($token->token_address, 0, 8) }}...{{ substr($token->token_address, -6) }}
                                </button>
                                @if($socialLinks)
                                    @if($socialLinks->website)
                                    <a href="{{ $socialLinks->website }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Website">
                                        <i class="las la-globe" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                    @if($socialLinks->twitter)
                                    <a href="{{ $socialLinks->twitter }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Twitter">
                                        <i class="lab la-twitter" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                    @if($socialLinks->telegram)
                                    <a href="{{ $socialLinks->telegram }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Telegram">
                                        <i class="lab la-telegram" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                    @if($socialLinks->discord)
                                    <a href="{{ $socialLinks->discord }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Discord">
                                        <i class="lab la-discord" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                    @if($socialLinks->facebook)
                                    <a href="{{ $socialLinks->facebook }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Facebook">
                                        <i class="lab la-facebook-f" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                    @if($socialLinks->reddit)
                                    <a href="{{ $socialLinks->reddit }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Reddit">
                                        <i class="lab la-reddit" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                    @if($socialLinks->linktree)
                                    <a href="{{ $socialLinks->linktree }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Linktree">
                                        <i class="las la-link" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                    @if($socialLinks->presale_url && (isset($token->is_presale_token) && $token->is_presale_token) && (!isset($token->presale_countdown) || $token->presale_countdown !== 'Ended'))
                                    <a href="{{ $socialLinks->presale_url }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Presale">
                                        <i class="las la-hourglass-start" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @elseif($socialLinks->presale_url && (isset($token->is_fair_launch_token) && $token->is_fair_launch_token) && (!isset($token->presale_countdown) || $token->presale_countdown !== 'Ended'))
                                    <a href="{{ $socialLinks->presale_url }}" target="_blank" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Fair Launch">
                                        <i class="las la-hourglass-start" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                    @if($socialLinks->whitepaper)
                                    <a href="{{ $socialLinks->whitepaper }}" target="{{ Str::endsWith(strtolower($socialLinks->whitepaper), ['.pdf', '.doc', '.docx', '.txt']) ? '_blank' : '_blank' }}" class="ms-2 social-icon-box mb-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Whitepaper">
                                        <i class="las la-file-alt" style="color: #BE8400; font-size: 24px;"></i>
                                    </a>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row token-details-main-row">
        <div class="col-12 col-lg-8">
            @if(isset($token->is_fair_launch_token) && $token->is_fair_launch_token)
            <div class="card mb-4 bg--base-two">
                <div class="card-header bg--dark">
                    <h5 class="card-title mb-0 text-white">Fair Launch Token</h5>
                </div>
                <div class="card-body text--white text-center">
                    <div class="presale-message">
                        <i class="las la-hourglass-start presale-icon"></i>
                        <p>This token is a fair launch. Price chart will be available after launch.</p>
                        @if($submitCoin && $submitCoin->presale_start_date)
                        <div class="presale-date-info">
                            <p>
                                @php
                                    try {
                                        $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', $submitCoin->presale_start_date);
                                        $now = \Carbon\Carbon::now();
                                        if ($now->gt($startDate)) {
                                            echo 'Fair Launch started: ' . $submitCoin->presale_start_date;
                                        } else {
                                            echo 'Fair Launch starts: ' . $submitCoin->presale_start_date;
                                        }
                                    } catch (\Exception $e) {
                                        echo 'Fair Launch starts: ' . $submitCoin->presale_start_date;
                                    }
                                @endphp
                            </p>
                            @if($submitCoin->presale_end_date)
                            <p>Fair Launch ends: {{ $submitCoin->presale_end_date }}</p>
                            @endif
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @elseif(isset($token->is_presale_token) && $token->is_presale_token)
            <div class="card mb-4 bg--base-two">
                <div class="card-header bg--dark">
                    <h5 class="card-title mb-0 text-white">Presale Token</h5>
                </div>
                <div class="card-body text--white text-center">
                    <div class="presale-message">
                        <i class="las la-hourglass-start presale-icon"></i>
                        <p>This token is in presale. Price chart will be available after launch.</p>
                        @if($submitCoin && $submitCoin->presale_start_date)
                        <div class="presale-date-info">
                            <p>
                                @php
                                    try {
                                        $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', $submitCoin->presale_start_date);
                                        $now = \Carbon\Carbon::now();
                                        if ($now->gt($startDate)) {
                                            echo 'Presale started: ' . $submitCoin->presale_start_date;
                                        } else {
                                            echo 'Presale starts: ' . $submitCoin->presale_start_date;
                                        }
                                    } catch (\Exception $e) {
                                        echo 'Presale starts: ' . $submitCoin->presale_start_date;
                                    }
                                @endphp
                            </p>
                            @if($submitCoin->presale_end_date)
                            <p>Presale ends: {{ $submitCoin->presale_end_date }}</p>
                            @endif
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @else
            <div class="card mb-4 bg--base-two">
                <div class="card-header bg--dark">
                    <h5 class="card-title mb-0 text-white">Live Price Chart</h5>
                </div>
                <div class="card-body p-0">
                    <!-- DexScreener Chart Embed BEGIN -->
                    <div class="dexscreener-chart-container">
                        <iframe
                            src="https://dexscreener.com/{{ strtolower($token->chain_id) }}/{{ $token->token_address }}?embed=1&theme=dark&trades=0&info=0"
                            style="width: 100%; height: 100%; border: none; border-radius: 0 0 8px 8px;"
                            frameborder="0"
                            allowtransparency="true"
                            scrolling="no">
                        </iframe>
                    </div>
                    <!-- DexScreener Chart Embed END -->
                </div>
            </div>
            @endif

            @if($submitCoin)
            <div class="card mb-4 bg--base-two">
                <div class="card-header bg--dark">
                    <h5 class="card-title mb-0 text-white">Token Information</h5>
                </div>
                <div class="card-body text--white">
                    @if($submitCoin->description)
                    <div class="token-description">{!! nl2br(e($submitCoin->description)) !!}</div>
                    @else
                    <div class="token-description">No description available.</div>
                    @endif

                    @if($submitCoin->video_url)
                    <div class="token-video-container mt-4">
                        @php
                            $videoUrl = $submitCoin->video_url;
                            $videoEmbedUrl = '';
                            $videoType = '';

                            // YouTube URL conversion
                            if (strpos($videoUrl, 'youtube.com/watch?v=') !== false) {
                                $videoId = explode('v=', $videoUrl)[1];
                                // Remove any additional parameters
                                if (strpos($videoId, '&') !== false) {
                                    $videoId = substr($videoId, 0, strpos($videoId, '&'));
                                }
                                $videoEmbedUrl = "https://www.youtube.com/embed/" . $videoId;
                                $videoType = 'youtube';
                            }
                            // YouTube short URL
                            elseif (strpos($videoUrl, 'youtu.be/') !== false) {
                                $videoId = explode('youtu.be/', $videoUrl)[1];
                                // Remove any additional parameters
                                if (strpos($videoId, '?') !== false) {
                                    $videoId = substr($videoId, 0, strpos($videoId, '?'));
                                }
                                $videoEmbedUrl = "https://www.youtube.com/embed/" . $videoId;
                                $videoType = 'youtube';
                            }
                            // Vimeo URL conversion
                            elseif (strpos($videoUrl, 'vimeo.com/') !== false) {
                                $videoId = explode('vimeo.com/', $videoUrl)[1];
                                // Remove any additional parameters
                                if (strpos($videoId, '?') !== false) {
                                    $videoId = substr($videoId, 0, strpos($videoId, '?'));
                                }
                                $videoEmbedUrl = "https://player.vimeo.com/video/" . $videoId;
                                $videoType = 'vimeo';
                            }
                            // Check for common video file extensions
                            elseif (preg_match('/\.(mp4|webm|ogg|mov|avi|wmv|flv|mkv)(\?.*)?$/i', $videoUrl)) {
                                $videoType = 'direct';
                                // Keep videoEmbedUrl empty to use the native player
                            }
                            // For other video URLs, we'll still try to play them directly
                            else {
                                $videoType = 'unknown';
                                // Keep videoEmbedUrl empty to use the native player
                            }
                        @endphp

                        @if($videoEmbedUrl)
                            <div class="responsive-video-container">
                                <iframe
                                    src="{{ $videoEmbedUrl }}"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen>
                                </iframe>
                            </div>
                        @else
                            <div class="responsive-video-container">
                                <video
                                    controls
                                    class="native-video-player"
                                    preload="metadata"
                                    poster="">
                                    <source src="{{ $videoUrl }}" type="video/mp4">
                                    <source src="{{ $videoUrl }}" type="video/webm">
                                    <source src="{{ $videoUrl }}" type="video/ogg">
                                    Your browser does not support the video tag. <a href="{{ $videoUrl }}" target="_blank">Click here to watch the video</a>.
                                </video>
                            </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <div class="col-12 col-lg-4">
            <div class="card mb-4 bg--base-two">
                <div class="card-header bg--dark">
                    <h5 class="card-title mb-0 text-white">{{ $token->token_symbol }}
                        @if(isset($token->is_presale_token) && $token->is_presale_token)
                            Presale
                        @elseif(isset($token->is_fair_launch_token) && $token->is_fair_launch_token)
                            Fair Launch
                        @else
                            Price USD
                        @endif
                    </h5>
                </div>
                <div class="card-body text--white">
                    <div class="price-big text-center mb-3">
                        @if(isset($token->is_presale_token) && $token->is_presale_token)
                            <div class="presale-info">
                                <span class="presale-badge">{{ $token->presale_countdown ?? 'Coming Soon' }}</span>
                                <div class="presale-date mt-2">{{ $token->presale_start_date_formatted ?? 'TBA' }}</div>
                            </div>
                        @elseif(isset($token->is_fair_launch_token) && $token->is_fair_launch_token)
                            <div class="presale-info">
                                <span class="presale-badge">{{ $token->presale_countdown ?? 'Coming Soon' }}</span>
                                <div class="presale-date mt-2">{{ $token->presale_start_date_formatted ?? 'TBA' }}</div>
                            </div>
                        @else
                            ${{ $token->price_usd }}
                        @endif
                    </div>

                    <div class="timeframes">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="timeframe-box">
                                    <div class="timeframe-label">5m</div>
                                    @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                        <div class="timeframe-value presale-na">N/A</div>
                                    @else
                                        <div class="timeframe-value {{ floatval($token->price_change_5m) >= 0 ? 'text-success' : 'text-danger' }}">
                                            {{ floatval($token->price_change_5m) >= 0 ? '+' : '' }}{{ floatval($token->price_change_5m) }}%
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="timeframe-box">
                                    <div class="timeframe-label">1h</div>
                                    @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                        <div class="timeframe-value presale-na">N/A</div>
                                    @else
                                        <div class="timeframe-value {{ floatval($token->price_change_1h) >= 0 ? 'text-success' : 'text-danger' }}">
                                            {{ floatval($token->price_change_1h) >= 0 ? '+' : '' }}{{ floatval($token->price_change_1h) }}%
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="timeframe-box">
                                    <div class="timeframe-label">6h</div>
                                    @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                        <div class="timeframe-value presale-na">N/A</div>
                                    @else
                                        <div class="timeframe-value {{ floatval($token->price_change_6h) >= 0 ? 'text-success' : 'text-danger' }}">
                                            {{ floatval($token->price_change_6h) >= 0 ? '+' : '' }}{{ floatval($token->price_change_6h) }}%
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="timeframe-box">
                                    <div class="timeframe-label">24h</div>
                                    @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                        <div class="timeframe-value presale-na">N/A</div>
                                    @else
                                        <div class="timeframe-value {{ floatval($token->price_change_24h) >= 0 ? 'text-success' : 'text-danger' }}">
                                            {{ floatval($token->price_change_24h) >= 0 ? '+' : '' }}{{ floatval($token->price_change_24h) }}%
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-4">
                    <div class="icon-box mb-4 promote-box">
                        <div class="stat-icon token-action-icon promote-icon" data-token-address="{{ $token->token_address }}" data-chain-id="{{ $token->chain_id }}" data-token-symbol="{{ $token->token_symbol }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Promote">⚡</div>
                        <div class="stat-value token-action-icon promote-icon" data-token-address="{{ $token->token_address }}" data-chain-id="{{ $token->chain_id }}" data-token-symbol="{{ $token->token_symbol }}">Promote</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="icon-box mb-4 vote-box">
                        <div class="stat-icon token-action-icon vote-icon" data-token-address="{{ $token->token_address }}" data-chain-id="{{ $token->chain_id }}" data-token-symbol="{{ $token->token_symbol }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Vote">🔥</div>
                        <div class="stat-value">{{ $totalVotes }}</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="icon-box mb-4 flag-box">
                        <div class="stat-icon token-action-icon flag-icon" data-token-address="{{ $token->token_address }}" data-chain-id="{{ $token->chain_id }}" data-token-symbol="{{ $token->token_symbol }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Dislike">🚩</div>
                        <div class="stat-value">{{ $negativeVotes }}</div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-3">
                    <div class="action-box mb-4 text-center">
                        <div class="stat-label">TXN</div>
                        <div class="stat-value">
                            @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                <span class="presale-na">N/A</span>
                            @else
                                {{ number_format($token->txn_24h ?? 0) }}
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="action-box mb-4 text-center">
                        <div class="stat-label">Buy</div>
                        <div class="stat-value">
                            @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                <span class="presale-na">N/A</span>
                            @else
                                {{ number_format($token->buy_count ?? 0) }}
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="action-box mb-4 text-center">
                        <div class="stat-label">Sell</div>
                        <div class="stat-value">
                            @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                <span class="presale-na">N/A</span>
                            @else
                                {{ number_format($token->sell_count ?? 0) }}
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="action-box mb-4 text-center">
                        <div class="stat-label">Volume</div>
                        <div class="stat-value">
                            @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                <span class="presale-na">N/A</span>
                            @else
                                ${{ formatAmount($token->volume_24h ?? 0) }}
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-6">
                    <div class="info-box mb-4">
                        <div class="d-flex justify-content-between">
                            @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                <div class="stat-label">Softcap</div>
                                <div class="stat-value">
                                    @php
                                        $softcap = $submitCoin && $submitCoin->softcap ? formatCapValue($submitCoin->softcap) : '-';
                                    @endphp
                                    {{ $softcap }}
                                </div>
                            @else
                                <div class="stat-label">Buy/Sell fee</div>
                                <div class="stat-value">0% / 0%</div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="info-box mb-4">
                        <div class="d-flex justify-content-between">
                            @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                <div class="stat-label">Hardcap</div>
                                <div class="stat-value">
                                    @php
                                        $hardcap = $submitCoin && $submitCoin->hardcap ? formatCapValue($submitCoin->hardcap) : '-';
                                    @endphp
                                    {{ $hardcap }}
                                </div>
                            @else
                                <div class="stat-label">Liquidity</div>
                                <div class="stat-value">
                                    ${{ formatAmount($token->liquidity_usd ?? 0) }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-6">
                    <div class="info-box mb-4">
                        <div class="d-flex justify-content-between">
                            <div class="stat-label">Total supply</div>
                            <div class="stat-value">
                                @php
                                    $metadata = null;
                                    if (is_object($token->metadata)) {
                                        $metadata = $token->metadata;
                                    } elseif (is_string($token->metadata)) {
                                        $metadata = json_decode($token->metadata ?? '{}');
                                    }
                                @endphp
                                {{ $metadata && isset($metadata->totalSupply) ? formatAmount($metadata->totalSupply) : 'N/A' }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="info-box mb-4">
                        <div class="market-cap-wrapper">
                            <span class="stat-label">Mcap</span>
                            <span class="stat-value">
                                @if((isset($token->is_presale_token) && $token->is_presale_token) || (isset($token->is_fair_launch_token) && $token->is_fair_launch_token))
                                    <span class="presale-na">N/A</span>
                                @else
                                    ${{ formatAmount($token->market_cap ?? 0) }}
                                @endif
                            </span>
                        </div>
                    </div>
                </div>
            </div>



            @if($allTimeHigh)
            <div class="card mb-4 bg--base-two">
                <div class="card-header bg--dark">
                    <h5 class="card-title mb-0 text-white">All Time High</h5>
                </div>
                <div class="card-body text--white">
                    <div class="mb-2">
                        <span class="stat-label">Price:</span>
                        <span class="stat-value">${{ formatPrice($allTimeHigh['price'], 2, 8) }}</span>
                        <span class="text-danger ms-2">{{ number_format($allTimeHigh['change'], 2) }}%</span>
                    </div>
                    <div>
                        <span class="stat-label">Date:</span>
                        <span class="stat-value">{{ $allTimeHigh['timestamp'] }}</span>
                    </div>
                </div>
            </div>
            @endif

            @if($allTimeHigh)
            <div class="card mb-4 bg--base-two">
                <div class="card-header bg--dark">
                    <h5 class="card-title mb-0 text-white">All Time High LP</h5>
                </div>
                <div class="card-body text--white">
                    <div class="mb-2">
                        <span class="stat-value">${{ formatAmount(284845) }}</span>
                        <span class="text-danger ms-2">-92.26%</span>
                    </div>
                    <div>
                        <span class="stat-label">Date:</span>
                        <span class="stat-value">Mar 26, 2025, 6:30 AM</span>
                    </div>
                </div>
            </div>
            @endif

            <!-- Ad between token data and buttons -->
            <div class="token-details-ad mb-4">
                <x-ad position="token_details" />
            </div>

            <div class="card action-buttons-container mb-4 bg--base-two">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if(isset($token->is_presale_token) && $token->is_presale_token && $socialLinks && $socialLinks->presale_url && (!isset($token->presale_countdown) || $token->presale_countdown !== 'Ended'))
                        {{-- Age: Presale element hidden as requested --}}
                        {{-- <div class="presale-age-container mb-3">
                            <div class="presale-age-label">Age:</div>
                            <div class="presale-age-badge">Presale</div>
                        </div> --}}
                        <a href="{{ $socialLinks->presale_url }}" class="btn btn-presale btn-block" target="_blank">
                            <i class="las la-hourglass-start"></i> Join Presale
                        </a>
                        @elseif(isset($token->is_fair_launch_token) && $token->is_fair_launch_token && $socialLinks && $socialLinks->presale_url && (!isset($token->presale_countdown) || $token->presale_countdown !== 'Ended'))
                        <a href="{{ $socialLinks->presale_url }}" class="btn btn-presale btn-block" target="_blank">
                            <i class="las la-hourglass-start"></i> Join Fair Launch
                        </a>
                        @endif



                        @php
                            $explorerUrl = getBlockExplorerUrl($token->chain_id, $token->token_address);
                            $isPresaleTokenAddress = strpos($token->token_address, 'presale_') === 0;
                            $isFairLaunchTokenAddress = strpos($token->token_address, 'fairlaunch_') === 0;
                        @endphp
                        @if($isPresaleTokenAddress || $isFairLaunchTokenAddress)
                            <button type="button" class="btn btn-token btn-block" data-bs-toggle="modal" data-bs-target="#checkContractErrorModal">
                                <i class="las la-search"></i> Check Contract
                            </button>
                        @elseif($explorerUrl)
                            @if(strpos($explorerUrl, '{CONTRACT_ADDRESS}') !== false)
                            <a href="{{ str_replace('{CONTRACT_ADDRESS}', $token->token_address, $explorerUrl) }}" target="_blank" class="btn btn-token btn-block">
                                <i class="las la-search"></i> Check Contract
                            </a>
                            @elseif($token->chain_id == 'solana')
                            <a href="{{ $explorerUrl }}/token/{{ $token->token_address }}" target="_blank" class="btn btn-token btn-block">
                                <i class="las la-search"></i> Check Contract
                            </a>
                            @elseif($token->chain_id == 'tron')
                            <a href="{{ $explorerUrl }}/{{ $token->token_address }}?type=tokens&contract" target="_blank" class="btn btn-token btn-block">
                                <i class="las la-search"></i> Check Contract
                            </a>
                            @else
                            <a href="{{ $explorerUrl }}/address/{{ $token->token_address }}" target="_blank" class="btn btn-token btn-block">
                                <i class="las la-search"></i> Check Contract
                            </a>
                            @endif
                        @else
                            <button type="button" class="btn btn-token btn-block" data-bs-toggle="modal" data-bs-target="#noExplorerModal">
                                <i class="las la-search"></i> Check Contract
                            </button>
                        @endif

                        @php
                            $supportedChains = [
                                '1', 'ethereum', 'eth',           // Ethereum
                                '56', 'bsc', 'bnb',               // BSC
                                '42161', 'arbitrum', 'arb',       // Arbitrum
                                '137', 'polygon', 'matic',        // Polygon
                                '324', 'zksync', 'zksyncera',     // zkSync Era
                                '59144', 'linea',                 // Linea
                                '8453', 'base',                   // Base
                                '534352', 'scroll',               // Scroll
                                '10', 'optimism', 'op',           // Optimism
                                '43114', 'avalanche', 'avax',     // Avalanche
                                '250', 'fantom', 'ftm',           // Fantom
                                '25', 'cronos', 'cro',            // Cronos
                                '66', 'okc',                      // OKC
                                '128', 'heco',                    // HECO
                                '100', 'gnosis', 'xdai',          // Gnosis
                                '10001', 'ethw',                  // ETHW
                                'tron', 'trx',                    // Tron
                                '321', 'kcc',                     // KCC
                                '201022', 'fon',                  // FON
                                '5000', 'mantle',                 // Mantle
                                '204', 'opbnb',                   // opBNB
                                '42766', 'zkfair',                // ZKFair
                                '81457', 'blast',                 // Blast
                                '169', 'manta', 'mantapacific',   // Manta Pacific
                                '80094', 'berachain', 'bera',     // Berachain
                                '2741', 'abstract',               // Abstract
                                '177', 'hashkey', 'hashkeychain', // Hashkey Chain
                                '146', 'sonic',                   // Sonic
                                '1514', 'story'                   // Story
                            ];
                            $isChainSupported = in_array(strtolower($token->chain_id), array_map('strtolower', $supportedChains));
                        @endphp
                        @if($isChainSupported)
                        <button type="button" class="btn btn-token btn-block scan-contract-btn" data-chain-id="{{ $token->chain_id }}" data-token-address="{{ $token->token_address }}">
                            <i class="las la-shield-alt"></i> Scan Contract
                        </button>
                        @endif
                    </div>
                </div>
            </div>

            @php
                $hasSocialLinks = false;
                $hasDisplayableSocialLinks = false;
                if ($socialLinks) {
                    // Check if any social links exist
                    $hasSocialLinks = $socialLinks->website || $socialLinks->presale_url || $socialLinks->twitter ||
                                     $socialLinks->telegram || $socialLinks->facebook || $socialLinks->discord ||
                                     $socialLinks->reddit || $socialLinks->linktree || $socialLinks->whitepaper;

                    // Check if any social links will actually be displayed
                    $hasDisplayableSocialLinks =
                        $socialLinks->website ||
                        ($socialLinks->presale_url &&
                            (
                                ((isset($token->is_presale_token) && $token->is_presale_token) && (!isset($token->presale_countdown) || $token->presale_countdown !== 'Ended')) ||
                                ((isset($token->is_fair_launch_token) && $token->is_fair_launch_token) && (!isset($token->presale_countdown) || $token->presale_countdown !== 'Ended'))
                            )
                        ) ||
                        $socialLinks->twitter || $socialLinks->telegram ||
                        $socialLinks->facebook || $socialLinks->discord ||
                        $socialLinks->reddit || $socialLinks->linktree ||
                        $socialLinks->whitepaper;
                }
            @endphp

            @if($hasDisplayableSocialLinks)
            <div class="card mb-4 bg--base-two">
                <div class="card-body">
                    <div class="row">
                        @if($socialLinks && $socialLinks->website)
                        <div class="col-6">
                            <a href="{{ $socialLinks->website }}" class="btn btn-token btn-block" target="_blank">
                                <i class="las la-globe"></i> Website
                            </a>
                        </div>
                        @endif
                        @if($socialLinks && $socialLinks->presale_url && (isset($token->is_presale_token) && $token->is_presale_token) && (!isset($token->presale_countdown) || $token->presale_countdown !== 'Ended'))
                        <div class="col-6">
                            <a href="{{ $socialLinks->presale_url }}" class="btn btn-token btn-block" target="_blank">
                                <i class="las la-link"></i> Presale Link
                            </a>
                        </div>
                        @elseif($socialLinks && $socialLinks->presale_url && (isset($token->is_fair_launch_token) && $token->is_fair_launch_token) && (!isset($token->presale_countdown) || $token->presale_countdown !== 'Ended'))
                        <div class="col-6">
                            <a href="{{ $socialLinks->presale_url }}" class="btn btn-token btn-block" target="_blank">
                                <i class="las la-link"></i> Fair Launch Link
                            </a>
                        </div>
                        @endif
                    </div>

                    @if(($socialLinks && $socialLinks->twitter) || ($socialLinks && $socialLinks->telegram))
                    <div class="row mt-3">
                        @if($socialLinks && $socialLinks->twitter)
                        <div class="col-6">
                            <a href="{{ $socialLinks->twitter }}" class="btn btn-token btn-block" target="_blank">
                                <i class="lab la-twitter"></i> Twitter
                            </a>
                        </div>
                        @endif
                        @if($socialLinks && $socialLinks->telegram)
                        <div class="col-6">
                            <a href="{{ $socialLinks->telegram }}" class="btn btn-token btn-block" target="_blank">
                                <i class="lab la-telegram"></i> Telegram
                            </a>
                        </div>
                        @endif
                    </div>
                    @endif

                    @if(($socialLinks && $socialLinks->facebook) || ($socialLinks && $socialLinks->discord))
                    <div class="row mt-3">
                        @if($socialLinks && $socialLinks->facebook)
                        <div class="col-6">
                            <a href="{{ $socialLinks->facebook }}" class="btn btn-token btn-block" target="_blank">
                                <i class="lab la-facebook-f"></i> Facebook
                            </a>
                        </div>
                        @endif
                        @if($socialLinks && $socialLinks->discord)
                        <div class="col-6">
                            <a href="{{ $socialLinks->discord }}" class="btn btn-token btn-block" target="_blank">
                                <i class="lab la-discord"></i> Discord
                            </a>
                        </div>
                        @endif
                    </div>
                    @endif

                    @if(($socialLinks && $socialLinks->reddit) || ($socialLinks && $socialLinks->linktree))
                    <div class="row mt-3">
                        @if($socialLinks && $socialLinks->reddit)
                        <div class="col-6">
                            <a href="{{ $socialLinks->reddit }}" class="btn btn-token btn-block" target="_blank">
                                <i class="lab la-reddit"></i> Reddit
                            </a>
                        </div>
                        @endif
                        @if($socialLinks && $socialLinks->linktree)
                        <div class="col-6">
                            <a href="{{ $socialLinks->linktree }}" class="btn btn-token btn-block" target="_blank">
                                <i class="las la-link"></i> Linktree.ee
                            </a>
                        </div>
                        @endif
                    </div>
                    @endif

                    @if($socialLinks && $socialLinks->whitepaper)
                    <div class="row mt-3">
                        <div class="col-6">
                            <a href="{{ $socialLinks->whitepaper }}" class="btn btn-token btn-block" target="{{ Str::endsWith(strtolower($socialLinks->whitepaper), ['.pdf', '.doc', '.docx', '.txt']) ? '_blank' : '_blank' }}">
                                <i class="las la-file-alt"></i> Whitepaper
                            </a>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Copy Address Modal -->
<div class="modal custom--modal fade" id="copyAddressModal" tabindex="-1" aria-labelledby="copyAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff;">
                <h5 class="modal-title" id="copyAddressModalLabel">@lang('Address Copied')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>@lang('Token address copied to clipboard!')</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
            </div>
        </div>
    </div>
</div>

@auth
<!-- Promote Token Modal -->
<div class="modal custom--modal fade" id="promoteTokenModal" tabindex="-1" aria-labelledby="promoteTokenModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promoteTokenModalLabel">@lang('Promote Token')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('user.token.promote') }}" method="POST">
                    @csrf
                    <input type="hidden" name="chain_id" value="{{ $token->chain_id }}">
                    <input type="hidden" name="token_address" value="{{ $token->token_address }}">
                    <div class="form-group">
                        <label>@lang('Token')</label>
                        <div class="input-group">
                            <span class="form-control">{{ $token->token_name }} ({{ $token->token_symbol }})</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Current Promote Credits')</label>
                        <div class="input-group">
                            <span class="form-control">{{ auth()->user()->promote_credits }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Promotion Days')</label>
                        <div class="input-group">
                            <input type="number" name="days" class="form-control" min="1" max="100" value="1" required>
                            <span class="input-group-text">Days</span>
                        </div>
                        <small class="text-muted">Each day costs 1 promote credit. The token will be displayed in the Promoted Coins section for the specified number of days.</small>
                    </div>
                    <div class="form-group mt-3 text-end">
                        <button type="submit" class="btn btn--base w-100">@lang('Confirm Promotion')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Trend Vote Modal -->
<div class="modal custom--modal fade" id="trendVoteModal" tabindex="-1" aria-labelledby="trendVoteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="trendVoteModalLabel">@if($trendVotes > 0)@lang('Use Trend Votes')@else Buy Trend Votes @endif</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="las la-info-circle"></i> You've already used your free daily vote for this token. You can use your trend votes to give it an extra boost to trend higher on the homepage.
                </div>
                <form action="{{ route('token.trend-vote') }}" method="POST">
                    @csrf
                    <input type="hidden" name="chain_id" value="{{ $token->chain_id }}">
                    <input type="hidden" name="token_address" value="{{ $token->token_address }}">
                    <div class="form-group">
                        <label>@lang('Token')</label>
                        <div class="input-group">
                            <span class="form-control">{{ $token->token_name }} ({{ $token->token_symbol }})</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Available Trend Votes')</label>
                        <div class="input-group">
                            <span class="form-control">{{ $trendVotes }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Number of Trend Votes to Use')</label>
                        <div class="input-group">
                            <input type="number" name="quantity" class="form-control" min="1" max="{{ $trendVotes }}" value="1" required>
                            <span class="input-group-text">Votes</span>
                        </div>
                    </div>
                    <div class="form-group mt-3 text-end">
                        @if($trendVotes > 0)
                            <button type="submit" class="btn btn--base w-100">@lang('Use Trend Votes')</button>
                        @else
                            <a href="{{ route('user.plans.buy.trend.votes') }}" class="btn btn--base w-100">Buy Trend Votes</a>
                        @endif
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Responsive styles for modals */
    @media (max-width: 1024px) {
        .modal-dialog {
            max-width: 90%;
            margin: 1.75rem auto;
        }
    }

    @media (max-width: 767px) {
        .modal-dialog {
            max-width: 95%;
            margin: 1rem auto;
        }

        .modal-content {
            padding: 10px;
        }

        .modal-header {
            padding: 10px 15px;
        }

        .modal-body {
            padding: 15px;
        }

        .alert {
            padding: 10px;
            font-size: 14px;
        }
    }

    @media (max-width: 575px) {
        .modal-dialog {
            margin: 0.5rem auto;
        }

        .modal-content {
            padding: 5px;
        }

        .modal-header {
            padding: 8px 12px;
        }

        .modal-body {
            padding: 12px;
        }

        .alert {
            padding: 8px;
            font-size: 13px;
        }

        .form-group label {
            font-size: 14px;
        }
    }

    /* Specific iPad adjustments */
    @media (width: 1024px) and (height: 1366px),
           (width: 1024px) and (height: 600px),
           (width: 768px) and (height: 1024px) {
        .modal-dialog {
            max-width: 80%;
            margin: 2rem auto;
        }
    }
</style>
@endauth

<!-- No Explorer Modal -->
<div class="modal custom--modal fade" id="noExplorerModal" tabindex="-1" aria-labelledby="noExplorerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff;">
                <h5 class="modal-title" id="noExplorerModalLabel">@lang('No Explorer Found')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <i class="las la-exclamation-circle fs-1 text-warning mb-3"></i>
                <p>No blockchain explorer is available for this chain.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
            </div>
        </div>
    </div>
</div>

<!-- Check Contract Error Modal -->
<div class="modal custom--modal fade" id="checkContractErrorModal" tabindex="-1" aria-labelledby="checkContractErrorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff;">
                <h5 class="modal-title" id="checkContractErrorModalLabel">@lang('Contract Check')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <i class="las la-info-circle fs-1" style="color: #BE8400;"></i>
                <p class="mt-3">Unable to check contract at this time. The contract may be invalid or not yet available on the blockchain. Please try again later.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
            </div>
        </div>
    </div>
</div>

<!-- GoPlus Security Modal -->
<div class="modal custom--modal fade" id="securityScanModal" tabindex="-1" aria-labelledby="securityScanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff;">
                <h5 class="modal-title" id="securityScanModalLabel">@lang('Contract Security Scan')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="securityScanLoading" class="text-center py-5">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Scanning contract for security issues...</p>
                </div>
                <div id="securityScanError" class="text-center py-4" style="display: none;">
                    <i class="las la-info-circle fs-1" style="color: #BE8400;"></i>
                    <p class="mt-3" id="securityScanErrorMessage">Unable to scan contract at this time. The contract may be invalid or not yet available on the blockchain. Please try again later.</p>
                </div>
                <div id="securityScanResults" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg--dark h-100">
                                <div class="card-header">
                                    <h6 class="card-title mb-0 text-white">Basic Info</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-dark">
                                        <tr>
                                            <th>Token Name</th>
                                            <td id="tokenName">-</td>
                                        </tr>
                                        <tr>
                                            <th>Token Symbol</th>
                                            <td id="tokenSymbol">-</td>
                                        </tr>
                                        <tr>
                                            <th>Chain</th>
                                            <td id="tokenChain">-</td>
                                        </tr>
                                        <tr>
                                            <th>Contract Address</th>
                                            <td id="tokenAddress" class="text-break">-</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg--dark h-100">
                                <div class="card-header">
                                    <h6 class="card-title mb-0 text-white">Security Score</h6>
                                </div>
                                <div class="card-body text-center">
                                    <div id="securityScoreContainer" class="mb-3">
                                        <div id="securityScore" class="display-4 fw-bold">-</div>
                                        <div id="securityScoreText" class="mt-2">Calculating...</div>
                                    </div>
                                    <div id="securityWarning" class="alert alert-warning" style="display: none;">
                                        <i class="las la-exclamation-triangle"></i> This contract has potential security issues.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg--dark mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0 text-white">Contract Analysis</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm table-dark" id="securityChecksTable">
                                    <thead>
                                        <tr>
                                            <th>Check</th>
                                            <th>Status</th>
                                            <th>Details</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="las la-info-circle"></i> Security scan powered by GoPlus Security. This is for informational purposes only and should not be considered as financial advice.
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
    body {
        background-color: #0E1621;
        color: #B9BABB;
    }

    .card {
        background-color: #17212B;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .card-header {
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .token-icon {
        border-radius: 50%;
        object-fit: cover;
    }

    .token-rank {
        display: inline-block;
        background-color: #BE8400;
        color: white;
        padding: 2px 10px;
        border-radius: 12px;
        font-size: 16px;
        margin-left: 10px;
        vertical-align: middle;
    }

    .price-big {
        font-size: 2rem;
        font-weight: bold;
    }

    .timeframe-label {
        font-size: 0.85rem;
        color: #aaa;
    }

    .timeframe-value {
        font-weight: bold;
    }

    .text--white {
        color: #B9BABB !important;
    }

    .bg--dark {
        background-color: #1C2631 !important;
    }

    .bg--base-two {
        background-color: #17212B !important;
    }

    .stat-label {
        color: #aaa;
    }



    .stat-value {
        color: #ffffff;
        font-weight: 600;
    }

    /* Token details ad styles */
    .token-details-ad {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        overflow: hidden;
    }

    .token-details-ad .ad-container {
        margin: 0;
    }

    /* Make promote text match vote count exactly */
    .stat-value.token-action-icon.promote-icon {
        font-size: 1em;
        font-weight: 600;
        display: block;
    }

    .table-dark {
        background-color: #1C2631;
        color: #B9BABB;
    }

    .table-dark th,
    .table-dark td {
        border-color: rgba(255, 255, 255, 0.1);
    }

    /* New box styles */
    .timeframe-box {
        background-color: #1C2631;
        border-radius: 8px;
        padding: 8px 5px;
        margin-bottom: 10px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .icon-box {
        background-color: #1C2631;
        border-radius: 8px;
        padding: 8px;
        text-align: center;
        height: 90%;
        border: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: 5px;
    }

    .action-box {
        background-color: #1C2631;
        border-radius: 8px;
        padding: 10px 5px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .info-box {
        background-color: #1C2631;
        border-radius: 8px;
        padding: 12px 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dex-info {
        padding: 15px;
    }

    .stat-icon {
        font-size: 18px;
        margin-bottom: 5px;
        display: block;
    }

    /* Ensure consistent spacing in promote box */
    .promote-box .stat-icon,
    .vote-box .stat-icon {
        margin-bottom: 5px;
    }

    /* Token action icon styling */
    .token-action-icon {
        font-size: 18px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: block;
    }

    .vote-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 100, 0, 0.7));
    }

    .promote-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.7));
    }

    /* Special hover effect for the text */
    .stat-value.promote-icon:hover {
        transform: scale(1.05);
        text-shadow: 0 0 3px rgba(255, 215, 0, 0.5);
    }

    .promote-box {
        padding: 15px 10px;
        height: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .promote-box:hover {
        border-color: rgba(255, 215, 0, 0.4);
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
    }

    /* Flag box styling to match other boxes */
    .flag-box {
        padding: 15px 10px;
        height: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .flag-box:hover {
        border-color: rgba(255, 0, 0, 0.4);
        box-shadow: 0 0 10px rgba(255, 0, 0, 0.2);
    }

    .flag-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 0, 0, 0.7));
    }

    .dex-name, .chain-name {
        font-weight: 600;
        color: #ffffff;
    }

    .liquidity-value {
        font-size: 0.85rem;
        color: #aaa;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    /* Market cap styling */
    .market-cap-wrapper .stat-label,
    .market-cap-wrapper .stat-value {
        display: inline-block;
    }

    /* Button styling */
    .btn-token {
        background-color: #BE8400;
        color: #ffffff;
        border: none;
        font-weight: 600;
        padding: 10px 15px;
        transition: background-color 0.3s ease;
    }

    .btn-token:hover {
        background-color: #D99A00; /* Lighter than #BE8400 */
        color: #ffffff;
    }

    /* Promote button styling */
    .btn-promote {
        background-color: #31D7A9;
        color: #ffffff;
        border: none;
        font-weight: 600;
        padding: 10px 15px;
        transition: background-color 0.3s ease;
    }

    .btn-promote:hover {
        background-color: #2AB090; /* Darker than #31D7A9 */
        color: #ffffff;
    }

    /* Presale styling */
    .btn-presale {
        background-color: #8E44AD; /* Purple color for presale */
        color: #ffffff;
        border: none;
        font-weight: 600;
        padding: 10px 15px;
        transition: background-color 0.3s ease;
    }

    .btn-presale:hover {
        background-color: #9B59B6; /* Lighter purple */
        color: #ffffff;
    }

    .presale-badge {
        background-color: #8E44AD;
        color: #ffffff;
        padding: 8px 15px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 1.1rem;
        display: inline-block;
        margin-bottom: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .presale-info {
        display: flex;
        flex-direction: column;
    }

    /* Vote box styling */
    .vote-box {
        padding: 15px 10px;
        height: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .vote-box:hover {
        border-color: rgba(255, 100, 0, 0.4);
        box-shadow: 0 0 10px rgba(255, 100, 0, 0.2);
    }

    .vote-label {
        font-size: 12px;
        color: #aaa;
        margin-top: 5px;
    }

    .vote-actions {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .btn-vote {
        background-color: #BE8400;
        color: white;
        border: none;
        font-size: 12px;
        padding: 5px 10px;
        transition: all 0.3s ease;
        white-space: nowrap;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-vote:hover:not(.disabled) {
        background-color: #9e6e00;
        color: white;
    }

    .btn-vote.disabled {
        background-color: #6c757d;
        cursor: not-allowed;
    }

    .btn-trend-vote {
        background-color: #31D7A9;
        color: white;
        border: none;
        font-size: 12px;
        padding: 5px 10px;
        transition: all 0.3s ease;
        white-space: nowrap;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-trend-vote:hover {
        background-color: #2AB090;
        color: white;
    }

    .fire-emoji-small {
        font-size: 14px;
        margin-right: 2px;
        align-items: center;
        justify-content: center;
    }

    /* Universal responsive layout for all devices */
    /* Make content stack on all devices up to 1199px (covers all mobile and tablet devices) */
    @media (max-width: 1199px) {
        .token-details-main-row {
            display: flex;
            flex-direction: column !important;
        }

        .token-details-main-row .col-lg-8,
        .token-details-main-row .col-lg-4 {
            width: 100% !important;
            max-width: 100% !important;
            flex: 0 0 100% !important;
        }

        /* Ensure proper spacing between sections */
        .card.mb-4 {
            margin-bottom: 20px !important;
        }
    }

    /* Adjust chart height based on screen size */
    @media (max-width: 767px) {
        .dexscreener-chart-container {
            height: 400px !important;
        }
    }

    @media (min-width: 768px) and (max-width: 991px) {
        .dexscreener-chart-container {
            height: 450px !important;
        }
    }

    @media (min-width: 992px) and (max-width: 1199px) {
        .dexscreener-chart-container {
            height: 500px !important;
        }
    }

    /* Additional responsiveness for portrait orientation on any device */
    @media (orientation: portrait) {
        .token-details-main-row {
            display: flex;
            flex-direction: column !important;
        }

        .token-details-main-row .col-lg-8,
        .token-details-main-row .col-lg-4 {
            width: 100% !important;
            max-width: 100% !important;
            flex: 0 0 100% !important;
        }
    }

    /* Responsive styles for vote buttons */
    @media (max-width: 1024px), (width: 912px) {
        .vote-box, .flag-box, .promote-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .stat-icon {
            margin-bottom: 5px;
        }

        /* Improve action boxes layout */
        .action-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px 5px;
        }

        /* Ensure info boxes display properly */
        .info-box {
            padding: 10px;
        }

        /* Fix timeframes display */
        .timeframe-box {
            padding: 8px 5px;
        }

        .timeframe-label {
            font-size: 13px;
        }

        .timeframe-value {
            font-size: 13px;
        }
    }

    @media (max-width: 991px) {
        .btn-vote, .btn-trend-vote {
            font-size: 11px;
            padding: 4px 8px;
        }

        .fire-emoji-small {
            font-size: 13px;
        }

        /* Fix social buttons layout */
        .card-body .row .col-6 {
            padding-left: 5px;
            padding-right: 5px;
        }

        .card-body .row {
            margin-left: -5px;
            margin-right: -5px;
        }
    }

    @media (max-width: 767px) {
        .icon-box {
            padding: 10px 5px;
        }

        .stat-icon {
            font-size: 18px;
        }

        .stat-value {
            font-size: 14px;
        }

        /* Improve token header on mobile */
        .token-icon-wrapper {
            margin-right: 10px;
        }

        .token-icon {
            width: 48px;
            height: 48px;
        }

        .token-info h2 {
            font-size: 1.2rem;
        }
    }

    @media (max-width: 575px) {
        .btn-vote, .btn-trend-vote {
            font-size: 10px;
            padding: 3px 6px;
        }

        .fire-emoji-small {
            font-size: 12px;
            margin-right: 1px;
        }

        .icon-box {
            padding: 8px 3px;
        }

        /* Fix small screen layout */
        .action-box {
            padding: 6px 2px;
        }

        .stat-label {
            font-size: 12px;
        }

        .stat-value {
            font-size: 12px;
        }
    }

    /* Tablet styling adjustments */
    @media (min-width: 768px) and (max-width: 1199px) {
        .icon-box {
            padding: 12px 8px;
        }

        .stat-icon {
            font-size: 20px;
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 15px;
        }

        /* Improve layout for tablets */
        .row {
            margin-left: -10px;
            margin-right: -10px;
        }

        .col-md-8, .col-md-4,
        .col-4, .col-6, .col-3 {
            padding-left: 10px;
            padding-right: 10px;
        }

        /* Ensure buttons are properly sized */
        .btn-token, .btn-presale, .btn-promote {
            padding: 8px 12px;
            font-size: 14px;
        }

        /* Ensure token info displays properly */
        .token-info h2 {
            font-size: 1.5rem;
        }
    }

    /* Small tablet adjustments */
    @media (min-width: 768px) and (max-width: 991px) {
        .icon-box {
            padding: 10px 6px;
        }

        .stat-icon {
            font-size: 18px;
            margin-bottom: 6px;
        }

        .stat-value {
            font-size: 14px;
        }
    }

    /* Large tablet adjustments */
    @media (min-width: 992px) and (max-width: 1199px) {
        /* Adjust icon boxes for larger tablets */
        .icon-box {
            padding: 14px 10px;
        }

        .stat-icon {
            font-size: 22px;
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 16px;
        }

        /* Adjust buttons for larger tablets */
        .btn-token, .btn-presale, .btn-promote {
            padding: 10px 15px;
            font-size: 15px;
        }

        /* Adjust vote buttons for larger tablets */
        .btn-vote, .btn-trend-vote {
            font-size: 13px;
            padding: 6px 10px;
        }

        .fire-emoji-small {
            font-size: 15px;
        }

        /* Adjust token info for larger tablets */
        .token-info h2 {
            font-size: 1.7rem;
        }

        .token-icon {
            width: 60px;
            height: 60px;
        }

        /* Adjust timeframes for larger tablets */
        .timeframe-box {
            padding: 10px 8px;
        }

        .timeframe-label {
            font-size: 15px;
        }

        .timeframe-value {
            font-size: 15px;
        }

        /* Improve layout for larger tablets */
        .row {
            margin-left: -12px;
            margin-right: -12px;
        }

        .col-md-8, .col-md-4,
        .col-4, .col-6, .col-3 {
            padding-left: 12px;
            padding-right: 12px;
        }

        /* Adjust action boxes for larger tablets */
        .action-box {
            padding: 12px 8px;
        }

        /* Adjust info boxes for larger tablets */
        .info-box {
            padding: 12px;
        }

        /* Adjust card spacing for larger tablets */
        .card {
            margin-bottom: 20px;
        }

        .card-body {
            padding: 20px;
        }

        /* Ensure proper spacing in the container */
        .container.py-4 {
            padding-top: 25px !important;
            padding-bottom: 25px !important;
            padding-left: 20px;
            padding-right: 20px;
        }

        /* Fix copy button for larger tablets */
        .copy-btn {
            font-size: 14px;
            padding: 6px 10px;
        }
    }

    .presale-date {
        color: #B9BABB;
        font-size: 0.9rem;
    }

    .presale-age-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 5px 0;
    }

    .presale-age-label {
        font-weight: 600;
        color: #B9BABB;
    }

    .presale-age-badge {
        background-color: #8E44AD;
        color: #ffffff;
        padding: 4px 12px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.9rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .presale-message {
        padding: 30px 15px;
    }

    .presale-icon {
        font-size: 3rem;
        color: #8E44AD;
        margin-bottom: 15px;
    }

    .presale-message p {
        font-size: 1.1rem;
        color: #B9BABB;
    }

    .presale-date-info {
        margin-top: 20px;
        padding: 15px;
        background-color: rgba(142, 68, 173, 0.1);
        border-radius: 8px;
        border: 1px solid rgba(142, 68, 173, 0.3);
    }

    .presale-date-info p {
        margin-bottom: 5px;
        font-size: 0.95rem;
    }

    .presale-na {
        color: #8E44AD !important;
        font-weight: bold !important;
        font-size: 1.1rem !important;
    }

    .presale-value {
        color: #BE8400 !important;
        font-weight: bold !important;
        font-size: 1.1rem !important;
    }

    .action-buttons-container {
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 8px;
    }

    /* Modal styling */
    #copyAddressModal .modal-header {
        background-color: #BE8400;
        color: #ffffff;
    }

    #copyAddressModal .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);
    }

    /* Chain badge styling */
    .chain-badge {
        background-color: #BE8400;
        color: #ffffff;
        font-weight: 500;
    }

    /* Social media icon box styling */
    .social-icon-box {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background-color: #243242;
        border-radius: 6px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
        transition: all 0.2s ease;
        margin-bottom: 5px;
    }

    .social-icon-box:hover {
        transform: translateY(-2px);
        box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.5);
    }

    /* Social links container */
    .social-links-container {
        gap: 5px;
    }

    /* Token description styling */
    .token-description {
        line-height: 1.6;
        word-wrap: break-word;
        font-size: 14px;
    }

    /* DexScreener chart container styling */
    .dexscreener-chart-container {
        width: 100%;
        height: 600px;
        overflow: hidden;
        border-radius: 0 0 8px 8px;
        background-color: #17212B;
    }

    /* Video container styles */
    .token-video-container {
        width: 100%;
        margin-bottom: 15px;
    }

    .responsive-video-container {
        position: relative;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        height: 0;
        overflow: hidden;
        max-width: 100%;
    }

    .responsive-video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
    }

    .responsive-video-container .native-video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        object-fit: cover;
        background-color: #000;
    }

    .video-link-container {
        text-align: center;
        padding: 15px 0;
    }

    /* Universal responsive styles for token details page */
    @media (max-width: 1199px) {
        .container.py-4 {
            padding-left: 15px;
            padding-right: 15px;
        }

        .card {
            margin-bottom: 15px;
        }

        .card-body {
            padding: 15px;
        }

        /* Ensure dexscreener chart is responsive on all devices */
        .dexscreener-chart-container iframe {
            height: 100% !important;
            width: 100% !important;
        }
    }

    /* Small mobile devices */
    @media (max-width: 480px) {
        .dexscreener-chart-container {
            height: 350px !important;
        }

        .token-info h2 {
            font-size: 1rem !important;
        }

        .token-icon {
            width: 40px !important;
            height: 40px !important;
        }

        .social-icon-box {
            width: 30px !important;
            height: 30px !important;
            margin-left: 2px !important;
        }

        .social-icon-box i {
            font-size: 18px !important;
        }
    }

    /* Universal token header layout fixes */
    .token-icon-wrapper {
        flex-shrink: 0;
    }

    .token-info {
        flex: 1;
        min-width: 0; /* Prevent text overflow */
    }

    .token-info h2 {
        word-break: break-word;
        overflow-wrap: break-word;
        font-size: 1.4rem;
    }

    /* Fix copy button */
    .copy-btn {
        font-size: 12px;
        padding: 4px 8px;
    }

    /* Adjust social icon boxes */
    .social-icon-box {
        width: 32px;
        height: 32px;
        margin-left: 5px !important;
    }

    .social-icon-box i {
        font-size: 20px !important;
    }

    /* Improve social links container */
    .social-links-container {
        margin-top: 5px;
    }

    /* Medium-small devices */
    @media (max-width: 576px) {
        .token-info h2 {
            font-size: 1.2rem;
        }

        .social-links-container {
            margin-top: 8px;
        }

        .social-icon-box {
            width: 30px;
            height: 30px;
            margin-left: 4px !important;
        }

        .social-icon-box i {
            font-size: 19px !important;
        }
    }

    /* Extra small devices */
    @media (max-width: 375px) {
        .token-info h2 {
            font-size: 1.1rem;
        }

        .social-icon-box {
            width: 28px;
            height: 28px;
            margin-left: 3px !important;
        }

        .social-icon-box i {
            font-size: 18px !important;
        }
    }
</style>
@endpush

@push('script')
<script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.8/dist/clipboard.min.js"></script>

<script>
    // Initialize clipboard.js
    var clipboard = new ClipboardJS('.copy-btn');

    clipboard.on('success', function(e) {
        // Show custom modal instead of browser alert
        $('#copyAddressModal').modal('show');
        e.clearSelection();
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    // Handle promote icon click
    $('.promote-icon').on('click', function() {
        var tokenAddress = $(this).data('token-address');
        var chainId = $(this).data('chain-id');
        var tokenSymbol = $(this).data('token-symbol');

        @auth
            // Show promote modal
            $('#promoteTokenModal').modal('show');
        @else
            // Redirect to login page
            window.location.href = '{{ route("user.login") }}';
        @endauth
    });

    // Flag to prevent duplicate vote submissions
    var isVoteSubmitting = false;

    // Handle vote icon click
    $('.vote-icon').on('click', function() {
        // Prevent duplicate submissions
        if (isVoteSubmitting) {
            return false;
        }

        var voteIcon = $(this);
        var tokenAddress = voteIcon.data('token-address');
        var chainId = voteIcon.data('chain-id');
        var tokenSymbol = voteIcon.data('token-symbol');

        @auth
            // Set flag to prevent duplicate submissions
            isVoteSubmitting = true;

            // Add visual feedback
            voteIcon.css('opacity', '0.5');

            // Check if user has already voted for this token today
            $.ajax({
                url: '{{ route("check.token.vote") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    chain_id: chainId,
                    token_address: tokenAddress
                },
                success: function(response) {
                    if (response.has_voted) {
                        // User has already used their daily vote, show trend vote modal
                        $('#trendVoteModal').modal('show');
                        // Reset submission flag and visual feedback
                        isVoteSubmitting = false;
                        voteIcon.css('opacity', '1');
                    } else {
                        // User hasn't voted today, submit regular vote
                        var form = $('<form>', {
                            'method': 'POST',
                            'action': '{{ route("token.vote") }}'
                        }).append($('<input>', {
                            'type': 'hidden',
                            'name': '_token',
                            'value': '{{ csrf_token() }}'
                        })).append($('<input>', {
                            'type': 'hidden',
                            'name': 'chain_id',
                            'value': chainId
                        })).append($('<input>', {
                            'type': 'hidden',
                            'name': 'token_address',
                            'value': tokenAddress
                        }));

                        $('body').append(form);

                        // Use setTimeout to ensure we don't submit the form multiple times
                        setTimeout(function() {
                            form.submit();
                        }, 100);
                    }
                },
                error: function() {
                    // On error, default to showing the modal
                    $('#trendVoteModal').modal('show');
                    // Reset submission flag and visual feedback
                    isVoteSubmitting = false;
                    voteIcon.css('opacity', '1');
                }
            });
        @else
            // Redirect to login page
            window.location.href = '{{ route("user.login") }}';
        @endauth
    });

    // Handle flag icon click
    $('.flag-icon').on('click', function() {
        var tokenAddress = $(this).data('token-address');
        var chainId = $(this).data('chain-id');
        var tokenSymbol = $(this).data('token-symbol');

        @auth
            // Check if user has already voted negatively for this token today
            $.ajax({
                url: '{{ route("check.token.vote") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    chain_id: chainId,
                    token_address: tokenAddress
                },
                success: function(response) {
                    if (response.has_negative_voted) {
                        // User has already used their daily negative vote
                        // Use the system's notification mechanism instead of browser alert
                        notify('error', 'You have already voted negatively for this token today');
                    } else {
                        // User hasn't voted negatively today, submit negative vote
                        var form = $('<form>', {
                            'method': 'POST',
                            'action': '{{ route("token.negative-vote") }}'
                        }).append($('<input>', {
                            'type': 'hidden',
                            'name': '_token',
                            'value': '{{ csrf_token() }}'
                        })).append($('<input>', {
                            'type': 'hidden',
                            'name': 'chain_id',
                            'value': chainId
                        })).append($('<input>', {
                            'type': 'hidden',
                            'name': 'token_address',
                            'value': tokenAddress
                        }));

                        $('body').append(form);

                        // Use setTimeout to ensure we don't submit the form multiple times
                        setTimeout(function() {
                            form.submit();
                        }, 100);
                    }
                },
                error: function() {
                    notify('error', 'Error checking vote status. Please try again.');
                }
            });
        @else
            // Redirect to login page
            window.location.href = '{{ route("user.login") }}';
        @endauth
    });

    // Handle scan contract button click
    $('.scan-contract-btn').on('click', function() {
        var tokenAddress = $(this).data('token-address');
        var chainId = $(this).data('chain-id');

        // Reset modal state
        $('#securityScanLoading').show();
        $('#securityScanError').hide();
        $('#securityScanResults').hide();

        // Show the modal
        $('#securityScanModal').modal('show');

        // Populate basic token info
        $('#tokenName').text('{{ $token->token_name }}');
        $('#tokenSymbol').text('{{ $token->token_symbol }}');
        $('#tokenChain').text('{{ ucfirst($token->chain_id) }}');
        $('#tokenAddress').text(tokenAddress);

        // Convert chain ID to numeric format if needed
        var numericChainId = chainId;

        // Chain ID mapping based on GoPlus API documentation
        var chainMapping = {
            // Main networks
            'ethereum': '1',
            'eth': '1',
            'bsc': '56',
            'bnb': '56',
            'polygon': '137',
            'matic': '137',
            'arbitrum': '42161',
            'arb': '42161',
            'optimism': '10',
            'op': '10',
            'avalanche': '43114',
            'avax': '43114',
            'fantom': '250',
            'ftm': '250',
            'cronos': '25',
            'cro': '25',
            'okc': '66',
            'heco': '128',
            'tron': 'tron',
            'trx': 'tron',
            'kcc': '321',

            // Layer 2 and other networks
            'zksync': '324',
            'zksyncera': '324',
            'linea': '59144',
            'base': '8453',
            'scroll': '534352',
            'gnosis': '100',
            'xdai': '100',
            'ethw': '10001',
            'fon': '201022',
            'mantle': '5000',
            'opbnb': '204',
            'zkfair': '42766',
            'blast': '81457',
            'manta': '169',
            'mantapacific': '169',
            'berachain': '80094',
            'bera': '80094',
            'abstract': '2741',
            'hashkey': '177',
            'hashkeychain': '177',
            'sonic': '146',
            'story': '1514'
        };

        // Look up the chain ID in the mapping
        var chainIdLower = chainId.toLowerCase();
        if (chainMapping[chainIdLower]) {
            numericChainId = chainMapping[chainIdLower];
        }

        // Make the API call to get security information
        $.ajax({
            url: '{{ route("token.security") }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                chain_id: numericChainId,
                token_address: tokenAddress
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Hide loading indicator
                    $('#securityScanLoading').hide();

                    // Process and display the security data
                    processSecurityData(response.data);

                    // Show results
                    $('#securityScanResults').show();
                } else {
                    // Show error message
                    $('#securityScanLoading').hide();
                    $('#securityScanError').show();
                    $('#securityScanErrorMessage').text('Unable to scan contract at this time. The contract may be invalid or not yet available on the blockchain. Please try again later.');
                }
            },
            error: function(xhr, status, error) {
                // Show error message
                $('#securityScanLoading').hide();
                $('#securityScanError').show();
                $('#securityScanErrorMessage').text('Unable to scan contract at this time. The contract may be invalid or not yet available on the blockchain. Please try again later.');
            }
        });
    });

    // Function to process and display security data
    function processSecurityData(data) {
        // Calculate security score
        let securityIssues = 0;
        let totalChecks = 0;
        let securityChecks = [];

        // Handle different response formats
        if (Array.isArray(data)) {
            // If data is an array, use the first item
            data = data[0];
        } else if (typeof data !== 'object') {
            // If data is not an object, show error
            $('#securityScanLoading').hide();
            $('#securityScanError').show();
            $('#securityScanErrorMessage').text('Unable to scan contract at this time. The contract may be invalid or not yet available on the blockchain. Please try again later.');
            return;
        }

        // Check if we have the expected data structure
        if (!data || Object.keys(data).length === 0) {
            $('#securityScanLoading').hide();
            $('#securityScanError').show();
            $('#securityScanErrorMessage').text('Unable to scan contract at this time. The contract may be invalid or not yet available on the blockchain. Please try again later.');
            return;
        }

        // Check if the data is nested in a different structure
        // Some API responses might have the token data nested under a different key
        if (!data.is_open_source && !data.owner_address && !data.buy_tax && !data.sell_tax) {
            // Try to find the token data in a nested structure
            let foundNestedData = false;

            // Check if data has a 'result' field
            if (data.result && typeof data.result === 'object') {
                // If result is an object with token address as key
                const resultKeys = Object.keys(data.result);
                if (resultKeys.length > 0) {
                    // Try to find the token address in the result keys (case-insensitive)
                    const tokenAddress = $('#tokenAddress').text().toLowerCase();
                    let matchingKey = null;

                    for (const key of resultKeys) {
                        if (key.toLowerCase() === tokenAddress) {
                            matchingKey = key;
                            break;
                        }
                    }

                    // If we found a matching key, use it
                    if (matchingKey) {
                        const potentialTokenData = data.result[matchingKey];
                        if (potentialTokenData && typeof potentialTokenData === 'object') {
                            data = potentialTokenData;
                            foundNestedData = true;
                        }
                    }
                    // Otherwise, use the first key that looks like an address
                    else {
                        let addressKey = null;
                        for (const key of resultKeys) {
                            if (key.startsWith('0x')) {
                                addressKey = key;
                                break;
                            }
                        }

                        // If we found an address key, use it
                        if (addressKey) {
                            const potentialTokenData = data.result[addressKey];
                            if (potentialTokenData && typeof potentialTokenData === 'object') {
                                data = potentialTokenData;
                                foundNestedData = true;
                            }
                        }
                        // Otherwise, use the first key
                        else {
                            const potentialTokenData = data.result[resultKeys[0]];
                            if (potentialTokenData && typeof potentialTokenData === 'object') {
                                data = potentialTokenData;
                                foundNestedData = true;
                            }
                        }
                    }
                }
            }

            // If we still don't have the right data, check each top-level field
            if (!foundNestedData) {
                for (const key in data) {
                    if (data[key] && typeof data[key] === 'object') {
                        const nestedData = data[key];
                        // Check if this object has token security fields
                        if (nestedData.is_open_source !== undefined ||
                            nestedData.owner_address !== undefined ||
                            nestedData.buy_tax !== undefined ||
                            nestedData.sell_tax !== undefined) {
                            data = nestedData;
                            foundNestedData = true;
                            break;
                        }
                    }
                }
            }

            // If we still couldn't find the right data structure, show error
            if (!foundNestedData) {
                $('#securityScanLoading').hide();
                $('#securityScanError').show();
                $('#securityScanErrorMessage').text('Unable to scan contract at this time. The contract may be invalid or not yet available on the blockchain. Please try again later.');
                return;
            }
        }

        // Helper function to normalize values (handle both string and boolean formats)
        function normalizeValue(value) {
            if (value === '1' || value === 1 || value === true || value === 'true') {
                return '1';
            }
            if (value === '0' || value === 0 || value === false || value === 'false') {
                return '0';
            }
            return value;
        }

        // Process contract checks
        if (data.is_open_source !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.is_open_source);
            securityChecks.push({
                check: 'Open Source',
                status: normalizedValue === '1',
                details: normalizedValue === '1' ? 'Contract is open source' : 'Contract is not open source'
            });
            if (normalizedValue !== '1') securityIssues++;
        }

        if (data.is_proxy !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.is_proxy);
            securityChecks.push({
                check: 'Proxy Contract',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Contract is a proxy contract' : 'Contract is not a proxy contract'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.is_mintable !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.is_mintable);
            securityChecks.push({
                check: 'Mintable',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Token supply can be increased' : 'Token supply cannot be increased'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.owner_address !== undefined) {
            totalChecks++;
            let hasOwner = data.owner_address !== '0x0000000000000000000000000000000000000000';
            securityChecks.push({
                check: 'Contract Owner',
                status: !hasOwner,
                details: hasOwner ? 'Contract has an owner who can modify it' : 'Contract ownership has been renounced'
            });
            if (hasOwner) securityIssues++;
        }

        if (data.can_take_back_ownership !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.can_take_back_ownership);
            securityChecks.push({
                check: 'Ownership Takeback',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Contract ownership can be taken back' : 'Contract ownership cannot be taken back'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.hidden_owner !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.hidden_owner);
            securityChecks.push({
                check: 'Hidden Owner',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Contract has a hidden owner' : 'No hidden owner detected'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.selfdestruct !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.selfdestruct);
            securityChecks.push({
                check: 'Self Destruct',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Contract can be self-destructed' : 'Contract cannot be self-destructed'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.external_call !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.external_call);
            securityChecks.push({
                check: 'External Calls',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Contract makes external calls' : 'No external calls detected'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.buy_tax !== undefined) {
            totalChecks++;
            let buyTax = parseFloat(data.buy_tax);
            let buyTaxDisplay = isNaN(buyTax) ? 'Unknown' : buyTax + '%';
            securityChecks.push({
                check: 'Buy Tax',
                status: !isNaN(buyTax) && buyTax <= 10,
                details: buyTaxDisplay + ' buy tax'
            });
            if (!isNaN(buyTax) && buyTax > 10) securityIssues++;
        }

        if (data.sell_tax !== undefined) {
            totalChecks++;
            let sellTax = parseFloat(data.sell_tax);
            let sellTaxDisplay = isNaN(sellTax) ? 'Unknown' : sellTax + '%';
            securityChecks.push({
                check: 'Sell Tax',
                status: !isNaN(sellTax) && sellTax <= 10,
                details: sellTaxDisplay + ' sell tax'
            });
            if (!isNaN(sellTax) && sellTax > 10) securityIssues++;
        }

        if (data.cannot_buy !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.cannot_buy);
            securityChecks.push({
                check: 'Can Buy',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Cannot buy this token' : 'Can buy this token'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.cannot_sell_all !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.cannot_sell_all);
            securityChecks.push({
                check: 'Can Sell All',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Cannot sell all tokens' : 'Can sell all tokens'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.slippage_modifiable !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.slippage_modifiable);
            securityChecks.push({
                check: 'Slippage Modifiable',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Slippage can be modified' : 'Slippage cannot be modified'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.is_honeypot !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.is_honeypot);
            securityChecks.push({
                check: 'Honeypot',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Contract is a honeypot' : 'Contract is not a honeypot'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.transfer_pausable !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.transfer_pausable);
            securityChecks.push({
                check: 'Transfer Pausable',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Transfers can be paused' : 'Transfers cannot be paused'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.blacklist !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.blacklist);
            securityChecks.push({
                check: 'Blacklist Function',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Contract has blacklist function' : 'No blacklist function detected'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.whitelist !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.whitelist);
            securityChecks.push({
                check: 'Whitelist Function',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Contract has whitelist function' : 'No whitelist function detected'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        if (data.anti_whale_modifiable !== undefined) {
            totalChecks++;
            const normalizedValue = normalizeValue(data.anti_whale_modifiable);
            securityChecks.push({
                check: 'Anti-Whale Modifiable',
                status: normalizedValue === '0',
                details: normalizedValue === '1' ? 'Anti-whale mechanism can be modified' : 'No modifiable anti-whale mechanism'
            });
            if (normalizedValue === '1') securityIssues++;
        }

        // Calculate score (0-100)
        let score = totalChecks > 0 ? Math.round(((totalChecks - securityIssues) / totalChecks) * 100) : 0;

        // Update score display
        $('#securityScore').text(score);

        // Set score color and text
        if (score >= 80) {
            $('#securityScore').css('color', '#28a745');
            $('#securityScoreText').text('Good');
            $('#securityScoreText').css('color', '#28a745');
            $('#securityWarning').hide();
        } else if (score >= 50) {
            $('#securityScore').css('color', '#ffc107');
            $('#securityScoreText').text('Medium Risk');
            $('#securityScoreText').css('color', '#ffc107');
            $('#securityWarning').show();
        } else {
            $('#securityScore').css('color', '#dc3545');
            $('#securityScoreText').text('High Risk');
            $('#securityScoreText').css('color', '#dc3545');
            $('#securityWarning').show();
        }

        // Populate security checks table
        let tableBody = $('#securityChecksTable tbody');
        tableBody.empty();

        securityChecks.forEach(function(check) {
            let statusIcon = check.status ?
                '<i class="las la-check-circle text-success"></i>' :
                '<i class="las la-exclamation-circle text-danger"></i>';

            tableBody.append(`
                <tr>
                    <td>${check.check}</td>
                    <td>${statusIcon}</td>
                    <td>${check.details}</td>
                </tr>
            `);
        });
    }

</script>
@endpush

@push('script')
<script>
    $(document).ready(function() {
        // Handle video player errors
        $('.native-video-player').on('error', function(e) {
            var videoUrl = $(this).find('source').first().attr('src');
            var videoContainer = $(this).closest('.responsive-video-container');

            // Replace the video player with a link
            videoContainer.html(`
                <div class="video-link-container">
                    <p class="text-center mb-3">Unable to play this video directly.</p>
                    <a href="${videoUrl}" target="_blank" class="btn btn-token btn-block">
                        <i class="las la-video"></i> Watch Video
                    </a>
                </div>
            `);
        });
    });
</script>
@endpush