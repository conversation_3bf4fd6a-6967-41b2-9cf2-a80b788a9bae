<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Lib\CurlRequest;
use App\Lib\GoogleAuthenticator;
use App\Models\AdminNotification;
use App\Models\CclToken;
use App\Models\Deposit;
use App\Models\Presale;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserLogin;
use App\Models\UserTokenPurchase;
use App\Models\Withdrawal;
use App\Rules\FileTypeValidate;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{

    public function dashboard()
    {
        $pageTitle = 'Dashboard';
        // User Info
        $widget['total_users']             = User::count();
        $widget['verified_users']          = User::active()->count();
        $widget['email_unverified_users']  = User::emailUnverified()->count();
        $widget['mobile_unverified_users'] = User::mobileUnverified()->count();

        $transactionCurrencies = Transaction::groupBy('currency')->orderBy('currency')->pluck('currency')->toArray();

        // user Browsing, Country, Operating Log
        $userLoginData = UserLogin::where('created_at', '>=', Carbon::now()->subDay(30))->get(['browser', 'os', 'country']);

        $chart['user_browser_counter'] = $userLoginData->groupBy('browser')->map(function ($item, $key) {
            return collect($item)->count();
        });
        $chart['user_os_counter'] = $userLoginData->groupBy('os')->map(function ($item, $key) {
            return collect($item)->count();
        });
        $chart['user_country_counter'] = $userLoginData->groupBy('country')->map(function ($item, $key) {
            return collect($item)->count();
        })->sort()->reverse()->take(5);

        $withdrawals['total_withdraw_amount']   = Withdrawal::approved()->sum('amount');
        $withdrawals['total_withdraw_pending']  = Withdrawal::pending()->count();
        $withdrawals['total_withdraw_rejected'] = Withdrawal::rejected()->count();
        $withdrawals['total_withdraw_charge']   = Withdrawal::approved()->sum('charge');

        return view('admin.dashboard', compact('pageTitle', 'widget', 'chart', 'withdrawals', 'transactionCurrencies'));
    }

    public function manageCclToken()
    {
        $pageTitle = 'Manage Launchpad';
        $cclToken = CclToken::getData();
        return view('admin.ccl_token.manage', compact('pageTitle', 'cclToken'));
    }

    public function updateTokenInfo(Request $request)
    {
        $request->validate([
            'token_name' => 'required|string|max:255',
            'token_symbol' => 'required|string|max:10',
            'total_supply' => 'required|string|max:255',
            'blockchain' => 'required|string|max:255',
            'about_content' => 'nullable|string',
            'buy_token_url' => 'nullable|url|max:255',
            'whitepaper_url' => 'nullable|url|max:255',
        ]);

        $cclToken = CclToken::getData();
        $cclToken->token_name = $request->token_name;
        $cclToken->token_symbol = $request->token_symbol;
        $cclToken->total_supply = $request->total_supply;
        $cclToken->blockchain = $request->blockchain;
        $cclToken->buy_token_url = $request->buy_token_url;
        $cclToken->whitepaper_url = $request->whitepaper_url;

        if ($request->has('about_content')) {
            $cclToken->about_content = $request->about_content;
        }

        $cclToken->save();

        $notify[] = ['success', 'Token information updated successfully'];
        return back()->withNotify($notify);
    }

    // Presale management has been consolidated into the dedicated presale management interface

    public function updateTokenUtility(Request $request)
    {
        $request->validate([
            'utility_title' => 'required|array',
            'utility_title.*' => 'required|string|max:255',
            'utility_description' => 'required|array',
            'utility_description.*' => 'required|string',
        ]);

        $utilityItems = [];
        foreach ($request->utility_title as $key => $title) {
            $utilityItems[] = [
                'title' => $title,
                'description' => $request->utility_description[$key]
            ];
        }

        $cclToken = CclToken::getData();
        $cclToken->utility_items = $utilityItems;
        $cclToken->save();

        $notify[] = ['success', 'Token utility updated successfully'];
        return back()->withNotify($notify);
    }

    public function updateTokenRoadmap(Request $request)
    {
        $request->validate([
            'roadmap_title' => 'required|array',
            'roadmap_title.*' => 'required|string|max:255',
            'roadmap_description' => 'required|array',
            'roadmap_description.*' => 'required|string',
        ]);

        $roadmapItems = [];
        foreach ($request->roadmap_title as $key => $title) {
            $roadmapItems[] = [
                'title' => $title,
                'description' => $request->roadmap_description[$key]
            ];
        }

        $cclToken = CclToken::getData();
        $cclToken->roadmap_items = $roadmapItems;
        $cclToken->save();

        $notify[] = ['success', 'Token roadmap updated successfully'];
        return back()->withNotify($notify);
    }

    public function updateTokenDistribution(Request $request)
    {
        $request->validate([
            'distribution_label' => 'required|array',
            'distribution_label.*' => 'required|string|max:255',
            'distribution_percentage' => 'required|array',
            'distribution_percentage.*' => 'required|numeric|min:0|max:100',
            'distribution_color' => 'required|array',
            'distribution_color.*' => 'required|string',
        ]);

        $distributionItems = [];
        foreach ($request->distribution_label as $key => $label) {
            $distributionItems[] = [
                'label' => $label,
                'percentage' => $request->distribution_percentage[$key],
                'color' => $request->distribution_color[$key]
            ];
        }

        $cclToken = CclToken::getData();
        $cclToken->distribution_items = $distributionItems;
        $cclToken->save();

        $notify[] = ['success', 'Token distribution updated successfully'];
        return back()->withNotify($notify);
    }

    public function toggleCclToken()
    {
        $cclToken = CclToken::getData();
        $cclToken->is_enabled = !$cclToken->is_enabled;
        $cclToken->save();

        $status = $cclToken->is_enabled ? 'enabled' : 'disabled';
        $notify[] = ['success', "Launchpad links $status successfully"];
        return back()->withNotify($notify);
    }

    public function transactionReport(Request $request)
    {

        $diffInDays = Carbon::parse($request->start_date)->diffInDays(Carbon::parse($request->end_date));

        $groupBy = $diffInDays > 30 ? 'months' : 'days';
        $format = $diffInDays > 30 ? '%M-%Y'  : '%d-%M-%Y';

        $currency = $request->currency;

        if ($groupBy == 'days') {
            $dates = $this->getAllDates($request->start_date, $request->end_date);
        } else {
            $dates = $this->getAllMonths($request->start_date, $request->end_date);
        }

        $plusTransactions   = Transaction::where('trx_type', '+')
            ->where('remark', 'deposit')
            ->where('currency', $currency)
            ->whereDate('created_at', '>=', $request->start_date)
            ->whereDate('created_at', '<=', $request->end_date)
            ->selectRaw('SUM(amount) AS amount')
            ->selectRaw("DATE_FORMAT(created_at, '{$format}') as created_on")
            ->latest()
            ->groupBy('created_on')
            ->get();

        $minusTransactions  = Transaction::where('trx_type', '-')
            ->where('remark', 'withdraw')
            ->where('currency', $currency)
            ->whereDate('created_at', '>=', $request->start_date)
            ->whereDate('created_at', '<=', $request->end_date)
            ->selectRaw('SUM(amount) AS amount')
            ->selectRaw("DATE_FORMAT(created_at, '{$format}') as created_on")
            ->latest()
            ->groupBy('created_on')
            ->get();


        $data = [];

        foreach ($dates as $date) {
            $data[] = [
                'created_on' => $date,
                'credits' => getAmount($plusTransactions->where('created_on', $date)->first()?->amount ?? 0),
                'debits' => getAmount($minusTransactions->where('created_on', $date)->first()?->amount ?? 0)
            ];
        }

        $data = collect($data);

        // Calculate total profits (deposits - withdrawals)
        $totalDeposits = $data->sum('credits');
        $totalWithdrawals = $data->sum('debits');
        $totalProfits = $totalDeposits - $totalWithdrawals;

        $report['created_on']   = $data->pluck('created_on');
        $report['data']     = [
            [
                'name' => 'Deposits',
                'data' => $data->pluck('credits')
            ],
            [
                'name' => 'Withdrawals',
                'data' => $data->pluck('debits')
            ]
        ];
        $report['total_deposits'] = $totalDeposits;
        $report['total_withdrawals'] = $totalWithdrawals;
        $report['total_profits'] = $totalProfits;
        $report['currency'] = $currency;

        return response()->json($report);
    }




    private function getAllDates($startDate, $endDate)
    {
        $dates = [];
        $currentDate = new \DateTime($startDate);
        $endDate = new \DateTime($endDate);

        while ($currentDate <= $endDate) {
            $dates[] = $currentDate->format('d-F-Y');
            $currentDate->modify('+1 day');
        }

        return $dates;
    }

    private function  getAllMonths($startDate, $endDate)
    {
        if ($endDate > now()) {
            $endDate = now()->format('Y-m-d');
        }

        $startDate = new \DateTime($startDate);
        $endDate = new \DateTime($endDate);

        $months = [];

        while ($startDate <= $endDate) {
            $months[] = $startDate->format('F-Y');
            $startDate->modify('+1 month');
        }

        return $months;
    }


    public function profile()
    {
        $pageTitle = 'Profile';
        $admin = auth('admin')->user();

        $ga = new GoogleAuthenticator();
        $secret = $ga->createSecret();
        $qrCodeUrl = $ga->getQRCodeGoogleUrl($admin->username . '@' . gs('site_name'), $secret);

        return view('admin.profile', compact('pageTitle', 'admin', 'secret', 'qrCodeUrl'));
    }

    public function show2faForm()
    {
        $ga = new GoogleAuthenticator();
        $admin = auth('admin')->user();
        $secret = $ga->createSecret();
        $qrCodeUrl = $ga->getQRCodeGoogleUrl($admin->username . '@' . gs('site_name'), $secret);
        $pageTitle = '2FA Security';
        return view('admin.twofactor', compact('pageTitle', 'secret', 'qrCodeUrl'));
    }

    public function create2fa(Request $request)
    {
        $admin = auth('admin')->user();
        $request->validate([
            'key' => 'required',
            'code' => 'required',
        ]);
        $response = verifyG2fa($admin, $request->code, $request->key);
        if ($response) {
            $admin->tsc = $request->key;
            $admin->ts = Status::ENABLE;
            $admin->save();
            $notify[] = ['success', 'Two factor authenticator activated successfully'];
            return back()->withNotify($notify);
        } else {
            $notify[] = ['error', 'Wrong verification code'];
            return back()->withNotify($notify);
        }
    }

    public function disable2fa(Request $request)
    {
        $request->validate([
            'code' => 'required',
        ]);

        $admin = auth('admin')->user();
        $response = verifyG2fa($admin, $request->code);
        if ($response) {
            $admin->tsc = null;
            $admin->ts = Status::DISABLE;
            $admin->save();
            $notify[] = ['success', 'Two factor authenticator deactivated successfully'];
        } else {
            $notify[] = ['error', 'Wrong verification code'];
        }
        return back()->withNotify($notify);
    }

    public function profileUpdate(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'email' => 'required|email',
            'image' => ['nullable', 'image', new FileTypeValidate(['jpg', 'jpeg', 'png'])]
        ]);
        $user = auth('admin')->user();

        if ($request->hasFile('image')) {
            try {
                $old = $user->image;
                $user->image = fileUploader($request->image, getFilePath('adminProfile'), getFileSize('adminProfile'), $old);
            } catch (\Exception $exp) {
                $notify[] = ['error', 'Couldn\'t upload your image'];
                return back()->withNotify($notify);
            }
        }

        $user->name = $request->name;
        $user->email = $request->email;
        $user->save();
        $notify[] = ['success', 'Profile updated successfully'];
        return to_route('admin.profile')->withNotify($notify);
    }

    public function password()
    {
        $pageTitle = 'Password Setting';
        $admin = auth('admin')->user();
        return view('admin.password', compact('pageTitle', 'admin'));
    }

    public function passwordUpdate(Request $request)
    {
        $request->validate([
            'old_password' => 'required',
            'password' => 'required|min:5|confirmed',
        ]);

        $user = auth('admin')->user();
        if (!Hash::check($request->old_password, $user->password)) {
            $notify[] = ['error', 'Password doesn\'t match!!'];
            return back()->withNotify($notify);
        }
        $user->password = Hash::make($request->password);
        $user->save();
        $notify[] = ['success', 'Password changed successfully.'];
        return to_route('admin.password')->withNotify($notify);
    }

    public function notifications()
    {
        $notifications = AdminNotification::orderBy('id', 'desc')->with('user')->paginate(getPaginate());
        $hasUnread = AdminNotification::where('is_read', Status::NO)->exists();
        $hasNotification = AdminNotification::exists();
        $pageTitle = 'Notifications';
        return view('admin.notifications', compact('pageTitle', 'notifications', 'hasUnread', 'hasNotification'));
    }


    public function notificationRead($id)
    {
        $notification = AdminNotification::findOrFail($id);
        $notification->is_read = Status::YES;
        $notification->save();
        $url = $notification->click_url;
        if ($url == '#') {
            $url = url()->previous();
        }
        return redirect($url);
    }



    public function readAllNotification()
    {
        AdminNotification::where('is_read', Status::NO)->update([
            'is_read' => Status::YES
        ]);
        $notify[] = ['success', 'Notifications read successfully'];
        return back()->withNotify($notify);
    }

    public function deleteAllNotification()
    {
        AdminNotification::truncate();
        $notify[] = ['success', 'Notifications deleted successfully'];
        return back()->withNotify($notify);
    }

    public function deleteSingleNotification($id)
    {
        AdminNotification::where('id', $id)->delete();
        $notify[] = ['success', 'Notification deleted successfully'];
        return back()->withNotify($notify);
    }

    public function downloadAttachment($fileHash)
    {
        $filePath = decrypt($fileHash);
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        $title = slug(gs('site_name')) . '- attachments.' . $extension;
        try {
            $mimetype = mime_content_type($filePath);
        } catch (\Exception $e) {
            $notify[] = ['error', 'File does not exists'];
            return back()->withNotify($notify);
        }
        header('Content-Disposition: attachment; filename="' . $title);
        header("Content-Type: " . $mimetype);
        return readfile($filePath);
    }

    // Presale Management Methods

    /**
     * Display a listing of presales
     */
    public function managePresales()
    {
        $pageTitle = 'Manage Presales';
        $presales = Presale::orderBy('position', 'asc')->paginate(getPaginate());
        return view('admin.presales.index', compact('pageTitle', 'presales'));
    }

    /**
     * Show the form for creating a new presale
     */
    public function createPresale()
    {
        $pageTitle = 'Create Presale';
        return view('admin.presales.create', compact('pageTitle'));
    }

    /**
     * Store a newly created presale
     */
    public function storePresale(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'token_name' => 'required|string|max:255',
            'token_symbol' => 'required|string|max:10',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'quantity' => 'nullable|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'next_price' => 'nullable|numeric|min:0',
            'is_active' => 'required|boolean',
            'position' => 'required|integer|min:0',
        ]);

        // If this presale is being set as active, check active presales
        if ($request->is_active) {
            $activePresales = Presale::where('is_active', true)->get();

            if ($activePresales->count() > 0) {
                // If the new presale has a start date
                if ($request->start_date) {
                    $newStartDate = \Carbon\Carbon::parse($request->start_date);

                    // Check if any active presale has an end date and if the new start date is after it
                    $canAdd = true;
                    foreach ($activePresales as $activePresale) {
                        // If the active presale is in 'Completed' status, allow adding a new presale regardless of dates
                        if ($activePresale->getStatus() == 'Completed') {
                            continue;
                        }

                        // If active presale has no end date, we can't add a new one
                        if (!$activePresale->end_date) {
                            $canAdd = false;
                            break;
                        }

                        // If new start date is not after active presale end date
                        if (!$newStartDate->gt($activePresale->end_date)) {
                            $canAdd = false;
                            break;
                        }
                    }

                    // If we can't add the presale, show error
                    if (!$canAdd) {
                        $notify[] = ['error', 'Only one presale can be active at a time. Please deactivate the existing active presale or set a start date after the end date of the current active presale.'];
                        return back()->withInput()->withNotify($notify);
                    }

                    // If we can add it, continue (it will be in 'Upcoming' status until its start date)
                } else {
                    // If no start date is provided, allow it but set it as 'TBD'
                    // The presale will be active immediately
                }
            }
        }

        $presale = new Presale();
        $presale->title = $request->title;
        $presale->token_name = $request->token_name;
        $presale->token_symbol = $request->token_symbol;
        $presale->start_date = $request->start_date;
        $presale->end_date = $request->end_date;
        $presale->quantity = $request->quantity;
        $presale->price = $request->price;
        $presale->next_price = $request->next_price;
        $presale->is_active = $request->is_active;
        $presale->position = $request->position;
        $presale->save();

        $notify[] = ['success', 'Presale created successfully'];
        return redirect()->route('admin.presales.index')->withNotify($notify);
    }

    /**
     * Show the form for editing a presale
     */
    public function editPresale($id)
    {
        $pageTitle = 'Edit Presale';
        $presale = Presale::findOrFail($id);
        return view('admin.presales.edit', compact('pageTitle', 'presale'));
    }

    /**
     * Update the specified presale
     */
    public function updatePresale(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'token_name' => 'required|string|max:255',
            'token_symbol' => 'required|string|max:10',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'quantity' => 'nullable|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'next_price' => 'nullable|numeric|min:0',
            'is_active' => 'required|boolean',
            'position' => 'required|integer|min:0',
        ]);

        $presale = Presale::findOrFail($id);

        // If we're activating a presale that wasn't active before
        if ($request->is_active && !$presale->is_active) {
            // Check if any other presale is already active
            $activePresales = Presale::where('is_active', true)
                                    ->where('id', '!=', $id)
                                    ->get();

            if ($activePresales->count() > 0) {
                // If the presale has a start date
                if ($request->start_date) {
                    $newStartDate = \Carbon\Carbon::parse($request->start_date);

                    // Check if any active presale has an end date and if the new start date is after it
                    $canAdd = true;
                    foreach ($activePresales as $activePresale) {
                        // If the active presale is in 'Completed' status, allow adding a new presale regardless of dates
                        if ($activePresale->getStatus() == 'Completed') {
                            continue;
                        }

                        // If active presale has no end date, we can't add a new one
                        if (!$activePresale->end_date) {
                            $canAdd = false;
                            break;
                        }

                        // If new start date is not after active presale end date
                        if (!$newStartDate->gt($activePresale->end_date)) {
                            $canAdd = false;
                            break;
                        }
                    }

                    // If we can't add the presale, show error
                    if (!$canAdd) {
                        $notify[] = ['error', 'Only one presale can be active at a time. Please deactivate the existing active presale or set a start date after the end date of the current active presale.'];
                        return back()->withInput()->withNotify($notify);
                    }

                    // If we can add it, continue (it will be in 'Upcoming' status until its start date)
                } else {
                    // If no start date is provided, allow it but set it as 'TBD'
                    // The presale will be active immediately
                }
            }
        }

        $presale->title = $request->title;
        $presale->token_name = $request->token_name;
        $presale->token_symbol = $request->token_symbol;
        $presale->start_date = $request->start_date;
        $presale->end_date = $request->end_date;
        $presale->quantity = $request->quantity;
        $presale->price = $request->price;
        $presale->next_price = $request->next_price;
        $presale->is_active = $request->is_active;
        $presale->position = $request->position;
        $presale->save();

        $notify[] = ['success', 'Presale updated successfully'];
        return back()->withNotify($notify);
    }

    /**
     * Delete a presale
     */
    public function deletePresale($id)
    {
        $presale = Presale::findOrFail($id);
        $presale->delete();

        $notify[] = ['success', 'Presale deleted successfully'];
        return back()->withNotify($notify);
    }

    /**
     * Display users who have purchased CCL tokens
     */
    public function presalePurchases(Request $request)
    {
        $pageTitle = 'Presale Purchases';
        $search = $request->search;

        // Get all token purchases
        $purchasesQuery = UserTokenPurchase::with(['user', 'presale']);

        // Filter to only show pending token transfers
        $purchasesQuery->where('token_transfer_status', UserTokenPurchase::STATUS_PENDING);

        // Apply search filter if provided
        if ($search) {
            $purchasesQuery->where(function($query) use ($search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('username', 'like', "%{$search}%");
                })
                ->orWhere('token_name', 'like', "%{$search}%")
                ->orWhere('token_symbol', 'like', "%{$search}%")
                ->orWhereHas('user', function($q) use ($search) {
                    $q->where('ccl_token_address', 'like', "%{$search}%");
                });
            });
        }

        $allPurchases = $purchasesQuery->get();

        // Group purchases by user_id and token_symbol
        $groupedPurchases = collect();
        $purchasesByUserAndSymbol = $allPurchases->groupBy(function ($purchase) {
            return $purchase->user_id . '_' . $purchase->token_symbol;
        });

        foreach ($purchasesByUserAndSymbol as $key => $purchases) {
            // Get the user_id from the first purchase
            $userId = $purchases->first()->user_id;
            $user = $purchases->first()->user;
            $tokenSymbol = $purchases->first()->token_symbol;
            $tokenName = $purchases->first()->token_name;

            // Calculate totals
            $totalAmount = $purchases->sum('amount');
            $totalTokens = $purchases->sum('tokens');

            // Check if all purchases have token_transfer_status as completed
            $allCompleted = $purchases->every(function ($purchase) {
                return $purchase->token_transfer_status == UserTokenPurchase::STATUS_COMPLETED;
            });

            // Create a consolidated purchase object
            $groupedPurchases->push([
                'user_id' => $userId,
                'user' => $user,
                'token_symbol' => $tokenSymbol,
                'token_name' => $tokenName,
                'total_amount' => $totalAmount,
                'total_tokens' => $totalTokens,
                'token_transfer_status' => $allCompleted ? UserTokenPurchase::STATUS_COMPLETED : UserTokenPurchase::STATUS_PENDING
            ]);
        }

        // Sort by total amount (descending)
        $sortedPurchases = $groupedPurchases->sortByDesc('total_amount');

        // Paginate the results
        $perPage = 20;
        $page = request()->get('page', 1);
        $offset = ($page - 1) * $perPage;

        $userPurchases = new \Illuminate\Pagination\LengthAwarePaginator(
            $sortedPurchases->slice($offset, $perPage),
            $sortedPurchases->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return view('admin.presales.purchases', compact('pageTitle', 'userPurchases', 'search'));
    }

    /**
     * Display users who have completed presale token transfers
     */
    public function completedPresalePurchases(Request $request)
    {
        $pageTitle = 'Completed Presale';
        $search = $request->search;

        // Get all token purchases
        $purchasesQuery = UserTokenPurchase::with(['user', 'presale']);

        // Filter to only show completed token transfers
        $purchasesQuery->where('token_transfer_status', UserTokenPurchase::STATUS_COMPLETED);

        // Apply search filter if provided
        if ($search) {
            $purchasesQuery->where(function($query) use ($search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('username', 'like', "%{$search}%");
                })
                ->orWhere('token_name', 'like', "%{$search}%")
                ->orWhere('token_symbol', 'like', "%{$search}%")
                ->orWhereHas('user', function($q) use ($search) {
                    $q->where('ccl_token_address', 'like', "%{$search}%");
                });
            });
        }

        $allPurchases = $purchasesQuery->get();

        // Group purchases by user_id and token_symbol
        $groupedPurchases = collect();
        $purchasesByUserAndSymbol = $allPurchases->groupBy(function ($purchase) {
            return $purchase->user_id . '_' . $purchase->token_symbol;
        });

        foreach ($purchasesByUserAndSymbol as $key => $purchases) {
            // Get the user_id from the first purchase
            $userId = $purchases->first()->user_id;
            $user = $purchases->first()->user;
            $tokenSymbol = $purchases->first()->token_symbol;
            $tokenName = $purchases->first()->token_name;

            // Calculate totals
            $totalAmount = $purchases->sum('amount');
            $totalTokens = $purchases->sum('tokens');

            // Create a consolidated purchase object
            $groupedPurchases->push([
                'user_id' => $userId,
                'user' => $user,
                'token_symbol' => $tokenSymbol,
                'token_name' => $tokenName,
                'total_amount' => $totalAmount,
                'total_tokens' => $totalTokens,
                'token_transfer_status' => UserTokenPurchase::STATUS_COMPLETED
            ]);
        }

        // Sort by total amount (descending)
        $sortedPurchases = $groupedPurchases->sortByDesc('total_amount');

        // Paginate the results
        $perPage = 20;
        $page = request()->get('page', 1);
        $offset = ($page - 1) * $perPage;

        $userPurchases = new \Illuminate\Pagination\LengthAwarePaginator(
            $sortedPurchases->slice($offset, $perPage),
            $sortedPurchases->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return view('admin.presales.purchases', compact('pageTitle', 'userPurchases', 'search'));
    }

    /**
     * Complete token transfer for a user's purchases
     */
    public function completeTokenTransfer(Request $request)
    {
        $request->validate([
            'user_id' => 'required|integer|exists:users,id',
            'token_symbol' => 'required|string',
        ]);

        // Update all purchases for this user and token symbol
        UserTokenPurchase::where('user_id', $request->user_id)
            ->where('token_symbol', $request->token_symbol)
            ->update(['token_transfer_status' => UserTokenPurchase::STATUS_COMPLETED]);

        $notify[] = ['success', 'Token transfer status updated to Completed'];
        return back()->withNotify($notify);
    }

    /**
     * Export presale purchases as CSV
     */
    public function exportPresalePurchases(Request $request)
    {
        $search = $request->search;
        $completed = $request->has('completed') ? true : false;

        // Get token purchases with search filter
        $purchasesQuery = UserTokenPurchase::with(['user']);

        // Filter based on token_transfer_status
        if ($completed) {
            $purchasesQuery->where('token_transfer_status', UserTokenPurchase::STATUS_COMPLETED);
        } else {
            $purchasesQuery->where('token_transfer_status', UserTokenPurchase::STATUS_PENDING);
        }

        // Apply search filter if provided
        if ($search) {
            $purchasesQuery->where(function($query) use ($search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('username', 'like', "%{$search}%");
                })
                ->orWhere('token_name', 'like', "%{$search}%")
                ->orWhere('token_symbol', 'like', "%{$search}%")
                ->orWhereHas('user', function($q) use ($search) {
                    $q->where('ccl_token_address', 'like', "%{$search}%");
                });
            });
        }

        $allPurchases = $purchasesQuery->get();

        // Group purchases by user_id and token_symbol
        $groupedPurchases = collect();
        $purchasesByUserAndSymbol = $allPurchases->groupBy(function ($purchase) {
            return $purchase->user_id . '_' . $purchase->token_symbol;
        });

        foreach ($purchasesByUserAndSymbol as $key => $purchases) {
            // Get the user_id from the first purchase
            $userId = $purchases->first()->user_id;
            $user = $purchases->first()->user;
            $tokenSymbol = $purchases->first()->token_symbol;
            $tokenName = $purchases->first()->token_name;

            // Calculate totals
            $totalTokens = $purchases->sum('tokens');

            // Check if all purchases have token_transfer_status as completed
            $allCompleted = $purchases->every(function ($purchase) {
                return $purchase->token_transfer_status == UserTokenPurchase::STATUS_COMPLETED;
            });

            // Create a consolidated purchase object
            $groupedPurchases->push([
                'user_id' => $userId,
                'user' => $user,
                'token_symbol' => $tokenSymbol,
                'token_name' => $tokenName,
                'total_tokens' => $totalTokens,
                'token_transfer_status' => $allCompleted ? UserTokenPurchase::STATUS_COMPLETED : UserTokenPurchase::STATUS_PENDING
            ]);
        }

        // Sort by total tokens (descending)
        $sortedPurchases = $groupedPurchases->sortByDesc('total_tokens');

        $filename = 'presale_purchases_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($sortedPurchases) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, ['Username', 'Token Name', 'Token Symbol', 'Token Amount', 'Token Transfer', 'Token Address']);

            // Add data rows
            foreach ($sortedPurchases as $purchase) {
                $tokenAmount = showAmount($purchase['total_tokens'], exceptZeros: true, currencyFormat: false);
                $tokenAddress = $purchase['user']->ccl_token_address ?? 'Not provided';

                fputcsv($file, [
                    $purchase['user']->username,
                    $purchase['token_name'],
                    $purchase['token_symbol'],
                    $tokenAmount,
                    $purchase['token_transfer_status'],
                    $tokenAddress
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function getChartData(Request $request)
    {
        if ($request->ajax()) {
            $currency = request()->currency;

            if ($request->type == 'transaction') {
                $trxReport['date']             = collect([]);
                $trxReport['plus_trx_amount']  = collect([]);
                $trxReport['minus_trx_amount'] = collect([]);
                $plusTrx                       = Transaction::where('trx_type', '+')
                    ->where('remark', 'deposit')
                    ->where('created_at', '>=', Carbon::now()->subDays(30))
                    ->where('currency', $currency)
                    ->selectRaw("SUM(amount) as amount, DATE_FORMAT(created_at,'%Y-%m-%d') as date")
                    ->orderBy('created_at')
                    ->groupBy('date')
                    ->get();

                $plusTrx->map(function ($trxData) use ($trxReport) {
                    $trxReport['date']->push($trxData->date);
                });

                $minusTrx = Transaction::where('trx_type', '-')
                    ->where('remark', 'withdraw')
                    ->where('created_at', '>=', Carbon::now()->subDays(30))
                    ->where('currency', $currency)
                    ->selectRaw("SUM(amount) as amount, DATE_FORMAT(created_at,'%Y-%m-%d') as date")
                    ->orderBy('created_at')
                    ->groupBy('date')
                    ->get();

                $minusTrx->map(function ($trxData) use ($trxReport) {
                    $trxReport['date']->push($trxData->date);
                });

                $trxReport['date'] = dateSorting($trxReport['date']->unique()->toArray());

                foreach ($trxReport['date'] as $trxDate) {
                    $trxReport['plus_trx_amount']->push(getAmount(@$plusTrx->where('date', $trxDate)->first()->amount));
                    $trxReport['minus_trx_amount']->push(getAmount(@$minusTrx->where('date', $trxDate)->first()->amount));
                }

                $trxReport['plus_trx_amount']  = $trxReport['plus_trx_amount']->toArray();
                $trxReport['minus_trx_amount'] = $trxReport['minus_trx_amount']->toArray();

                return response()->json(['trxReport' => $trxReport, 'currency' => strtoupper($currency)]);
            }


        }
    }
}
