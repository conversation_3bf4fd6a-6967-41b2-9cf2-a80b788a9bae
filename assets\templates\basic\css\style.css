/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
:root {
  --main: #27ae61;
}

html {
  font-size: 100%;
  scroll-behavior: smooth;
}

body {
  background-color: white;
  font-family: "Josefin Sans", sans-serif;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5em;
  color: #777777;
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

a {
  display: inline-block !important;
}

button:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
input:focus,
textarea:focus {
  outline: none;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  border: none;
  cursor: pointer;
}

input,
textarea {
  padding: 15px 25px;
  width: 100%;
}

span {
  display: inline-block;
}


a,
a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
}

*::-webkit-scrollbar-track {
  -webkit-box-shadow: inset -5px -5px 8px -1px rgba(255, 255, 255, 0.7), 5px 5px 8px -1px rgba(0, 0, 0, 0.065);
  box-shadow: inset -5px -5px 8px -1px rgba(255, 255, 255, 0.7), 5px 5px 8px -1px rgba(0, 0, 0, 0.065);
  background-color: #eaf7e4;
}

*::-webkit-scrollbar {
  width: 3px;
  background-color: #eaf7e4;
}

*::-webkit-scrollbar-button {
  background-color: var(--main);
}

*::-webkit-scrollbar-thumb {
  background-color: var(--main);
}

/*-------------------------------------------------
    [ ## Heading ]
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  clear: both;
  line-height: 1.2em;
  color: #292929;
  -webkit-font-smoothing: antialiased;
  text-transform: capitalize;
}

h1 {
  font-size: 2.441em;
  font-family: "Nunito", sans-serif;
  font-weight: 700;
}

h2 {
  font-size: 30px;
  font-family: "Nunito", sans-serif;
  font-weight: 800;
}

h3 {
  font-size: 24px;
  font-family: "Nunito", sans-serif;
  font-weight: 800;
}

h4 {
  font-size: 20px;
  font-family: "Nunito", sans-serif;
  font-weight: 600;
}

h5 {
  font-size: 1em;
}

h6 {
  font-size: 0.8em;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a {
  color: inherit;
  text-decoration: none;
}

h1 a:hover,
h2 a:hover,
h3 a:hover,
h4 a:hover {
  color: inherit;
  text-decoration: none;
}

.heading {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.25em;
  margin-bottom: 15px;
}

@media only screen and (max-width: 767px) {
  .heading {
    font-size: 22px;
  }
}

.section-heading {
  font-weight: 700;
  font-size: 30px;
  position: relative;
  z-index: 2;
  line-height: 1.25em;
  margin-bottom: 30px;
  margin-top: -7px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding-bottom: 20px;
}

@media only screen and (max-width: 767px) {
  .section-heading {
    font-size: 24px;
    padding-bottom: 10px;
    margin-top: -5px;
  }
}

.section-heading:before {
  position: absolute;
  content: "";
  height: 3px;
  width: 70px;
  background: linear-gradient(to right, #272d43, transparent);
  bottom: -4px;
  left: 0%;
}

.section-header {
  margin-bottom: 50px;
}

.section-header .sub-title {
  color: var(--main);
  font-size: 30px;
  letter-spacing: 3px;
  font-weight: 500;
  margin-bottom: 20px;
  position: relative;
  font-family: "Nunito", sans-serif;
}

.section-title {
  position: relative;
  font-weight: 800;
  margin-bottom: 20px;
  margin-top: -10px;
}

.section-title span {
  color: var(--main);
}

/*-------------------------------------------------
    [ ## Others Typography ]
*/
p {
  margin-bottom: 15px;
  line-height: 1.8em;
}

p:last-child {
  margin-bottom: 0px;
}

@media only screen and (max-width: 1199px) {
  p {
    line-height: 1.7em;
  }
}

a {
  display: block;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

blockquote {
  margin: 0 0 1.3em;
}

img {
  max-width: 100%;
  height: auto;
}

/*--------------------------------------------------------------
# Elements
--------------------------------------------------------------*/
/*-------------------------------------------------
    [ ## Margin Element ]
*/
.mr-0 {
  margin: 0;
}

.mrt-0 {
  margin-top: 0;
}

.mrb-0 {
  margin-bottom: 0;
}

.mrl-0 {
  margin-left: 0;
}

.mrr-0 {
  margin-right: 0;
}

.mrt-5 {
  margin-top: 5px;
}

.mrb-5 {
  margin-bottom: 5px;
}

.mrl-5 {
  margin-left: 5px;
}

.mrr-5 {
  margin-right: 5px;
}

.mrt-10 {
  margin-top: 10px;
}

.mrb-10 {
  margin-bottom: 10px;
}

.mrl-10 {
  margin-left: 10px;
}

.mrr-10 {
  margin-right: 10px;
}

.mrt-15 {
  margin-top: 15px;
}

.mrb-15 {
  margin-bottom: 15px;
}

.mrl-15 {
  margin-left: 15px;
}

.mrr-15 {
  margin-right: 15px;
}

.mrt-20 {
  margin-top: 20px;
}

.mrb-20 {
  margin-bottom: 20px;
}

.mrl-20 {
  margin-left: 20px;
}

.mrr-20 {
  margin-right: 20px;
}

.mrt-25 {
  margin-top: 25px;
}

.mrb-25 {
  margin-bottom: 25px;
}

.mrl-25 {
  margin-left: 25px;
}

.mrr-25 {
  margin-right: 25px;
}

.mrt-30 {
  margin-top: 30px;
}

.mrb-30 {
  margin-bottom: 30px;
}

.mrl-30 {
  margin-left: 30px;
}

.mrr-30 {
  margin-right: 30px;
}

.mrt-35 {
  margin-top: 35px;
}

.mrb-35 {
  margin-bottom: 35px;
}

.mrb-80 {
  margin-bottom: 80px;
}

.mrl-35 {
  margin-left: 35px;
}

.mrr-35 {
  margin-right: 35px;
}

.mrt-40 {
  margin-top: 40px;
}

.mrb-40 {
  margin-bottom: 40px;
}

.mrl-40 {
  margin-left: 40px;
}

.mrr-40 {
  margin-right: 40px;
}

.mrt-45 {
  margin-top: 45px;
}

.mrb-45 {
  margin-bottom: 45px;
}

.mrl-45 {
  margin-left: 45px;
}

.mrr-45 {
  margin-right: 45px;
}

.mrt-50 {
  margin-top: 50px;
}

.mrb-50 {
  margin-bottom: 50px;
}

.mrl-50 {
  margin-left: 50px;
}

.mrr-50 {
  margin-right: 50px;
}

.mrt-55 {
  margin-top: 55px;
}

.mrb-55 {
  margin-bottom: 55px;
}

.mrl-55 {
  margin-left: 55px;
}

.mrr-55 {
  margin-right: 55px;
}

.mrt-60 {
  margin-top: 60px;
}

.mrb-60 {
  margin-bottom: 60px;
}

.mrl-60 {
  margin-left: 60px;
}

.mrr-60 {
  margin-right: 60px;
}

.mrt-75 {
  margin-top: 75px;
}

.mrt-65 {
  margin-top: 65px;
}

.mrb-65 {
  margin-bottom: 65px;
}

.mrl-65 {
  margin-left: 65px;
}

.mrr-65 {
  margin-right: 60px;
}

.mrt-70 {
  margin-top: 70px;
}

.mrb-70 {
  margin-bottom: 70px;
}

.mrl-70 {
  margin-left: 70px;
}

.mrr-70 {
  margin-right: 70px;
}

.mrt-75 {
  margin-top: 75px;
}

.mrb-75 {
  margin-bottom: 75px;
}

.mrl-75 {
  margin-left: 75px;
}

.mrr-75 {
  margin-right: 75px;
}

.mrt-80 {
  margin-top: 80px;
}

.mrb-80 {
  margin-bottom: 80px;
}

.mrb-85 {
  margin-bottom: 85px;
}

.mrl-80 {
  margin-left: 80px;
}

.mrr-80 {
  margin-right: 80px;
}

.mrt-90 {
  margin-top: 90px;
}

.mrb-90 {
  margin-bottom: 90px;
}

.mrl-90 {
  margin-left: 90px;
}

.mrr-90 {
  margin-right: 90px;
}

.mrt-100 {
  margin-top: 100px;
}

.mrb-100 {
  margin-bottom: 100px;
}

.mrl-100 {
  margin-left: 100px;
}

.mrr-100 {
  margin-right: 100px;
}

.mrt-105 {
  margin-top: 105px;
}

.mrb-105 {
  margin-bottom: 105px;
}

.mrl-105 {
  margin-left: 105px;
}

.mrr-105 {
  margin-right: 105px;
}

.mrt-120 {
  margin-top: 120px;
}

.mrb-120 {
  margin-bottom: 120px;
}

@media only screen and (max-width: 991px) {
  .mrb-120 {
    margin-bottom: 0;
  }
}

.mrl-120 {
  margin-left: 120px;
}

.mrr-120 {
  margin-right: 120px;
}

.mrb-145 {
  margin-bottom: 145px;
}

.mrt-150 {
  margin-top: 150px;
}

.mrt-200 {
  margin-top: 200px;
}

.mrb-150 {
  margin-bottom: 150px;
}

.mrb-155 {
  margin-bottom: 155px;
}

.mrb-170 {
  margin-bottom: 170px;
}

.mrb-175 {
  margin-bottom: 175px;
}

.mrl-150 {
  margin-left: 150px;
}

.mrr-150 {
  margin-right: 150px;
}

.mrb-185 {
  margin-bottom: 185px;
}

.mrb-190 {
  margin-bottom: 190px;
}

.mrb-195 {
  margin-bottom: 195px;
}

.mrb-200 {
  margin-bottom: 200px;
}

@media only screen and (max-width: 991px) {
  .md-mrt-60 {
    margin-top: 60px;
  }

  .md-mrb-60 {
    margin-bottom: 60px;
  }

  .md-mrt-55 {
    margin-top: 55px;
  }

  .md-mrb-55 {
    margin-bottom: 55px;
  }

  .md-mrt-50 {
    margin-top: 50px;
  }

  .md-mrb-50 {
    margin-bottom: 50px;
  }
}

/* Margin Top Negative Value */
.ml-t-5 {
  margin-top: -5px;
}

.ml-t-8 {
  margin-top: -8px;
}

.ml-t-058p {
  margin-top: -0.58%;
}

.ml-t-10 {
  margin-top: -10px;
}

.ml-t-15 {
  margin-top: -15px;
}

.ml-t-20 {
  margin-top: -20px;
}

.ml-t-25 {
  margin-top: -25px;
}

.ml-t-30 {
  margin-top: -30px;
}

.ml-t-35 {
  margin-top: -35px;
}

.ml-t-40 {
  margin-top: -40px;
}

.ml-t-45 {
  margin-top: -45px;
}

.ml-t-50 {
  margin-top: -50px;
}

.ml-t-55 {
  margin-top: -55px;
}

.ml-t-60 {
  margin-top: -60px;
}

.ml-t-75 {
  margin-top: -75px;
}

.ml-t-90 {
  margin-top: -90px;
}

.ml-t-105 {
  margin-top: -105px;
}

.ml-t-120 {
  margin-top: -120px;
}

.ml-t-135 {
  margin-top: -135px;
}

.ml-t-150 {
  margin-top: -150px;
}

.ml-t-165 {
  margin-top: -165px;
}

.ml-t-180 {
  margin-top: -180px;
}

.ml-t-195 {
  margin-top: -195px;
}

.ml-t-210 {
  margin-top: -210px;
}

.ml-t-225 {
  margin-top: -225px;
}

/* Margin bottom Negtive value */
.ml-b-5 {
  margin-bottom: -5px;
}

.ml-b-10 {
  margin-bottom: -10px;
}

.ml-b-15 {
  margin-bottom: -15px;
}

.ml-b-20 {
  margin-bottom: -20px;
}

.ml-b-25 {
  margin-bottom: -25px;
}

.ml-b-30 {
  margin-bottom: -30px;
}

.ml-b-35 {
  margin-bottom: -35px;
}

.ml-b-40 {
  margin-bottom: -40px;
}

.ml-b-45 {
  margin-bottom: -45px;
}

.ml-b-50 {
  margin-bottom: -50px;
}

.ml-b-55 {
  margin-bottom: -55px;
}

.ml-b-60 {
  margin-bottom: -60px;
}

.ml-b-75 {
  margin-bottom: -75px;
}

.ml-b-80 {
  margin-bottom: -80px;
}

.ml-b-90 {
  margin-bottom: -90px;
}

.ml-b-105 {
  margin-bottom: -105px;
}

.ml-b-120 {
  margin-bottom: -120px;
}

.ml-b-135 {
  margin-bottom: -135px;
}

.ml-b-150 {
  margin-bottom: -150px;
}

@media only screen and (max-width: 991px) {
  .ml-b-150 {
    margin-bottom: -30px;
  }
}

.ml-bp-0933 {
  margin-bottom: -0.933%;
}

/* Margin for Responsive value */
@media only screen and (max-width: 767px) {
  .mrb-m-30 {
    margin-bottom: 30px;
  }

  .mrb-m-45 {
    margin-bottom: 45px;
  }

  .mrb-m-60 {
    margin-bottom: 60px;
  }

  .mr-m-0 {
    margin: 0;
  }
}

/*-------------------------------------------------
    [ ## Padding Element ]
*/
.pd-0 {
  padding: 0;
}

.pd-t-0 {
  padding-top: 0;
}

.pd-b-0 {
  padding-bottom: 0;
}

.pd-l-0 {
  padding-left: 0;
}

.pd-r-0 {
  padding-right: 0;
}

.pd-t-5 {
  padding-top: 5px;
}

.pd-b-5 {
  padding-bottom: 5px;
}

.pd-l-5 {
  padding-left: 5px;
}

.pd-r-5 {
  padding-right: 5px;
}

.pd-t-10 {
  padding-top: 10px;
}

.pd-b-10 {
  padding-bottom: 10px;
}

.pd-l-10 {
  padding-left: 10px;
}

.pd-r-10 {
  padding-right: 10px;
}

.pd-t-15 {
  padding-top: 15px;
}

.pd-b-15 {
  padding-bottom: 15px;
}

.pd-l-15 {
  padding-left: 15px;
}

.pd-r-15 {
  padding-right: 15px;
}

.pd-t-20 {
  padding-top: 20px;
}

.pd-b-20 {
  padding-bottom: 20px;
}

.pd-l-20 {
  padding-left: 20px;
}

.pd-r-20 {
  padding-right: 20px;
}

.pd-t-25 {
  padding-top: 25px;
}

.pd-b-25 {
  padding-bottom: 25px;
}

.pd-l-25 {
  padding-left: 25px;
}

.pd-r-25 {
  padding-right: 25px;
}

.pd-t-30 {
  padding-top: 30px;
}

.pd-b-30 {
  padding-bottom: 30px;
}

.pd-l-30 {
  padding-left: 30px;
}

.pd-r-30 {
  padding-right: 30px;
}

.pd-t-35 {
  padding-top: 35px;
}

.pd-b-35 {
  padding-bottom: 35px;
}

.pd-l-35 {
  padding-left: 35px;
}

.pd-r-35 {
  padding-right: 35px;
}

.pd-t-40 {
  padding-top: 40px;
}

.pd-b-40 {
  padding-bottom: 40px;
}

.pd-l-40 {
  padding-left: 40px;
}

.pd-r-40 {
  padding-right: 40px;
}

.pd-t-45 {
  padding-top: 45px;
}

.pd-b-45 {
  padding-bottom: 45px;
}

.pd-l-45 {
  padding-left: 45px;
}

.pd-r-45 {
  padding-right: 45px;
}

.pd-t-50 {
  padding-top: 50px;
}

.pd-b-50 {
  padding-bottom: 50px;
}

.pd-l-50 {
  padding-left: 50px;
}

.pd-r-50 {
  padding-right: 50px;
}

.pd-t-60 {
  padding-top: 60px;
}

.pd-b-60 {
  padding-bottom: 60px;
}

.pd-l-60 {
  padding-left: 60px;
}

.pd-r-60 {
  padding-right: 60px;
}

.pd-t-70 {
  padding-top: 70px;
}

.pd-b-70 {
  padding-bottom: 70px;
}

.pd-l-70 {
  padding-left: 70px;
}

.pd-r-70 {
  padding-right: 70px;
}

.pd-t-75 {
  padding-top: 75px;
}

.pd-b-75 {
  padding-bottom: 75px;
}

.pd-l-75 {
  padding-left: 75px;
}

.pd-r-75 {
  padding-right: 75px;
}

.ptb-80 {
  padding: 80px 0;
}

.pd-t-80 {
  padding-top: 80px;
}

.pd-b-80 {
  padding-bottom: 80px;
}

.pd-l-80 {
  padding-left: 80px;
}

.pd-r-80 {
  padding-right: 80px;
}

.pd-t-90 {
  padding-top: 90px;
}

.pd-b-90 {
  padding-bottom: 90px;
}

.pd-l-90 {
  padding-left: 90px;
}

.pd-r-90 {
  padding-right: 90px;
}

.pd-t-100 {
  padding-top: 100px;
}

.pd-b-100 {
  padding-bottom: 100px;
}

.pd-l-100 {
  padding-left: 100px;
}

.pd-r-100 {
  padding-right: 100px;
}

.pd-t-105 {
  padding-top: 105px;
}

.pd-b-105 {
  padding-bottom: 105px;
}

.pd-l-105 {
  padding-left: 105px;
}

.pd-r-105 {
  padding-right: 105px;
}

.pd-t-120 {
  padding-top: 120px;
}

@media only screen and (max-width: 991px) {
  .pd-t-120 {
    padding-top: 100px;
  }
}

.pd-t-150 {
  padding-top: 150px;
}

@media only screen and (max-width: 991px) {
  .pd-t-150 {
    padding-top: 100px !important;
  }
}

.pd-b-120 {
  padding-bottom: 120px;
}

@media only screen and (max-width: 991px) {
  .pd-b-120 {
    padding-bottom: 100px;
  }
}

.pd-l-120 {
  padding-left: 120px;
}

.pd-r-120 {
  padding-right: 120px;
}

.ptb-120 {
  padding: 120px 0;
}

@media only screen and (max-width: 991px) {
  .ptb-120 {
    padding: 100px 0;
  }
}

.ptb-150 {
  padding: 150px 0;
}

@media only screen and (max-width: 991px) {
  .ptb-150 {
    padding: 100px 0;
  }
}

.pd-t-135 {
  padding-top: 135px;
}

.pd-b-135 {
  padding-bottom: 135px;
}

.pd-l-135 {
  padding-left: 135px;
}

.pd-r-135 {
  padding-right: 135px;
}

.pd-t-140 {
  padding-top: 140px;
}

.pd-b-140 {
  padding-bottom: 140px;
}

.pd-l-140 {
  padding-left: 140px;
}

.pd-r-140 {
  padding-right: 140px;
}

.pd-t-150 {
  padding-top: 150px;
}

.pd-b-150 {
  padding-bottom: 150px;
}

.pd-l-150 {
  padding-left: 150px;
}

.pd-r-150 {
  padding-right: 150px;
}

.pd-t-165 {
  padding-top: 165px;
}

.pd-b-165 {
  padding-bottom: 165px;
}

.pd-l-165 {
  padding-left: 165px;
}

.pd-r-165 {
  padding-right: 165px;
}

.pd-t-180 {
  padding-top: 180px;
}

.pd-b-170 {
  padding-bottom: 170px;
}

.pd-b-180 {
  padding-bottom: 180px;
}

.pd-l-180 {
  padding-left: 180px;
}

.pd-r-180 {
  padding-right: 180px;
}

.pd-t-195 {
  padding-top: 195px;
}

.pd-b-195 {
  padding-bottom: 195px;
}

.pd-l-195 {
  padding-left: 195px;
}

.pd-r-195 {
  padding-right: 195px;
}

.pd-t-200 {
  padding-top: 200px;
}

.pd-b-200 {
  padding-bottom: 200px;
}

.pd-l-200 {
  padding-left: 200px;
}

.pd-r-200 {
  padding-right: 200px;
}

.pd-t-205 {
  padding-top: 205px;
}

.pd-b-205 {
  padding-bottom: 205px;
}

.pd-l-205 {
  padding-left: 205px;
}

.pd-r-205 {
  padding-right: 205px;
}

.pd-t-210 {
  padding-top: 210px;
}

.pd-t-220 {
  padding-top: 220px;
}

.pd-b-220 {
  padding-bottom: 220px;
}

.pd-l-220 {
  padding-left: 220px;
}

.pd-r-220 {
  padding-right: 220px;
}

.pd-t-225 {
  padding-top: 225px;
}

.pd-b-225 {
  padding-bottom: 225px;
}

.pd-l-225 {
  padding-left: 225px;
}

.pd-r-225 {
  padding-right: 225px;
}

.pd-t-235 {
  padding-top: 235px;
}

.pd-b-235 {
  padding-bottom: 235px;
}

.pd-l-235 {
  padding-left: 235px;
}

.pd-r-235 {
  padding-right: 220px;
}

.pd-t-240 {
  padding-top: 240px;
}

.pd-t-255 {
  padding-top: 255px;
}

@media only screen and (max-width: 992px) {
  .pm-tb-90 {
    padding-top: 90px;
    padding-bottom: 90px;
  }
}

@media only screen and (max-width: 992px) {
  .pm-b-90 {
    padding-bottom: 90px;
  }
}

@media only screen and (max-width: 992px) {
  .pm-t-0 {
    padding-top: 0;
  }
}

@media only screen and (max-width: 992px) {
  .pdmX-60 {
    padding: 60px;
  }
}

@media only screen and (max-width: 767px) {
  .pdms-30 {
    padding: 30px;
  }
}

/*-------------------------------------------------
    [ ## Overlay Element ]
*/
.bg_img {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.bg-image {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
}

.bg-fixed {
  background-attachment: fixed;
}

.bg-overlay-primary {
  position: relative;
}

.bg-overlay-primary:before {
  content: "";
  position: absolute;
  background-color: #272d43;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  opacity: 0.88;
}

.bg-overlay-primary>div {
  position: relative;
  z-index: 2;
}

.bg-overlay-main {
  position: relative;
}

.bg-overlay-main:before {
  content: "";
  position: absolute;
  background-color: #272d43;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  opacity: 0.8;
}

.bg-overlay-main>div {
  position: relative;
  z-index: 2;
}

.bg-overlay-primary {
  position: relative;
}

.bg-overlay-primary:before {
  content: "";
  position: absolute;
  background-color: #272d43;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  opacity: 0.84;
}

.bg-overlay-primary>div {
  position: relative;
  z-index: 2;
}

.bg-overlay-black {
  position: relative;
}

.bg-overlay-black:before {
  content: "";
  position: absolute;
  background-color: black;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  opacity: 0.01;
}

.bg-overlay-black>div {
  position: relative;
  z-index: 2;
}

.bg-overlay-black-two {
  position: relative;
}

.bg-overlay-black-two:before {
  content: "";
  position: absolute;
  background-color: black;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  opacity: 0.6;
}

.bg-overlay-black-three {
  position: relative;
}

.bg-overlay-black-three:before {
  content: "";
  position: absolute;
  background-color: black;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  opacity: 0.4;
}

.bg-overlay-black-three>div {
  position: relative;
  z-index: 2;
}


.body-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  content: "";
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.738);
  z-index: 99;
  transition: 0.2s linear;
  visibility: hidden;
  opacity: 0;
}

.body-overlay.show {
  visibility: visible;
  opacity: 1;
}

/*-------------------------------------------------
    [ ## Lists ]
*/
li>ul,
li>ol {
  margin-bottom: 0;
  margin-left: 1em;
}

/*-------------------------------------------------
    [ ## Post, Page, Comments Table ]
*/
body:not(.woocommerce-page) .article-content table,
body:not(.bbpress) .article-content table,
body:not(.buddypress) .article-content table {
  margin-bottom: 30px;
}

body:not(.woocommerce-page) .article-content table td,
body:not(.woocommerce-page) .article-content table th,
body:not(.bbpress) .article-content table td,
body:not(.bbpress) .article-content table th,
body:not(.buddypress) .article-content table td,
body:not(.buddypress) .article-content table th {
  border: 1px solid #dddddd;
  padding: 15px;
}

.comment-content table {
  margin-bottom: 30px;
}

.comment-content table td,
.comment-content table th {
  border: 1px solid #dddddd;
  padding: 15px;
}

.scrollToTop {
  position: fixed;
  bottom: 0;
  right: 30px;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: var(--main);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  color: #fff;
  line-height: 50px;
  font-size: 20px;
  text-align: center;
  z-index: 99;
  cursor: pointer;
  transition: all 1s;
  transform: translateY(100%);
}

.scrollToTop.active {
  bottom: 30px;
  transform: translateY(0%);
  color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.scrollToTop:hover {
  color: #fff;
}

/*-------------------------------------------
  -- Animations
-------------------------------------------*/
[data-animate] {
  opacity: 0;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: .6s;
  animation-duration: .6s;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
}

/*-------------------------------------------------
    [ ## Buttons ]
*/
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

input[type="submit"]:hover {
  color: white;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"],
input[type="button"],
input[type="reset"],
input[type="submit"] {
  cursor: pointer;
  -webkit-appearance: button;
}

button:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus {
  outline: none;
}

button::-moz-focus-inner,
input[type="button"]::-moz-focus-inner,
input[type="reset"]::-moz-focus-inner,
input[type="submit"]::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}

.btn {
  -webkit-transition: all 0.3s ease 0.02s;
  transition: all 0.3s ease 0.02s;
}

.btn:active,
.btn:focus {
  box-shadow: 0 0px 0px rgba(0, 0, 0, 0.125) inset;
}

.btn {
  border: 0px solid;
  border-radius: 0px;
  font-weight: 700;
  font-size: 16px;
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

@media only screen and (max-width: 767px) {
  .btn {
    font-size: 14px;
  }
}

.cmn-btn-active {
  position: relative;
  background: white;
  color: #292929;
  padding: 8px 25px;
  text-transform: capitalize;
  box-shadow: 7px 5px 30px 0px rgb(72 73 121 / 15%);
  font-family: "Josefin Sans", sans-serif;
  border-radius: 3px;
  font-size: 16px;
  font-weight: 500;
  z-index: 2;
  overflow: hidden;
  -webkit-transition: all ease 0.5s;
  -moz-transition: all ease 0.5s;
  transition: all ease 0.5s;
}

.cmn-btn-danger {
  position: relative;
  background: #f75959;
  color: #fff;
  padding: 9px 25px !important;
  text-transform: capitalize;
  box-shadow: 7px 5px 30px 0px rgb(72 73 121 / 15%);
  font-family: "Josefin Sans", sans-serif;
  border-radius: 3px;
  font-size: 16px;
  font-weight: 500;
  z-index: 2;
  overflow: hidden;
  -webkit-transition: all ease 0.5s;
  -moz-transition: all ease 0.5s;
  transition: all ease 0.5s;
}

.cmn-btn-danger:focus {
  color: white;
}

@media (max-width: 767px) {
  .cmn-btn-danger {
    padding: 7px 15px !important;
  }
}

@media (max-width: 424px) {
  .cmn-btn-danger {
    padding: 6px 8px !important;
  }
}

.cmn-btn-danger:hover {
  color: #fff;
}

.cmn-btn-active::before {
  right: 50%;
}

.cmn-btn-active::after {
  left: 50%;
}

.cmn-btn-active::before,
.cmn-btn-active::after {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  content: '';
  background-color: white;
  z-index: -1;
  -webkit-transition: all ease 0.5s;
  -moz-transition: all ease 0.5s;
  transition: all ease 0.5s;
}

.cmn-btn-active:focus,
.cmn-btn-active:hover {
  color: white;
  background-color: var(--main);
}

.cmn-btn-active:focus::before,
.cmn-btn-active:hover::before {
  right: 100%;
}

.cmn-btn-active:focus::after,
.cmn-btn-active:hover::after {
  left: 100%;
}

.cmn-btn {
  border: 1px solid var(--main) !important;
  position: relative;
  background: var(--main);
  color: white;
  padding: 8px 25px;
  text-transform: capitalize;
  box-shadow: 7px 5px 30px 0px rgb(72 73 121 / 15%);
  border-radius: 0;
  font-family: "Josefin Sans", sans-serif;
  border-radius: 3px;
  font-size: 16px;
  font-weight: 500;
  z-index: 2;
  overflow: hidden;
  -webkit-transition: all ease 0.5s;
  -moz-transition: all ease 0.5s;
  transition: all ease 0.5s;
}


.cmn-btn::before {
  right: 50%;
}

.cmn-btn::after {
  left: 50%;
}

.cmn-btn::before,
.cmn-btn::after {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  content: '';
  background-color: var(--main);
  z-index: -1;
  -webkit-transition: all ease 0.5s;
  -moz-transition: all ease 0.5s;
  transition: all ease 0.5s;
}

.cmn-btn i {
  margin-right: 10px;
}

.cmn-btn:focus,
.cmn-btn:hover {
  color: var(--main);
  background-color: white;
}

.cmn-btn:focus::before,
.cmn-btn:hover::before {
  right: 100%;
}

.cmn-btn:focus::after,
.cmn-btn:hover::after {
  left: 100%;
}

.custom-btn {
  color: var(--main);
  font-weight: 600;
  font-family: "Josefin Sans", sans-serif;
  display: block !important;
  font-size: 16px;
  transition: all 0.3s;
}

.custom-btn:hover {
  color: var(--main);
}

/*-------------------------------------------------
    [ ## Fields ]
*/
input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus {
  outline: none;
}

input[type=time]::-webkit-inner-spin-button,
input[type=time]::-webkit-outer-spin-button,
input[type=date]::-webkit-inner-spin-button,
input[type=date]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

input[type="search"] {
  -webkit-appearance: textfield;
}

input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

input,
textarea {
  padding: 12px 20px;
}

input::placeholder,
textarea::placeholder {
  color: #777777;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px transparent inset;
  -webkit-text-fill-color: #777777 !important;
}

input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px transparent inset;
  -webkit-text-fill-color: #777777 !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition: background-color 5000s ease-in-out 0s;
  transition: background-color 5000s ease-in-out 0s;
}

textarea {
  display: block;
  width: 100%;
  display: block;
  min-height: 120px;
}

input,
select,
textarea {
  border: 1px solid #e5e5e5;
  vertical-align: baseline;
  border-radius: 3px;
  font-size: 100%;
  color: #777777;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

label {
  font-size: 16px;
  color: #2a2a2a;
  margin-bottom: 10px;
  display: block;
}

::-webkit-input-placeholder {
  color: #b3b3b3;
}

.widget_newsletter::-webkit-input-placeholder {
  color: #808080;
}

:-moz-placeholder {
  color: #b3b3b3;
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

::-moz-placeholder {
  color: #b3b3b3;
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

:-ms-input-placeholder {
  color: #b3b3b3;
}

.nice-select .list {
  background-color: #fff;
  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
  box-sizing: border-box;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  pointer-events: none;
  position: absolute;
  top: 100%;
  left: 0;
  -webkit-transform-origin: 50% 0;
  transform-origin: 50% 0;
  -webkit-transform: scale(0.75) translateY(-21px);
  transform: scale(0.75) translateY(-21px);
  -webkit-transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  z-index: 9;
  min-height: 50px;
  max-height: 200px;
}

.nice-select .list:hover .option:not(:hover) {
  background-color: transparent !important;
}

.nice-select.open .list {
  opacity: 1;
  pointer-events: auto;
  -webkit-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: unset;
  min-height: 50px;
  max-height: 200px;
  overflow-y: scroll;
}

.nice-select.open .nice-select.disabled {
  border-color: #ededed;
  color: #999;
  pointer-events: none;
}

.nice-select .option {
  cursor: pointer;
  font-weight: 400;
  line-height: 40px;
  list-style: none;
  min-height: 40px;
  outline: none;
  padding-left: 18px;
  padding-right: 29px;
  text-align: left;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  font-size: 12px;
}

.nice-select .option.disabled {
  background-color: transparent;
  color: #999;
  cursor: default;
}

.nice-select .option.disabled:after {
  border-color: #cccccc;
}

.nice-select .option:hover {
  background-color: #f6f6f6;
  color: #777777;
}

.nice-select .current {
  display: block;
  color: #777777;
}

.select-custom:focus {
  box-shadow: none;
  outline: none;
  border-radius: 0px;
  background: none;
  color: #727272;
}

.form-group {
  margin-bottom: 20px;
}

.form-controller {
  border: 1px solid #e5e5e5;
  background-color: white;
  width: 100%;
  height: 50px;
  outline: none;
  padding: 5px 20px;
  color: black;
}

.form-controller:focus {
  color: #292929;
  border-color: #272d43;
}

@media only screen and (max-width: 575px) {
  .form-controller {
    padding: 5px 15px;
  }
}

textarea.form-controller {
  padding-top: 15px;
}

input[type="radio"] {
  display: none;
}

input[type="radio"]+label {
  color: #1d2331;
  position: relative;
  padding-left: 23px;
  line-height: 1.2em;
  text-transform: capitalize;
  cursor: pointer;
}

input[type="radio"]+label:before {
  border: 3px solid #B9BEC0;
  content: "";
  height: 14px;
  left: 1px;
  position: absolute;
  top: 3px;
  -webkit-transition: all 0.15s ease-in 0s;
  transition: all 0.15s ease-in 0s;
  width: 14px;
  background-color: white;
  border-radius: 50%;
}

input[type="radio"]:checked+label:before {
  border-color: var(--main);
  background-color: #fff;
}

/*--------------------------------------------------------------
# Header Content
--------------------------------------------------------------*/
/*-------------------------------------------------
    [ ## Preloader ]
*/
.preloader-area {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #272d43;
  z-index: 99999;
}

.jump {
  animation: jump 1.5s infinite ease;
}

@keyframes jump {
  0% {
    top: 0;
  }

  50% {
    top: -40px;
  }

  100% {
    top: 0;
  }
}

.coin {
  margin: auto;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  height: 150px;
  width: 150px;
}

@media(max-width: 767px) {
  .coin {
    height: 100px !important;
    width: 100px !important;
  }

  .coin .front,
  .coin .back {
    height: 100px !important;
    width: 100px !important;
  }

  .coin .front:before,
  .coin .back:before {
    margin: 22px 22px !important;
    width: 40px !important;
    height: 40px !important;
  }

  .coin .front .currency,
  .coin .back .currency {
    font-size: 23px !important;
  }

  .coin .front .shapes div,
  .coin .back .shapes div {
    width: 10px !important;
    height: 2px !important;
    margin: 40px 7px !important;
  }

  .coin .front .shapes div:before,
  .coin .back .shapes div:before {
    width: 10px !important;
    height: 2px !important;
    margin: -7px 0 !important;
  }

  .coin .front .shapes div:after,
  .coin .back .shapes div:after {
    width: 10px !important;
    height: 2px !important;
    margin: 3px 0 !important;
  }

  .coin .front .shapes div,
  .coin .back .shapes div {
    width: 10px !important;
    height: 2px !important;
    margin: 40px 7px !important;
  }

  .coin .front .top,
  .coin .back .top {
    font-size: 10px !important;
    top: 0px !important;
  }

  .coin .front .bottom,
  .coin .back .bottom {
    font-size: 10px !important;
    bottom: 4px !important;
  }

  .coin .shadow {
    margin: 112px 7px 0 3px !important;
  }
}

.coin .front,
.coin .back {
  position: absolute;
  height: 150px;
  width: 150px;
  background: #ffbd0b;
  border-radius: 50%;
  border-top: 7px solid #ffd84c;
  border-left: 7px solid #ffd84c;
  border-right: 7px solid #d57e08;
  border-bottom: 7px solid #d57e08;
  transform: rotate(44deg);
}

.coin .front:before,
.coin .back:before {
  content: "";
  margin: 35.5px 35.5px;
  position: absolute;
  width: 70px;
  height: 70px;
  background: #f0a608;
  border-radius: 50%;
  border-bottom: 5px solid #ffd84c;
  border-right: 5px solid #ffd84c;
  border-left: 5px solid #d57e08;
  border-top: 5px solid #d57e08;
  z-index: 2;
}

.coin .front .currency,
.coin .back .currency {
  overflow: hidden;
  position: absolute;
  color: #ffbd0b;
  font-size: 40px;
  transform: rotate(-44deg);
  line-height: 3.7;
  width: 100%;
  height: 100%;
  text-align: center;
  text-shadow: 0 3px 0 #cb7407;
  z-index: 3;
  border-radius: 50%;
}

.coin .front .currency:after,
.coin .back .currency:after {
  content: "";
  position: absolute;
  height: 200px;
  width: 40px;
  margin: 20px -65px;
  box-shadow: 50px -23px 0 -10px rgba(255, 255, 255, 0.22), 85px -10px 0 -16px rgba(255, 255, 255, 0.19);
  transform: rotate(-50deg);
  animation: shine 1.5s infinite ease;
}

@keyframes shine {
  0% {
    margin: 20px -65px;
  }

  50% {
    margin: 70px -85px;
  }

  100% {
    margin: 20px -65px;
  }
}

.coin .front .shapes,
.coin .back .shapes {
  transform: rotate(-44deg);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.coin .front .shapes div,
.coin .back .shapes div {
  width: 20px;
  height: 4px;
  background: #d57e08;
  border-top: 2px solid #c47207;
  margin: 75px 7px;
}

.coin .front .shapes div:before,
.coin .back .shapes div:before {
  content: "";
  position: absolute;
  width: 20px;
  height: 4px;
  background: #d57e08;
  border-top: 2px solid #c47207;
  margin: -10px 0;
}

.coin .front .shapes div:after,
.coin .back .shapes div:after {
  content: "";
  position: absolute;
  width: 20px;
  height: 4px;
  background: #d57e08;
  border-top: 2px solid #c47207;
  margin: 8px 0;
}

.coin .front .shape_l,
.coin .back .shape_l {
  float: left;
}

.coin .front .shape_r,
.coin .back .shape_r {
  float: right;
}

.coin .front .top,
.coin .back .top {
  font-size: 14px;
  color: #d67f08;
  text-align: center;
  width: 100%;
  position: absolute;
  left: 0;
  top: 9px;
  text-shadow: 0 1px 0px #cb7407;
}

.coin .front .bottom,
.coin .back .bottom {
  font-size: 14px;
  color: #d67f08;
  text-align: center;
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 9px;
  text-shadow: 0 1px 0px #cb7407;
}

.coin .shadow {
  width: 100%;
  height: 20px;
  background: rgba(0, 0, 0, 0.4);
  left: 0;
  bottom: -50px;
  border-radius: 50%;
  z-index: -1;
  margin: 185px 7px 0 7px;
  animation: swift 1.5s infinite ease;
}

@keyframes swift {
  0% {
    opacity: 0.8;
  }

  50% {
    opacity: 0.4;
    transform: scale(0.8);
  }

  100% {
    opacity: 0.8;
  }
}

::selection {
  background-color: var(--main);
  color: white;
}

/*-------------------------------------------------
    [ ## Header ]
*/
.header-section {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  background-color: transparent;
}

@media only screen and (max-width: 991px) {
  .header-section .header-top-area .container {
    max-width: 100%;
  }
}

.header {
  background-color: transparent;
  width: 100%;
  z-index: 99;
  -webkit-transition: all ease 1s;
  transition: all ease 1s;
}

.header.active {
  transform: translateY(0%);
  position: fixed;
  top: 0;
  left: 0;
  animation-name: fadeInDown;
  animation-duration: 1s;
  transition: all 1s;
}

.header-section.header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  background-color: #272d43;
  box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.1);
}


.header-top-area {
  position: relative;
  background: #39405A;
}

.header-top-area .header-top-content .header-right-info span {
  color: white;
  margin-right: 15px;
  font-family: "Nunito", sans-serif;
  font-weight: 700;
  font-size: 14px;
}

.header-top-area .header-top-content .header-right-info span i {
  margin-right: 10px;
}

.header-top-area .header-top-content .header-right-info .first-info {
  background-color: #272d43;
  padding: 20px;
}

.header-top-area .header-top-content .header-right-info .second-info span {
  color: var(--main);
}

.header-top-area .header-top-content .header-right-info .third-info span {
  color: var(--main);
}

.header-top-area .header-top-content .header-right-info .fourth-info span {
  color: var(--main);
}

.header-top-area .header-top-content .header-account {
  background-color: #ff5757;
  margin-left: 20px;
  padding: 25px 35px;
}

.header-top-area .header-top-content .header-account span {
  color: white;
  text-transform: uppercase;
}

.header-top-area .header-top-content .header-account .register::after {
  content: '/';
  padding-left: 5px;
  padding-right: 5px;
}

.header-top-area .header-top-content .header-social a {
  font-size: 15px;
  display: inline-block;
  padding: 6px;
  color: white;
}

.header-top-area .header-top-content .header-social a:hover,
.header-top-area .header-top-content .header-social a.active {
  color: var(--main);
}

@media only screen and (max-width: 1199px) {
  .header-top-area .header-top-content .header-social a {
    font-size: 14px;
  }
}

@media only screen and (max-width: 575px) {
  .header-top-area .header-top-content .header-social a {
    padding: 0 5px;
  }
}

.header-top-area .header-top-content .header-mail-area i {
  color: #272d43;
  margin-right: 5px;
}

.header-top-area .header-top-content .header-mail-area span {
  text-transform: lowercase;
}

.header-top-area .header-top-content .header-support span {
  color: white;
  margin-right: 30px;
}

.header-section.header-fixed .header-top-area {
  display: none;
}

.header-bottom-area {
  position: relative;
  background: transparent;
  padding-top: 20px;
}

.header-section.header-fixed .header-bottom-area {
  padding-top: 0;
}

.header-bottom-area .header-account {
  padding-left: 30px;
}

.header-bottom-area .header-account .cmn-btn {
  padding: 12px 30px;
}

.header-action {
  margin: -5px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.header-action a {
  font-size: 14px;
  margin: 5px;
}

.header-bottom-area .navbar-expand-lg {
  z-index: 10;
  background-color: transparent;
  padding: 0 !important;
}

@media only screen and (max-width: 991px) {
  .header-bottom-area .navbar-expand-lg {
    padding: 0 !important;
  }
}

.header-bottom-area .navbar-collapse .main-menu li:last-child .sub-menu {
  left: auto;
  right: 0;
}

.header-bottom-area .navbar-collapse .main-menu li {
  position: relative;
  margin-right: 45px;
}

@media (max-width: 1499px) {
  .header-bottom-area .navbar-collapse .main-menu li {
    margin-right: 25px;
  }
}

.header-bottom-area .navbar-collapse .main-menu li:nth-last-of-type(2) {
  margin-right: 0;
}

@media (max-width: 991px) {
  .header-bottom-area .navbar-collapse .main-menu li {
    margin-right: 0px;
  }
}

.header-bottom-area .navbar-collapse .main-menu li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 100%;
}

.header-bottom-area .navbar-collapse .main-menu li.menu_has_children {
  position: relative;
}

.header-bottom-area .navbar-collapse .main-menu li.menu_has_children i {
  font-size: 18px;
  /* transform: translateY(2px); */
  margin-left: 5px;
}

@media (max-width: 991px) {
  .header-bottom-area .navbar-collapse .main-menu li.menu_has_children::before {
    top: 12px;
    right: 15px;
  }
}

.header-bottom-area .navbar-collapse .main-menu li a {
  position: relative;
  padding: 20px 0 20px 0;
  color: white;
  font-weight: 600;
  align-items: center;
  display: inline-block;
  font-family: "Nunito", sans-serif;
  font-size: 16px;
  text-transform: capitalize;
}

.header-bottom-area .navbar-collapse .main-menu li a:hover {
  color: var(--main) !important;
}

@media (max-width: 1199px) {
  .dashboard-header .navbar-collapse .main-menu li a:hover {
    color: #fff !important;
  }
}

.header-bottom-area .navbar-collapse .main-menu li:hover a::after {
  width: 100%;
}

.header-bottom-area .header-section.header-fixed .navbar-collapse .main-menu li a {
  padding: 30px 18px 30px 0;
}

@media (max-width: 1199px) {
  .header-bottom-area .navbar-collapse .main-menu li a {
    padding: 30px 0px;
  }
}

.header-bottom-area .navbar-collapse .main-menu li .sub-menu {
  position: absolute;
  top: 105%;
  left: 0;
  width: 200px;
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: 9;
  margin-left: 0;
  border: 0;
  outline: 0;
}

.header-bottom-area .navbar-collapse .main-menu li .sub-menu li {
  border-bottom: 1px solid #e5e5e5;
  margin-right: 0;
  transition: all ease 0.3s;
}

.header-bottom-area .navbar-collapse .main-menu li .sub-menu li:last-child {
  border-bottom: 0px solid;
}

.header-bottom-area .navbar-collapse .main-menu li .sub-menu li:hover {
  color: #292929;
  padding-left: 5px;
}

.header-bottom-area .navbar-collapse .main-menu li .sub-menu li a {
  color: #292929;
  padding: 8px 20px;
  font-size: 14px;
  display: block;
  transition: all ease 0.5s;
}

.header-bottom-area .navbar-collapse .main-menu li .sub-menu li a::after {
  position: unset;
  content: none;
}

.header-bottom-area .navbar-collapse .main-menu li .sub-menu .header-section.header-fixed .navbar-collapse .main-menu li .sub-menu li a {
  padding: 8px 20px;
}

@media (max-width: 991px) {
  .header-bottom-area .navbar-collapse .main-menu li .sub-menu .navbar-collapse .main-menu li .sub-menu {
    position: initial;
    opacity: 1;
    visibility: visible;
    display: none;
    -webkit-transition: none;
    transition: none;
  }
}

.header-bottom-area .navbar-collapse .main-menu li .sub-menu .navbar-collapse .main-menu li:last-child .sub-menu {
  left: auto;
  right: 0;
}

/* ==================================== Dashboard Header Css Start ========================== */
@media (max-width: 1199px) {
  .dashboard-header .navbar-collapse .main-menu li a {
    padding: 12px 0 !important;
    display: block;
  }

  .dashboard-header .navbar-collapse .main-menu li a.cmn-btn {
    padding: 8px 25px !important;
  }

  .dashboard-header .navbar-collapse .main-menu li a.cmn-btn:hover {
    color: var(--main) !important;
  }

  .dashboard-header .navbar-collapse .main-menu {
    padding: 20px;
    background-color: #272d43;
    border-radius: 0 0 5px 5px;
  }

  .header-nav-menu {
    height: 100vh;
    overflow-x: auto;
  }

  .dashboard-header .navbar-collapse .main-menu li .sub-menu {
    position: static;
    visibility: visible;
    opacity: 1;
    z-index: 99;
    display: none;
    background-color: #233162;
    width: 100%;
    padding: 0 15px;
    top: auto;
  }

  .dashboard-header .navbar-collapse .main-menu li .sub-menu.show {
    display: block;
  }

  .header-bottom-area .navbar-collapse .main-menu li .sub-menu li:hover {
    padding-left: 0px;
  }

  .dashboard-header .navbar-collapse .main-menu li .sub-menu li a::before {
    display: none;
  }

  .header-bottom-area .navbar-collapse .main-menu li .sub-menu li {
    border-bottom: 1px solid #e5e5e52e;
  }
}

/* ==================================== Dashboard Header Css End ========================== */


.language-select-list {
  position: relative;
  z-index: 99;
  margin-right: 20px;
}

.language-thumb i {
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.login-thumb i {
  font-size: 18px;
  font-weight: 600;
}

.login-bar-two {
  margin-left: 30px;
}

.language-select .nice-select {
  padding-left: 10px;
  font-size: 14px;
  background: transparent;
  font-family: "Nunito", sans-serif;
  font-weight: 700;
  border: none;
}

.language-select .nice-select span {
  color: white;
}

.language-select .nice-select .option {
  font-weight: 600;
}

.language-select .nice-select .option:hover {
  color: #777777;
}

.language-select .nice-select:after {
  border-style: solid;
  border-width: 7px 7px;
  border-color: white transparent transparent;
  transform: rotate(0);
  right: 4px;
}

.site-logo img {
  width: 220px;
  height: 50px;
  object-fit: contain;
}

@media (max-width: 1399px) {
  .site-logo img {
    width: 150px;
    height: 45px;
  }
}

@media only screen and (max-width: 420px) {
  .site-logo img {
    width: 140px;
    height: 45px;
  }
}

.navbar-toggler {


  padding: 0 !important;
  border: 0;
}

.navbar-toggler span {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 5px;
  color: var(--main);
  font-size: 30px;
}

@media only screen and (max-width: 575px) {
  .navbar-toggler span {
    font-size: 22px;
  }
}

@media (max-width: 991px) {
  .header-top-area {
    display: none;
  }
}

@media (max-width: 991px) {
  .header-bottom-area {
    padding: 10px 0;
  }
}

.title-border {
  position: relative;
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 20px;
  border-radius: 30px;
}

.title-border::before {
  position: absolute;
  content: '';
  top: 50%;
  left: -50px;
  height: 5px;
  width: 70px;
  border: 1px solid var(--main);
  border-radius: 30px;
  overflow: hidden;
}

.title-border::after {
  position: absolute;
  content: '';
  top: 50%;
  left: -33px;
  height: 5px;
  width: 35px;
  background-color: var(--main);
  animation: divider-effect 5s linear infinite;
}

.section-header .title-border-left {
  position: relative;
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
  border-radius: 30px;
}

.section-header .title-border-left::before {
  position: absolute;
  content: '';
  top: 50%;
  left: 0;
  height: 5px;
  width: 70px;
  border: 1px solid var(--main);
  border-radius: 30px;
  overflow: hidden;
}

.section-header .title-border-left::after {
  position: absolute;
  content: '';
  top: 50%;
  left: 0;
  height: 5px;
  width: 35px;
  background-color: var(--main);
  animation: divider-effect 5s linear infinite;
}

.section-header p {
  font-size: 16px;
  margin-left: auto;
  margin-right: auto;
  text-transform: unset;
  font-weight: 400;
}

@media only screen and (max-width: 991px) {
  .section-header p {
    width: 100%;
    font-size: 18px;
  }
}

/*--------------------------------------------------------------
# Navigation
--------------------------------------------------------------*/
/*-------------------------------------------------
    [ ## Links ]
*/
a {
  color: inherit;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

a:hover,
a:focus,
a:active {
  text-decoration: none;
}

a:focus {
  outline: none;
}

a:hover,
a:active {
  outline: 0;
  text-decoration: none;
}

/*-------------------------------------------------
    [ ## Menus ]
*/
/*-------------------------------------------------
    [ ## Pagination ]
*/
/* page link pagination */
.pagination {
  margin-top: 0px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.pagination .page-item {
  text-align: center;
  padding: 3px;
}

.pagination .page-item a,
.pagination .page-item span {
  width: 35px;
  height: 35px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background: transparent;
  border: 1px solid #e5e5e5;
  color: #292929;
  padding: 0;
  line-height: 35px;
  display: block;
  margin: 0;
}

.pagination .page-item.disabled span {
  background: rgb(0 0 0 / 10%);
  border: 1px solid rgb(0 0 0 / 1%);
  color: black;
}

.pagination .page-item.disabled:hover span {
  background: rgb(0 0 0 / 20%);
  border: 1px solid rgb(0 0 0 / 10%);
  color: black;
}

.pagination .page-item.active .page-link,
.pagination .page-item:hover .page-link {
  background: var(--main);
  border-color: var(--main);
  color: white;
}

/*-------------------------------------------------
    [ ## Navigation Responsive ]
*/
/*--------------------------------------------------------------
# Layout
--------------------------------------------------------------*/
/*--------------------------------------------------------------
    [ ## Hero ]
--------------------------------------------------------------*/
.banner-section {
  position: relative;
  background-attachment: fixed;
  z-index: 9;
  padding-top: 250px;
  padding-bottom: 240px;
}

@media only screen and (max-width: 991px) {
  .banner-section {
    padding-top: 150px;
    padding-bottom: 180px;
  }
}

.banner-section .banner-content {
  position: relative;
  z-index: 9;
}

.banner-section .banner-content .title {
  font-size: 60px;
  font-weight: 700;
  text-transform: capitalize;
  position: relative;
  color: white;
}

.banner-section .container,
.container-fluid {
  position: relative;
  z-index: 11;
}

.banner-section .banner-content .title span {
  color: white;
}

@media only screen and (max-width: 991px) {
  .banner-section .banner-content .title {
    font-size: 30px !important;
  }
}

.banner-section .banner-content p {
  font-size: 18px;
  width: 70%;
  margin: 0 auto;
  text-transform: unset;
  line-height: 1.5em;
  color: white;
}

.banner-section .banner-btn {
  margin-top: 50px;
  display: flex;
  align-items: center;
}

@media only screen and (max-width: 440px) {
  .banner-section .banner-btn {
    display: block;
  }
}

.banner-section .banner-btn .cmn-btn {
  margin-right: 20px;
}

@media only screen and (max-width: 440px) {
  .banner-section .banner-btn .cmn-btn {
    margin-right: 5px;
  }
}

#particles-js {
  width: 100%;
  height: 100%;
  background-image: url(style.css);
  background-size: cover;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
}

.inner-banner-section {
  padding-top: 140px;
  padding-bottom: 80px;
  overflow: hidden;
}

@media (max-width: 991px) {
  .inner-banner-section {
    padding-top: 100px;
    padding-bottom: 60px;
  }
}

@media (max-width: 767px) {
  .inner-banner-section {
    padding-top: 80px;
    padding-bottom: 40px;
  }
}

.dashboard-inner-banner {
  overflow: hidden;
  padding-top: 124px;
  padding-bottom: 60px;
}

@media (max-width: 1199px) {
  .dashboard-inner-banner {
    padding-top: 105px;
    padding-bottom: 40px;
  }
}

@media (max-width: 991px) {
  .dashboard-inner-banner {
    padding-top: 95px;
  }
}

.inner-banner-section .banner-content .title {
  font-size: 40px;
}

.inner-content {
  margin-bottom: -5px;
}

.inner-content .title {
  color: white;
  font-size: 80px;
}

@media only screen and (max-width: 991px) {
  .inner-content .title {
    font-size: 55px;
  }
}

.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-top: 20px;
  justify-content: center;
}

.breadcrumb li {
  color: var(--main);
  text-transform: capitalize;
}

.breadcrumb-item a {
  font-weight: 500;
  font-size: 18px;
  position: relative;
  z-index: 2;
}

.breadcrumb-item.active {
  font-weight: 500;
  color: white;
  font-size: 18px;
  position: relative;
  z-index: 2;
}

.breadcrumb-item+.breadcrumb-item::before {
  content: "\f101";
  font-family: "Font Awesome 5 Free";
  font-weight: 600;
  color: white;
  font-size: 18px;
  left: 0;
}

@media (max-width: 575px) {
  h2 {
    font-size: 22px !important;
  }

  h3 {
    font-size: 20px;
  }

  .pd-t-120 {
    padding-top: 80px;
  }

  .ptb-120 {
    padding: 80px 0;
  }
}

@media (max-width: 991px) {
  h2 {
    font-size: 32px;
  }

  h3 {
    font-size: 20px;
  }

  .banner-section .banner-content p {
    width: 100%;
  }
}

.banner-slider {
  overflow: hidden;
}

.banner-slider .swiper-slide-active .banner-element {
  animation: fadeInUp 1s;
}

.ruddra-next,
.ruddra-prev {
  background-color: white;
  width: 50px;
  height: 50px;
  border: none !important;
  border-radius: 0;
  text-align: center;
  line-height: 50px;
  outline: none;
  transition: all 300ms ease;
  display: inline-block;
  font-size: 18px;
  color: var(--main);
  position: absolute !important;
  top: 40%;
  z-index: 99;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  transition: all 0.3s;
}

.ruddra-prev {
  left: 20px;
}

.ruddra-next {
  right: 20px;
}

.ruddra-next:hover,
.ruddra-prev:hover {
  background-color: var(--main);
  color: white;
}

/*--------------------------------------------------------------
    [ ## Services ]
--------------------------------------------------------------*/
.service-section {
  background-color: #f8f8f8;
}

.service-icon {
  font-size: 40px;
  line-height: 1em;
  margin-bottom: 10px;
}

.service-icon i {
  color: var(--main);
}

.service-content .title {
  margin-bottom: 10px;
  font-size: 20px;
}

/*--------------------------------------------------------------
    [ ## team ]
--------------------------------------------------------------*/
/*--------------------------------------------------------------
    [ ## Project ]
--------------------------------------------------------------*/
/*--------------------------------------------------------------
    [ ## Testimonial ]
--------------------------------------------------------------*/
/*--------------------------------------------------------------
    [ ## Sections ]
--------------------------------------------------------------*/
/*--------------------------------------------------------------
    [ ## Sections ]
--------------------------------------------------------------*/
/*-------------------------------------------------
    [ ### work Block ]
*/
.work-section .work-content-area {
  transition: all 0.5s;
}

.work-section .work-content {
  margin-right: 15px;
}

@media only screen and (max-width: 991px) {
  .work-section .work-content {
    margin-right: 0;
  }
}

.work-section .work-content .work-item-area .work-item {
  position: relative;
  transition: all 0.5s;
}

@media only screen and (max-width: 991px) {
  .work-section .work-content .work-item-area .work-item {
    display: block !important;
    text-align: center;
  }

  .work-section .work-content .work-item-area .work-item .work-icon {
    width: 55px !important;
    height: 55px !important;
    line-height: 55px !important;
    font-size: 22px !important;
  }

  .work-section .work-content .work-item-area .work-item .work-icon i {
    bottom: 5px !important;
    right: 5px !important;
    font-size: 24px !important;
  }
}

.work-section .work-content .work-item-area .work-item .work-icon {
  width: 80px;
  height: 80px;
  line-height: 80px;
  display: inline-block;
  text-align: center;
  background-color: #272d43;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  color: #fff !important;
  font-size: 40px;
  border: 3px solid var(--main);
}

.work-section .work-content .work-item-area .work-item .work-icon i {
  position: absolute;
  font-size: 50px;
  right: 0;
  bottom: 0;
  opacity: .2;
  color: var(--main);
}

.work-section .work-content .work-item-area .work-item .work-details {
  width: calc(100% - 80px);
  padding-left: 20px;
}

@media only screen and (max-width: 991px) {
  .work-section .work-content .work-item-area .work-item .work-details {
    width: 100%;
    padding-left: 0;
    padding-top: 10px;
  }

  .work-section .work-content .work-item-area .work-item .work-details p {
    text-align: center;
  }
}

.work-section .work-content .work-item-area .work-item .work-details--style {
  padding-right: 20px;
}

@media only screen and (max-width: 991px) {
  .work-section .work-content .work-item-area .work-item .work-details--style {
    padding-right: 0;
  }
}

.work-section .work-content--style {
  margin-right: 0;
  margin-left: 15px;
}

@media only screen and (max-width: 991px) {
  .work-section .work-content--style {
    margin-left: 0;
  }
}

/*-------------------------------------------------
    [ ### about block ]
*/
.about-section {
  background-color: #f8f8f8;
}

.about-section .about-content .title {
  padding-bottom: 10px;
}

.about-section .about-content .title span {
  color: var(--main);
}

.about-section .about-content .title-border {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
  border-radius: 30px;
}

.about-section .about-content .title-border::before {
  position: absolute;
  content: '';
  top: 50%;
  left: 0;
  height: 5px;
  width: 70px;
  border: 1px solid #272d43;
  border-radius: 30px;
}

.about-section .about-content .title-border::after {
  position: absolute;
  content: '';
  top: 50%;
  left: 0;
  height: 5px;
  width: 35px;
  background-color: #272d43;
}

.about-section .about-content .about-item-area {
  margin-top: 30px;
  position: relative;
  z-index: 2;
}

/* .about-section .about-content .about-item-area::before {
  position: absolute;
  content: '';
  top: 20px;
  left: 5%;
  width: 2px;
  height: calc(100% - 40px);
  border: 2px dashed #272d43;
  z-index: -1;
} */

/* @media only screen and (max-width: 575px) {
  .about-section .about-content .about-item-area::before {
    top: 56px;
    left: 10%;
    height: calc(100% - 102px);
  }
} */

.about-section .about-content .about-item-area .about-item {
  position: relative;
  align-items: unset !important;
}

.about-section .about-content .about-item-area .about-item::before {
  position: absolute;
  content: '';
  left: 28px;
  width: 2px;
  height: calc(100% - 30px);
  border: 2px dashed #272d43;
  z-index: -1;
  top: 63px;
}

@media (max-width: 400px) {
  .about-section .about-content .about-item-area .about-item::before {
    left: 23px;
    height: calc(100% - -23px);
    top: 50px;
  }
}

.about-item:last-of-type::before {
  display: none;
}

.about-section .about-content .about-item-area .about-item .about-icon {
  width: 60px;
  height: 60px;
  line-height: 65px;
  display: inline-block;
  text-align: center;
  background-color: #272d43;
  border-radius: 50%;
  color: white;
  font-size: 24px;
}

@media (max-width: 400px) {
  .about-section .about-content .about-item-area .about-item .about-icon {
    width: 50px;
    height: 50px;
    line-height: 55px;
    font-size: 20px;
  }
}

.about-section .about-content .about-item-area .about-item .about-details {
  width: calc(100% - 60px);
  padding-left: 20px;
}

@media (max-width: 400px) {
  .about-section .about-content .about-item-area .about-item .about-details {
    width: calc(100% - 50px);
    padding-left: 15px;
  }
}

.about-section .about-content .about-item-area .about-item .about-details .title {
  font-weight: 500;
  color: var(--main);
  margin-bottom: 0;
  font-size: 22px;
}

/*-------------------------------------------------
    [ ### choose Block ]
*/
.choose-section {
  background-color: #272d43;
  overflow: hidden;
}

.choose-section .section-header .section-title {
  color: white;
}

.choose-section .section-header p {
  color: white;
}

.choose-item-area {
  position: relative;
}

.choose-section .choose-item-area .choose-item:nth-child(2n) {
  margin-left: -60px;
  margin-right: 30px;
}

@media only screen and (max-width: 575px) {
  .choose-section .choose-item-area .choose-item:nth-child(2n) {
    margin-left: 0;
    margin-right: 0;
  }
}

.choose-section .choose-item-area .choose-item:nth-child(2n-2) {
  margin-right: -60px;
  margin-left: 30px;
}

@media only screen and (max-width: 767px) {
  .choose-section .choose-item-area .choose-item {
    display: block !important;
    text-align: center;
  }

  .choose-section .choose-item-area .choose-item .choose-icon {
    width: 100% !important;
  }

  .choose-section .choose-item-area .choose-item .choose-content {
    width: 100% !important;
    padding-top: 20px !important;
  }

  .choose-item-area .row {
    justify-content: center !important;
  }
}

@media only screen and (max-width: 991px) {
  .choose-section .choose-item-area .choose-item:nth-child(2n-2) {
    margin-left: 0;
    margin-right: 0;
  }
}

.choose-section .choose-item-area .choose-item .choose-icon {
  width: 60px;
}

.choose-section .choose-item-area .choose-item .choose-icon i {
  font-size: 60px;
  color: var(--main);
}

.choose-section .choose-item-area .choose-item .choose-content {
  width: calc(100% - 60px);
  padding-left: 20px;
}

.choose-section .choose-item-area .choose-item .choose-content .title {
  color: white;
  font-weight: 600;
  font-size: 22px;
}

.choose-section .choose-item-area .choose-item .choose-content p {
  color: white;
}

.choose-section .choose-item-area .choose-thumb {
  position: absolute;
  bottom: -16%;
}

@media only screen and (max-width: 991px) {
  .choose-section .choose-item-area .choose-thumb-inner {
    display: none;
  }
}

/*-------------------------------------------------
    [ ### Call To Action ]
*/
.call-to-action-section {
  background-color: #272d43;
}

.subscribe-section {
  background-color: #272d43;
}

.call-to-action-section .call-to-action-content .title {
  color: white;
  font-weight: 700;
}

.call-to-action-section .call-to-action-content p {
  color: white;
}

.call-to-action-section .call-to-action-btn {
  float: right;
}

@media only screen and (max-width: 991px) {
  .call-to-action-section .call-to-action-btn {
    float: left;
  }
}

.maps {
  height: 400px;
}

@media only screen and (max-width: 991px) {
  .contact-inquiry {
    margin-bottom: 60px;
  }
}

.contact-thumb {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding-right: 50px;
}

.contact-area {
  padding: 40px;
  box-shadow: 0px 0px 27px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: -170px;
  background-color: white;
  position: relative;
  z-index: 9;
}

@media only screen and (max-width: 991px) {
  .contact-area {
    padding: 30px;
  }
}

.contact-area::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 40%;
  height: 100%;
  background-color: var(--main);
  z-index: -1;
}

@media only screen and (max-width: 991px) {
  .contact-area::before {
    width: 0;
  }
}

.contact-left-header .title {
  margin-bottom: 30px;
  color: white;
}

@media only screen and (max-width: 991px) {
  .contact-left-header .title {
    color: #292929;
  }
}

.contact-icon {
  position: absolute;
  opacity: .2;
}

.contact-icon i::before {
  font-size: 120px !important;
  color: var(--main);
}

.contact-item li {
  color: white;
}

@media only screen and (max-width: 991px) {
  .contact-item li {
    color: #777777;
  }
}

.contact-right-area .title {
  margin-bottom: 30px;
  color: var(--main);
}

.contact-form-title .title {
  margin-bottom: 30px;
}

.contact-form input,
.contact-form textarea {
  background-color: white;
  box-shadow: 0px 0px 10px 0.5px rgba(0, 0, 0, 0.05);
  color: #777777;
  padding: 12px 20px;
  border: none;
  border-radius: 120px;
}

.contact-form textarea {
  padding: 15px 60px;
  min-height: 100px;
  border-radius: 20px;
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
  color: #777777;
}

.contact-form .form-group input[type="submit"] {
  width: auto;
  background-color: var(--main);
  padding: 12px 40px;
  font-size: 16px;
  margin-top: 20px;
  color: white;
}

.contact-form-area {
  margin-left: 40px;
  box-shadow: 0px 0px 47.4px 31.6px rgba(0, 0, 0, 0.03);
  padding: 40px;
}

@media only screen and (max-width: 991px) {
  .contact-form-area {
    margin-left: 0;
    padding: 20px;
  }
}

.contact-form-area .title {
  font-size: 30px;
  margin-bottom: 30px;
}

@media only screen and (max-width: 991px) {
  .contact-form-area .title {
    font-size: 22px;
  }
}

.contact-section .register-form-area {
  padding: 0 !important;
}

.register-form-area .title-border::before {
  left: 2px;
}

.register-form-area .title-border::after {
  left: 3px;
}

.contact-info-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
  border: 1px solid #00000012;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0px 4px 9px -2px #ddd;
  height: 100%;
}

.contact-info-item i {
  display: inline-block;
  text-align: center;
  color: #fff;
  font-size: 24px;
  border-radius: 50%;
  margin-bottom: 30px;
  width: 80px;
  background-color: var(--main);
  ;
  height: 80px;
  line-height: 80px;
  box-shadow: 0px 0px 10px 0.5px rgba(0, 0, 0, 0.05);
}

.contact-info-item i::before {
  font-size: 24px;
  line-height: 1em;
}

.calculate-section .cal-area {
  box-shadow: 7px 5px 30px 0px rgb(72 73 121 / 15%);
  background-color: white;
  border-radius: 3px;
  padding: 40px 30px;
  margin-top: -70px;
  position: relative;
  z-index: 9;
}

.calculate-section .cal-area .title {
  font-weight: 800;
  margin-bottom: 0;
  margin-left: 15px;
}

@media only screen and (max-width: 991px) {
  .calculate-section .cal-area .title {
    margin-bottom: 10px;
  }
}

.calculate-section .cal-area .cal-form .form-group {
  margin-bottom: 0;
}

@media only screen and (max-width: 991px) {
  .calculate-section .cal-area .cal-form .form-group {
    margin-bottom: 10px;
  }
}

.calculate-section .cal-area .cal-form input {
  background-color: #272d43;
  color: white;
  border-radius: 3px;
  padding: 10px 25px;
  border: none;
  text-align: center;
}

.calculate-section .cal-area .cal-form input::placeholder {
  color: white;
}

.calculate-section .cal-area .cal-form .nice-select {
  background-color: var(--main);
  border-radius: 3px;
  height: 45px;
  line-height: 45px;
}

.calculate-section .cal-area .cal-form .nice-select .current {
  color: white;
}

.calculate-section .cal-area .cal-form .nice-select::after {
  border-bottom: 2px solid white;
  border-right: 2px solid white;
}

.calculate-section .cal-area .cal-form .nice-select .option:hover {
  color: white;
}

.calculate-section .cal-area .cal-form .nice-select .option.selected:hover {
  color: #777777;
}

.calculate-section .cal-area .cal-form .submit-btn {
  background-color: var(--main);
  color: white;
  width: 50px;
  height: 45px;
  line-height: 45px;
  display: inline-block;
  border-radius: 3px;
  text-align: center;
  font-size: 20px;
}

@media only screen and (max-width: 991px) {
  .calculate-section .cal-area .cal-form .submit-btn {
    width: 100%;
  }
}

.calculate-section .cal-area .revenue-area .sub-title {
  color: var(--main);
}

.calculate-section .cal-area .revenue-area .title {
  font-size: 24px;
  margin-bottom: 0;
}

.faq-wrapper {
  margin: -7px 0;
}

.faq-wrapper .faq-item {
  padding: 20px 0;
  transition: all 0.3s;
}

.faq-wrapper .faq-item .faq-title {
  font-size: 18px;
  margin-top: -7px;
  margin-bottom: -7px;
  text-transform: unset;
  align-items: center;
  cursor: pointer;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
}

.faq-wrapper .faq-item .faq-title .title {
  width: calc(100% - 40px);
  padding: 12px 20px;
  display: inline-block;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0;
  text-transform: unset;
}

@media only screen and (max-width: 991px) {
  .faq-wrapper .faq-item .faq-title .title {
    font-size: 18px;
  }
}

@media only screen and (max-width: 575px) {
  .faq-wrapper .faq-item .faq-title .title {
    padding: 10px;
    font-size: 16px;
  }
}

.faq-wrapper .faq-item.open .right-icon::before {
  background-color: #fff;
  -webkit-transform: translate(-50%, -50%) rotate(90deg);
  -ms-transform: translate(-50%, -50%) rotate(90deg);
  transform: translate(-50%, -50%) rotate(90deg);
}

.faq-wrapper .faq-item.open .faq-title {
  background-color: var(--main);
  color: #fff;
  border: 1px solid var(--main);
}

.faq-wrapper .faq-item.open .right-icon::after {
  background-color: #fff;
}

.faq-wrapper .faq-item.active .faq-content {
  display: block;
}

.faq-wrapper .faq-item.active.open .right-icon::before {
  -webkit-transform: translate(-50%, -50%) rotate(90deg);
  -ms-transform: translate(-50%, -50%) rotate(90deg);
  transform: translate(-50%, -50%) rotate(90deg);
}

.faq-wrapper .faq-content {
  display: none;
}

.faq-wrapper .faq-content p {
  margin-top: 25px;
  text-transform: unset;
}

.faq-wrapper .faq-content p:last-child {
  margin-bottom: -7px;
}

.faq-wrapper .right-icon {
  display: block;
  width: 35px;
  height: 35px;
  position: relative;
  right: 20px;
}

@media (max-width: 575px) {
  .faq-wrapper .right-icon {
    width: 15px;
    height: 15px;
    right: 0px;
  }
}

.faq-wrapper .right-icon::before,
.faq-wrapper .right-icon::after {
  position: absolute;
  content: '';
  background: #232323;
  -webkit-transition: all ease 0.3s;
  -moz-transition: all ease 0.3s;
  transition: all ease 0.3s;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.faq-wrapper .right-icon::before {
  width: 2px;
  height: 16px;
  top: 50%;
  left: 50%;
}

.faq-wrapper .right-icon::after {
  width: 16px;
  height: 2px;
  top: 50%;
  left: 50%;
}

.call-to-action-form {
  margin-top: 30px;
  position: relative;
}

.call-to-action-form .submit-btn {
  position: absolute;
  font-size: 20px;
  right: -2px;
  background-color: var(--main);
  border: none;
  color: #fff;
  padding: 13px 30px;
  border-radius: 0 3px 3px 0;
}

.call-to-action-form input {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.pricing-section {
  position: relative;
}

.pricing-section .pricing-item {
  padding: 40px 20px;
  border-radius: 3px;
  transition: all 0.5s;
  background-color: #272d43;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pricing-section .pricing-item:hover {
  background-color: var(--main);
}

.pricing-section .pricing-item:hover .pricing-icon i {
  background-color: var(--main);
}

.pricing-section .pricing-item .pricing-icon {
  margin-top: -60px;
}

.pricing-section .pricing-item .pricing-icon i {
  width: 115px;
  height: 115px;
  line-height: 115px;
  display: inline-block;
  text-align: center;
  color: white;
  background-color: #272d43;
  border-radius: 50%;
  font-size: 100px;
  transition: all 0.5s;
}

.pricing-section .pricing-item .pricing-header .pricing-border {
  display: inline-block;
  width: 10px;
  height: 10px;
  line-height: 10px;
  background-color: #dfdfdf;
  border-radius: 50%;
  position: relative;
}

.pricing-section .pricing-item .pricing-header .pricing-border::before {
  position: absolute;
  content: '';
  top: 40%;
  left: -70px;
  height: 3px;
  width: 60px;
  border: 1px solid #dfdfdf;
  border-radius: 30px;
}

.pricing-section .pricing-item .pricing-header .pricing-border::after {
  position: absolute;
  content: '';
  top: 40%;
  right: -70px;
  height: 3px;
  width: 60px;
  border: 1px solid #dfdfdf;
  border-radius: 30px;
}

.pricing-section .pricing-item .pricing-header .title {
  font-weight: 600;
  font-size: 34px;
  color: white;
  margin-bottom: 20px;
}

.pricing-section .pricing-item .pricing-header .title span {
  font-size: 16px;
}

.pricing-section .pricing-item .pricing-header .sub-title {
  font-weight: 600;
  color: white;
  margin-top: 20px;
}

.pricing-section .pricing-item .pricing-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.pricing-section .pricing-item .pricing-body .pricing-list li {
  padding-bottom: 20px;
  font-size: 16px;
  color: white;
}

@media only screen and (max-width: 991px) {
  .pricing-section .pricing-item .pricing-body .pricing-list li {
    font-size: 16px;
  }
}

.pricing-section .pricing-item .pricing-body .pricing-list li span {
  font-size: 18px;
}

@media only screen and (max-width: 991px) {
  .pricing-section .pricing-item .pricing-body .pricing-list li span {
    font-size: 16px;
  }
}

.pricing-section .pricing-item .pricing-body .pricing-btn-area {
  margin-top: 10px;
}

.register-section .register-form-area {
  padding: 50px 30px;
  position: relative;
  z-index: 9;
}

.register-section .register-form-area::before {
  position: absolute;
  content: '';
  bottom: -25px;
  left: -25px;
  background-color: var(--main);
  width: 100%;
  height: 100%;
  border-radius: 5px;
  z-index: -10;
}

.contact-section .register-form-area::before {
  display: none;
}

.contact-section .register-form-area::after {
  display: none;
}

.register-section .register-form-area::after {
  position: absolute;
  content: '';
  top: 0;
  right: 0;
  background-color: white;
  width: 100%;
  height: 100%;
  box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  z-index: -10;
}

.register-area {
  min-height: 100vh;
}

@media only screen and (max-width: 991px) {
  .register-section .register-form-area {
    padding: 30px;
    margin-top: 30px;
  }
}

@media only screen and (max-width: 575px) {
  .register-section .register-form-area {
    padding: 20px 12px;
    margin-top: 20px;
  }
}

.register-logo-area {
  background-color: #272d43;
  padding: 10px;
  margin-bottom: 30px;
  border-radius: 5px;
}

.register-logo-area a {
  width: 150px;
}

.account-header {
  margin-bottom: 30px;
}

.account-header .sub-title {
  color: #292929;
  font-size: 18px;
  font-weight: 600;
}

.account-header .sub-title a {
  color: var(--main);
}

.register-section .register-form-area .register-form .form-group {
  position: relative;
}

.register-section .register-form-area .register-form .register-icon {
  position: absolute;
  top: 50%;
  left: 10px;
  margin-bottom: 0;
  transform: translateY(-50%);
}

.register-section .register-form-area .register-form .register-icon i,
.register-section .register-form-area .register-form .register-icon span {
  width: 30px;
  height: 30px;
  line-height: 30px;
  display: inline-block;
  text-align: center;
  color: white;
  background-color: var(--main);
  border-radius: 5px;
  font-size: 12px;
}

.register-section .register-form-area .register-form input:not([type=checkbox]):not([type=radio]) {
  background-color: #fff;
  color: #2a2a2a;
  padding: 8px 20px 8px 45px;
  border-radius: 5px;
  font-size: 16px;
}

.register-section .register-form-area .register-form input::placeholder {
  color: #2a2a2a66;
  line-height: 1;
  font-size: 13px;
}

.register-section .register-form-area .register-form .nice-select {
  background-color: var(--main);
  border-radius: 5px;
  height: 42px;
  line-height: 42px;
}

.register-section .register-form-area .register-form .nice-select .current {
  color: white;
}

.register-section .register-form-area .register-form .nice-select::after {
  border-bottom: 2px solid white;
  border-right: 2px solid white;
}

.register-section .register-form-area .register-form .nice-select .option:hover {
  color: white;
}

.register-section .register-form-area .register-form .nice-select .option.selected:hover {
  color: #777777;
}

.register-section .register-form-area .register-form .radio-wrapper {
  background-color: #272d43;
  border-radius: 10px;
  padding: 15px 30px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.register-section .register-form-area .register-form .radio-item {
  margin-right: 20px;
}

.register-section .register-form-area .register-form .radio-item [type="radio"]:checked,
.register-section .register-form-area .register-form .radio-item [type="radio"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

.register-section .register-form-area .register-form .radio-item [type="radio"]:checked+label,
.register-section .register-form-area .register-form .radio-item [type="radio"]:not(:checked)+label {
  position: relative;
  padding-left: 28px;
  cursor: pointer;
  line-height: 20px;
  display: inline-block;
  color: white;
  margin: 0;
}

.register-section .register-form-area .register-form .radio-item [type="radio"]:checked+label:before,
.register-section .register-form-area .register-form .radio-item [type="radio"]:not(:checked)+label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid transparent;
  border-radius: 100%;
  background: #fff;
}

.register-section .register-form-area .register-form .radio-item [type="radio"]:checked+label:after,
.register-section .register-form-area .register-form .radio-item [type="radio"]:not(:checked)+label:after {
  content: '';
  width: 12px;
  height: 12px;
  background: var(--main);
  position: absolute;
  top: 3px;
  left: 3px;
  border-radius: 100%;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

.register-section .register-form-area .register-form .radio-item [type="radio"]:not(:checked)+label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

.register-section .register-form-area .register-form .radio-item [type="radio"]:checked+label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.register-section .register-form-area .register-form .checkbox-wrapper .checkbox-item {
  text-align: left;
}

.register-section .register-form-area .register-form .checkbox-wrapper .checkbox-item input[type="checkbox"] {
  width: 15px;
  height: 15px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  margin-right: 10px;
  position: relative;
  margin-bottom: 4px;
}

.register-section .register-form-area .register-form .checkbox-wrapper .checkbox-item label {
  margin: 0;
  display: inline;
}

.register-section .register-form-area .register-form .checkbox-wrapper .checkbox-item span {
  display: inline;
}

@media (max-width: 575px) {

  .register-section .register-form-area .register-form .checkbox-wrapper .checkbox-item label,
  .register-section .register-form-area .register-form .checkbox-wrapper .checkbox-item span a {
    font-size: 15px;
  }
}

.register-section .register-form-area .register-form .forgot-password a {
  color: var(--main);
}

.register-section .register-form-area .register-form .submit-btn {
  padding: 9px 30px;
  border-radius: 5px;
  color: white;
  background-color: var(--main);
  width: 100%;
  margin-top: 0px;
}

.dashboard-section .dash-user-area {
  padding-bottom: 40px;
}

@media only screen and (max-width: 1199px) {
  .dashboard-section .dash-user-area .dash-left-user-area {
    display: block !important;
  }
}

.dashboard-section .dash-user-area .dash-left-user-area .user-icon {
  border: 5px solid var(--main);
  width: 100px;
  height: 100px;
}

.dashboard-section .dash-user-area .dash-left-user-area .user-icon img {
  height: 100%;
}

.dashboard-section .dash-user-area .dash-left-user-area .user-details {
  width: 100%;
  background-color: #272d43;
}

@media only screen and (max-width: 1199px) {
  .dashboard-section .dash-user-area .dash-left-user-area .user-details {
    width: 100%;
  }
}

.dashboard-section .dash-user-area .dash-left-user-area .user-details .title {
  background-color: var(--main);
  color: white;
  padding: 15px 10px;
  font-weight: 700;
  font-size: 22px;
  display: inline-block;
  margin-bottom: 0;
}

@media only screen and (max-width: 1199px) {
  .dashboard-section .dash-user-area .dash-left-user-area .user-details .title {
    width: 100%;
  }
}

.dashboard-section .dash-user-area .dash-left-user-area .user-details .dash-link li {
  display: inline-block;
  border-right: 1px solid #dfdfdf;
  padding: 15px 10px;
}

@media only screen and (max-width: 1199px) {
  .dashboard-section .dash-user-area .dash-left-user-area .user-details .dash-link li {
    width: 100%;
    border: none;
    padding: 10px;
  }
}

.dashboard-section .dash-user-area .dash-left-user-area .user-details .dash-link li a {
  color: white;
}

.dashboard-section .dash-item {
  background-color: #272d43;
  padding-left: 8px;
  box-shadow: 7px 5px 30px 0px rgb(72 73 121 / 15%);
  border-radius: 10px;
}

.dashboard-section .dash-item .dash-icon {
  background-color: #ffffff26;
  width: 100px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  color: var(--main);
}

.dashboard-section .dash-item .dash-icon img {
  color: var(--main);
  width: 60px;
  height: 60px;
  line-height: 60px;
  font-size: 24px;
  display: inline-block;
  text-align: center;
  background-color: white;
  border-radius: 50%;
  border: 3px solid var(--main);
}

.dashboard-section .dash-item .dash-icon span {
  color: #292929;
  font-size: 18px;
}

.dashboard-section .dash-item .dash-content {
  width: calc(100% - 100px);
  padding: 30px;
}

.dashboard-section .dash-item .dash-content .sub-title {
  color: white;
  font-size: 20px;
  font-weight: 700;
}

.dashboard-section .dash-item .dash-content .sub-title span {
  color: var(--main);
  font-weight: 700;
}

.dashboard-section .dash-item .dash-content .title {
  color: white;
  font-weight: 600;
}

.dash-link li.menu_has_children {
  position: relative;
}

@media (max-width: 991px) {
  .dash-link .menu_has_children .sub-menu {
    display: none;
  }
}

@media (max-width: 991px) {
  .dash-link .menu_has_children.show .sub-menu {
    display: flex !important;
    flex-wrap: wrap;
  }
}

.dash-link li .sub-menu {
  position: absolute;
  top: 105%;
  left: 0;
  width: 180px;
  background-color: white;
  box-shadow: 7px 5px 30px 0px rgb(72 73 121 / 15%);
  padding: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: -9;
  margin-left: 0;
}

@media (min-width: 1200px) {
  .dash-link li:last-child .sub-menu {
    right: 0;
    left: auto;
  }
}

.dash-link li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 100%;
  z-index: 99;
}

.dash-link li .sub-menu li {
  border-bottom: 1px solid #e5e5e5;
  margin-right: 0;
  width: 100%;
  padding: 0 !important;
  transition: all ease 0.5s;
  position: relative;
}

.dash-link li .sub-menu li:hover a {
  margin-left: 10px;
}

.dash-link li .sub-menu li a {
  color: #292929 !important;
  padding: 8px 20px;
  font-size: 14px;
  display: block;
  transition: all ease 0.5s;
}

.hash-section {
  background-color: #f8f8f8;
  padding: 60px 0;
}

@media only screen and (max-width: 991px) {
  .hash-section .hash-left {
    display: block !important;
  }
}

.hash-section .hash-left .hash-content {
  text-align: right;
  padding-right: 20px;
}

@media only screen and (max-width: 991px) {
  .hash-section .hash-left .hash-content {
    text-align: left;
    padding-right: 0;
    padding-bottom: 10px;
  }
}

.hash-section .hash-left .hash-content .title {
  color: #8595AF;
}

.hash-section .hash-left .hash-content .sub-title {
  font-size: 18px;
  font-weight: 600;
}

.hash-section .hash-left .hash-content .hash-btn {
  margin-top: 15px;
}

.hash-section .hash-left .hash-icon img {
  color: var(--main);
  width: 100px;
  height: 100px;
  line-height: 100px;
  font-size: 24px;
  display: inline-block;
  text-align: center;
  background-color: white;
  border-radius: 50%;
  border: 5px solid #8595AF;
}

@media only screen and (max-width: 991px) {
  .hash-section .hash-right {
    display: block !important;
  }
}

.hash-section .hash-right .hash-content {
  width: calc(100% - 100px);
  padding-left: 20px;
}

@media only screen and (max-width: 991px) {
  .hash-section .hash-right .hash-content {
    width: 100%;
    padding-left: 0;
    padding-top: 10px;
  }
}

.hash-section .hash-right .hash-content .title {
  color: #8595AF;
}

.hash-section .hash-right .hash-content .sub-title {
  font-size: 18px;
  font-weight: 600;
}

.hash-section .hash-right .hash-content .hash-btn {
  margin-top: 15px;
}

.hash-section .hash-right .hash-icon {
  width: 100px;
}

.hash-section .hash-right .hash-icon img {
  color: var(--main);
  width: 100px;
  height: 100px;
  line-height: 100px;
  font-size: 24px;
  display: inline-block;
  text-align: center;
  background-color: white;
  border-radius: 50%;
  border: 5px solid #8595AF;
}

.order-section .order-header {
  margin-bottom: 20px;
}

@media only screen and (max-width: 991px) {
  .order-section .order-header {
    float: none !important;
  }
}


.order-section .order-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0 10px;
  position: relative;
  z-index: 9;
  border-radius: 3px;
}

@media only screen and (max-width: 991px) {
  .order-section .order-table {
    width: 700px;
  }
}

.order-section .order-table thead tr {
  background-color: var(--main);
}

.order-section .order-table thead tr th {
  border: none;
  font-weight: 600;
  color: white;
  font-size: 16px;
  padding: 10px;
}

.order-section .order-table tbody tr {
  border-right: none;
  border-left: none;
  border-top: none;
  background-color: white;
  border: 1px solid #e5e5e5;
}

.order-section .order-table tbody tr th {
  padding: 10px;
}

.order-section .order-table tbody tr th .user-img {
  width: 50px;
  height: 50px;
  border-radius: 50px;
  overflow: hidden;
  margin-right: 20px;
}

.order-section .order-table tbody tr th span {
  color: #292929;
}

.order-section .order-table tbody tr td {
  border: none;
  color: #292929;
  font-weight: 600;
  padding: 10px;
  font-size: 16px;
}

/*--------------------------------------------------------------
    [ ## Blog ]
--------------------------------------------------------------*/
.sidebar {
  position: sticky;
  top: 10%;
}

.blog-item {
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  padding: 20px;
}

.blog-item .blog-content .title a {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box !important;
}

.blog-item .blog-content p {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box !important;
}

.blog-item:hover .blog-content .title {
  color: var(--main);
}

.blog-thumb {
  position: relative;
  display: block;
  overflow: hidden;
}

.blog-thumb .overlay-date {
  position: absolute;
  background-color: var(--main);
  color: white;
  font-size: 24px;
  font-weight: 500;
  left: 0;
  bottom: 0;
  height: 100px;
  width: 100px;
  line-height: 30px;
  padding: 20px;
  text-align: center;
  font-family: "Nunito", sans-serif;
}

@media only screen and (max-width: 991px) {
  .blog-thumb .overlay-date {
    font-size: 18px;
    width: 80px;
    height: 80px;
    line-height: 27px;
    padding: 10px;
  }
}

.blog-thumb img {
  width: 100%;
  transition: all 0.7s;
}

.blog-content {
  margin-top: 25px;
  transition: all 0.7s;
}

.blog-content .date {
  color: var(--main);
}

.blog-content .date i {
  margin-right: 6px;
}

.blog-content .title {
  font-size: 22px;
  margin-bottom: 10px;
}

@media (max-width: 991px) {
  .blog-content .title {
    font-size: 20px;
  }
}

.blog-content .blog-post-meta {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.blog-content .blog-post-meta span i {
  color: #d41919;
  margin-right: 10px;
}

.blog-content .blog-post-meta .blog-date {
  position: relative;
  margin-right: 100px;
}

.blog-content .blog-post-meta .blog-date::after {
  position: absolute;
  content: '';
  top: 0;
  right: -40%;
  width: 1px;
  height: 30px;
  background-color: #e5e5e5;
}

.blog-content .blog-btn .trans-btn i {
  margin-left: 7px;
  transition: all 0.3s;
}

.news-details-section .news-item {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.news-details-content {
  padding: 20px !important;
}

.news-details-content .title {
  font-size: 24px !important;
}

@media only screen and (max-width: 991px) {
  .news-details-content .title {
    font-size: 20px !important;
  }
}

.widget-box .search-option input {
  background: white;
  color: #777777;
  border: 1px solid #e5e5e5;
  width: 100%;
  padding: 15px 25px;
}

.widget-box .search-option input::placeholder {
  color: #777777;
}

.widget-box .search-submit {
  position: absolute;
  right: 0;
  margin-top: -54px;
  width: 80px;
  height: 54px;
  color: var(--main);
  background-color: transparent;
}

.widget-box .category-content li {
  position: relative;
  background-color: white;
  border: 1px solid #e5e5e5;
  padding: 16px 30px;
  margin-bottom: 10px;
}

.widget-box .category-content li:hover {
  background-color: #272d43;
}

.widget-box .category-content li a {
  font-weight: 500;
}

.contact-widget-box {
  background-color: white;
  border: 1px solid #e5e5e5;
  padding: 40px;
}

.contact-widget-box .title {
  margin-bottom: 15px;
}

.contact-widget-box p {
  margin-bottom: 30px;
}

.widget-box {
  background-color: white;
  border: 1px solid #e5e5e5;
  padding: 20px;
  border-radius: 3px;
}

.widget-box .widget-title {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 10px;
  margin-bottom: 10px;
  font-weight: 700;
}

.popular-widget-box .single-popular-item {
  margin-bottom: 20px;
  align-items: center;
}

.popular-widget-box .single-popular-item .popular-item-thumb {
  width: 60px;
}

.popular-widget-box .single-popular-item .popular-item-content {
  width: calc(100% - 60px);
  padding-left: 10px;
}

.popular-widget-box .single-popular-item .popular-item-content .blog-user {
  margin-bottom: 0;
}

.popular-widget-box .single-popular-item .popular-item-content .blog-date {
  font-size: 12px;
}

.blog-details-content .blog-title {
  font-size: 28px;
}

@media (max-width: 991px) {
  .blog-details-content .blog-title {
    font-size: 24px;
  }
}

.leave-comment {
  margin-top: 50px;
  padding: 20px;
  border: 1px solid #e5e5e5;
}

.leave-comment .title {
  margin-bottom: 30px;
}

.comment-form input {
  color: #777777;
  padding: 12px 25px;
  border: 1px solid #e5e5e5;
}

.comment-form input::placeholder {
  color: #777777;
}

.comment-form textarea {
  color: #777777;
  border: 1px solid #e5e5e5;
}

.comment-form textarea::placeholder {
  color: #777777;
}

.comment-form input[type="submit"] {
  width: auto;
  background-color: var(--main);
  color: white;
  padding: 12px 30px;
  font-size: 16px;
}

blockquote .quote-content {
  font-size: 16px;
  font-style: italic;
  color: white;
  background-color: var(--main);
  text-align: center;
  padding: 20px 25px;
}

@media (max-width: 575px) {
  blockquote .quote-content {
    font-size: 16px;
  }
}

.comments-section {
  border: 1px solid #e5e5e5;
  padding: 25px;
  overflow: hidden;
  margin-top: 50px;
}

.comments-section .ratings {
  margin-bottom: 10px;
}

.comments-section .ratings i {
  color: #ffd57f;
}

.comments-section .title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
}

.comments-section .comment-wrapper {
  margin: 0;
  padding: 0;
}

.comments-section .comment-item {
  padding: 10px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
}

@media only screen and (max-width: 575px) {
  .comments-section .comment-item {
    display: block;
  }
}

@media (min-width: 576px) {
  .comment-item {
    padding: 30px 25px 20px 40px;
  }
}

@media (min-width: 768px) {
  .comment-item {
    margin-bottom: 20px;
  }
}

.comment-item:last-child {
  margin-bottom: 0;
}

.comment-item .comment-thumb {
  width: 70px;
  height: 70px;
  overflow: hidden;
}

.comment-item .comment-content {
  position: relative;
  width: calc(100% - 70px);
  padding-left: 20px;
  transition: all 0.3s;
}

@media only screen and (max-width: 575px) {
  .comment-item .comment-content {
    width: 100%;
    padding-left: 0;
    padding-top: 20px;
  }
}

.comment-item .comment-content .sub-title {
  display: block;
  font-size: 18px;
}

.comment-item .comment-content .sub-title a {
  font-weight: 600;
}

.comment-item .comment-content .sub-title a:hover {
  text-decoration: none;
}

.comment-item .comment-content span {
  font-size: 14px;
  font-style: italic;
  font-weight: 500;
  margin-bottom: 10px;
}

.comment-item .comment-content p {
  margin: 0;
}

.comments-section .comment-wrapper li .reply-wrapper {
  padding-left: 60px;
}

@media only screen and (max-width: 575px) {
  .comments-section .comment-wrapper li .reply-wrapper {
    padding-left: 0;
    margin-left: 0;
  }
}

.comment-item .comment-content .reply-button {
  position: absolute;
  font-weight: 600;
  top: 0;
  right: 0;
  transition: all 0.3s;
}

.comment-item .comment-content .reply-button i {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border: 1px solid #e5e5e5;
  border-radius: 50%;
}

.comment-item .comment-content:hover .reply-button i {
  background-color: var(--main);
  color: white;
}

/*--------------------------------------------------------------
# Others
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Blog Single
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Site Content
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## About Page
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Contact Page
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## 404 Page
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Registration
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Posts and pages
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Comments
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Widgets Content
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Footer Content
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Footer-block
--------------------------------------------------------------*/
.footer-section {
  background-color: #39405A;
  position: relative;
  margin-top: auto;
}

.footer-widget ul li {
  padding: 6px 0;
  color: #fff;
}

.footer-widget ul li a {
  color: #fff;
}

.footer-widget {
  margin-top: -4px;
}

.footer-widget p {
  color: white;
}

.footer-about p {
  margin-top: 20px;
}

.footer-item-area {
  margin-top: 40px;
}

.footer-item-area--style {
  margin-top: 0;
}

.footer-item {
  width: 70%;
  padding: 15px;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
}

.footer-about-icon {
  width: 40px;
}

.footer-about-content li {
  padding: 0 !important;
}

.footer-section .widget-title {
  position: relative;
  color: #fff;
  margin-bottom: 20px;
}

.footer-widget ul li:last-child {
  padding: 6px 0 0 0;
}

.footer-form input {
  border: 1px solid #e5e5e5;
  width: 90%;
  color: #777777;
}

.footer-form input::placeholder {
  color: #777777;
}

.footer-form input[type="submit"] {
  background-color: var(--main);
  color: white;
  font-size: 16px;
}

.footer-social {
  margin-top: 30px;
}

.footer-social li {
  display: inline-block;
}

.footer-social li a {
  position: relative;
  color: white !important;
  width: 40px;
  height: 40px;
  line-height: 45px;
  font-size: 18px;
  text-align: center;
  display: inline-block;
  padding-left: 0 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: 2;
}

.footer-social li a::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--main);
  transform: rotate(45deg);
  z-index: -1;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.footer-social li a:hover,
.footer-social li a.active {
  color: var(--main) !important;
}

.footer-social li a:hover::before,
.footer-social li a.active::before {
  background-color: white;
}

.footer-social li a i {
  margin-right: 0 !important;
}

.footer-social li+li {
  margin-left: 20px;
}

.footer-link-area .footer-link {
  color: white;
  margin-right: 20px;
}

.footer-link-area .footer-link i {
  color: var(--main);
  margin-right: 10px;
}

.privacy-area {
  padding-top: 15px;
  padding-bottom: 10px;
  position: relative;
  background-color: #39405A;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.privacy-area p {
  color: white;
  font-size: 14px;
}

.pricing-tab-menu {
  margin: -3px;
  margin-bottom: 50px;
  justify-content: center;
  border: none;
}

.pricing-tab-menu .nav-item {
  padding: 3px;
}

.pricing-tab-menu .nav-item .nav-link {
  border-radius: 3px;
  border: 1px solid #e5e5e5;
  color: var(--main);
}

.pricing-tab-menu .nav-item .nav-link.active {
  background: var(--main);
  border-color: var(--main);
  color: #fff;
}

.deposit-thumb {
  max-width: unset;
}

.oh {
  overflow: hidden;
}

.header-social {
  display: flex;
  flex-wrap: wrap;
}

.header-social li {
  list-style: none;
}

.header-social li a {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  padding: 0;
}

.select-bar {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  width: auto;
}

.select-bar .current {
  color: #fff;
}

.select-bar::after {
  border-color: rgba(255, 255, 255, 0.7);
}

.header-top-area {
  position: relative;
  z-index: 11;
}

.select-bar .list .option:hover,
.select-bar .list .option.selected {
  color: #292929;
  background: #f8f8f8 !important;
}

.select-bar {
  line-height: 40px;
  height: 40px;
}

@media (max-width: 991px) {
  .select-language .select-bar {
    line-height: unset;
    height: unset;
    padding: 8px 20px !important;
  }
}

@media (max-width: 767px) {
  .select-language .select-bar {
    padding: 6px 20px 6px 10px !important
  }
}

@media (max-width: 767px) {
  .select-language .select-bar {
    padding: 5px 20px 5px 10px !important
  }
}

.profile-section {
  background-color: #f8f8f8;
}

.profile-area {
  background-color: #fff;
  box-shadow: 0px 3px 10px 0px rgb(0 0 0 / 10%);
  padding: 30px 30px;
  border-radius: 5px;
}

.submit-btn {
  padding: 10px 30px;
  border-radius: 5px;
  color: white;
  background-color: var(--main);
  width: 100%;
  margin-top: 10px;
}

.profile-form .nice-select {
  height: 38px;
  line-height: 38px;
  border-radius: 5px;
  border: 1px solid #ced4da;
}

.profile-form .nice-select .option:hover {
  color: white;
}

.profile-form .nice-select .option.selected:hover {
  color: #777777;
}

.profile-thumb-preview {
  width: 100%;
  height: 200px;
  position: relative;
  border-radius: 5px;
  border: 1px solid #ced4da;
}

.profile-thumb-preview .image-preview {
  width: 100%;
  height: 100%;
}

.profile-edit .upload {
  display: none;
}

.profile-edit label {
  text-align: center;
  line-height: 35px;
  font-size: 16px;
  cursor: pointer;
  padding: 2px 25px;
  width: 100%;
  border-radius: 5px;
  background-color: var(--main);
  color: #fff;
}


.client-slider {
  overflow: hidden;
}

.client-slider .swiper-slide-active .client-item {
  background-color: var(--main);
}

.client-slider .swiper-pagination {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: 60px;
}

.client-slider .swiper-pagination-bullet {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: var(--main);
}

.client-slider .swiper-pagination-bullet-active {
  width: 25px;
  border-radius: 10px;
}

.client-item {
  background-color: #272d43;
  border-radius: 5px;
  padding: 20px 30px;
  position: relative;
  z-index: 2;
}

.client-thumb {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
}

.client-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 80px;
  color: white;
  opacity: .1;
  z-index: -1;
}

.client-content {
  padding-top: 10px;
}

.client-content p {
  color: white;
}

.client-footer {
  padding-top: 20px;
}

.client-footer .title {
  color: white;
  margin-bottom: 0;
}

.client-footer .sub-title {
  color: white;
  font-size: 14px;
  font-style: italic;
}

.word--break {
  word-break: break-all;
}

.fz--14 {
  font-size: 14px;
}

.pl--65 {
  padding-left: 65px !important;
}

.register-section .register-form-area .register-form .nice-select.input--group--select {
  height: 36px;
  width: unset;
  line-height: 28px;
  position: absolute;
  top: 3px;
  left: 3px;
  z-index: 99;
  color: #111;
}

.country-select {
  border: 1px solid #ced4da !important;
  border-radius: 5px !important;
}

.country-select .input-group-append {
  margin: 5px;
}

.country-select input {
  border-radius: 0 5px 5px 0 !important;
  padding-left: 7px !important;
  border: none !important;
}

.country-select select {
  appearance: auto;
  -webkit-appearance: auto;
  -moz-appearance: auto;
  border-radius: 5px !important;
}

.country-select select option {
  background-color: #fff;
  color: #292929;
}

.country-select select:focus {
  box-shadow: none;
}

.country-select select:focus-visible {
  outline: none;
}

.nice-select .option:hover {
  color: rgb(119, 119, 119) !important;
}

/* new */
.plan-form .cmn-btn:hover,
.support-ticket .cmn-btn:hover,
.support-ticket .cmn-btn:focus {
  background-color: var(--main);
  color: #fff;
}

.modal .close {
  background: transparent;
  color: #fff;
  font-size: 20px;
  font-weight: 500;
}

.modal .modal-header {
  background-color: var(--main);
}

.modal .modal-title {
  color: #fff;
}

.form-control:focus {
  border-color: var(--main);
  box-shadow: none;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #e9ecef57 !important;
}

.bg-secondary {
  background-color: #272d43 !important;
}

.support-ticket .card-title {
  padding-top: 0.5rem;
}

.support-ticket .input-group-text {
  border-color: transparent !important;
}

*[class*="btn-"] {
  transition: all 0.3s;
  border-radius: 3px;
}

.btn--dark {
  background-color: #10163A !important;
  color: #ffffff;
}

.btn--dark:hover {
  background-color: #0a0e26 !important;
  color: #ffffff;
}

.btn--primary {
  background-color: #4634ff !important;
  color: #ffffff;
}

.btn--primary:hover {
  background-color: #5e50ee !important;
  color: #ffffff;
}

.btn--base,
.btn-icon {
  background-color: var(--main);
  color: #fff;
}

.btn--base {
  display: inline-block;
  border-radius: 3px;
  text-align: center;
  padding: 7px 15px;
}

.btn--base:hover,
.btn--base:focus,
.btn-icon:hover,
.btn-icon:focus {
  background-color: var(--main);
  color: #fff;
}

.bg-base,
.card-header {
  background-color: var(--main);
}

.bg-base {
  border-color: var(--main);
}

.card-title,
.card-header h5 {
  padding-top: 10px;
  color: #fff;
}

.card-header h5 {
  color: #fff;
}

.input-group input {
  padding-left: 5px !important;
}

.copyBtn {
  border-color: var(--main);
}

.mobile-code {
  background-color: var(--main) !important;
  border-color: var(--main) !important;
  color: #fff;
}

.text-base,
.text-base:hover,
.text-base:focus {
  color: var(--main);
}

.f--30 {
  font-size: 30px;
}

/* ===================== Sidebar Custom Css ====================== */
@media (max-width: 1199px) {
  .dashboard-section .dash-user-area .dash-left-user-area .user-details {
    width: 100%;
    background-color: transparent;
    position: relative;
  }
}

@media (max-width: 1199px) {

  /* .dashboard-section .dash-user-area .dash-left-user-area { */
  .dash-user-area {
    position: fixed;
    top: 0;
    z-index: 9999;
    height: 100%;
    min-height: 100%;
    background: #272d43;
    max-width: 320px;
    left: -100%;
    transition: .3s linear;
    overflow: hidden;
    overflow-y: auto;
  }
}

.dash-user-area.show_sidebar {
  left: 0;
}

.sidebar-cross__icon {
  position: absolute;
  right: 15px;
  top: 15px;
  color: #fff;
  font-size: 20px;
  border: 1px solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 3px;
  cursor: pointer;
  transition: .2s linear;
}

.sidebar-cross__icon:hover {
  background-color: red;
  border: red;
}

@media (max-width: 1199px) {
  .dash-link {
    margin-top: 20px;
  }
}


.menu_has_children a::before {
  position: absolute;
  content: "\f054";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  right: -12px;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
  color: #fff;
  font-size: 11px;
  transition: .2s linear;
}

/* ======================= Dashboard Menu Small Device Css Start ================================ */
@media (max-width: 1199px) {
  .dash-link li .sub-menu {
    position: static;
    top: 0;
    width: 100%;
    padding: 0;
    opacity: 1;
    visibility: visible;
    background-color: #272d43;
    background-color: transparent;
    margin-left: 20px;
    margin-top: 10px;
    display: none;
    transition: unset;
  }

  .dash-link li .sub-menu li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.097) !important;
  }

  .dash-link li .sub-menu li a {
    color: #fff !important;
    display: block !important;
    padding: 8px 0px;
  }

  .dash-link li .sub-menu li a::before {
    display: none;
  }

  .menu_has_children a::before {
    right: -12px;
  }

  .menu_has_children a {
    position: relative;
    display: block !important;
  }

  .menu_has_children a.icon_rotate::before {
    transform: rotate(90deg);
  }
}

/* ======================= Dashboard Menu Small Device Css End ================================ */

@media (max-width: 1199px) {
  .sidebar-overlay {
    position: fixed;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.752);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: .2s linear;
  }

  .sidebar-overlay.show_overlay {
    visibility: visible;
    opacity: 1;
  }
}

.dashboard-menu-bar {
  margin-bottom: 10px;
}

.btn-dark:active:focus {
  box-shadow: none;
}

.header-bottom-area {
  transition: .3s linear;
}

.header-bottom-area.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #39405A;
  transition: .3s linear;
  z-index: 99;
  padding-top: 0;
}

@media (max-width: 991px) {
  .header-bottom-area.fixed-header {
    padding-top: 10px;
  }
}

.page-link:focus {
  box-shadow: none;
}

.popular-item-content .title a:hover {
  color: var(--main);
}

.footer-list li a:hover {
  color: var(--main);
}

.currency-addon {
  background-color: var(--main);
  color: #fff;
  border: 1px solid var(--main);
}

label.required:after {
  content: '*';
  color: #DC3545 !important;
  margin-left: 2px;
}

.checkbox-wrapper {
  margin-top: 20px;
}

/* ============================ Table Css Start ============================= */
.table {
  margin: 0;
  font-size: 15px;
  border-collapse: separate;
  border-spacing: 0px;
}

.table thead tr th {
  background-color: var(--main);
  text-align: center;
  font-size: 15px;
  padding: 15px;
  color: #fff;
  font-weight: 500;
  border-bottom: 0;
}

.table thead tr th:first-child {
  text-align: left;
  border-radius: 5px 0 0 0;
}

.table thead tr th:last-child {
  border-radius: 0 5px 0 0;
  text-align: right;
}

.table tbody {
  border: 0 !important;
  background-color: #fff;
}

.table tbody tr:last-child td:first-child {
  border-radius: 0 0 0px 5px;
  /* border-left: 1px solid #eee; */
}

.table tbody tr:last-child td:last-child {
  border-radius: 0 0 5px 0;
  /* border-right: 1px solid #eee; */
}

.table tbody tr td {
  text-align: center;
  vertical-align: middle;
  padding: 20px 15px;
  border-width: 1px;
  border: 0;
  color: #292929;
  border-bottom: 1px solid #eee;
}

.table tbody tr td::before {
  content: attr(data-label);
  font-size: 15px;
  color: #292929;
  font-weight: 500;
  display: none;
  width: 50% !important;
  text-align: left;
}

.table tbody tr td:first-child {
  text-align: left;
  border-left: 1px solid #eee;
}

.table tbody tr td:last-child {
  text-align: right;
  border-right: 1px solid #eee;
}

@media (max-width: 767px) {
  .table--responsive--md thead {
    display: none;
  }

  .table--responsive--md tbody tr {
    display: block;
  }

  .table--responsive--md tbody tr td {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 35px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid #ddd;
  }

  .table--responsive--md tbody tr td:last-child {
    border: none;
  }

  .table--responsive--md tbody tr td::before {
    display: block;
  }
}

@media (max-width: 767px) {
  .table--responsive--md tbody tr td {
    border: 0;
  }

  .table--responsive--md tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--md tbody tr:nth-child(odd) {
    background-color: #de980512;
  }

  .table--responsive--md tbody tr:nth-child(even) {
    background-color: #39405a08;
  }
}

@media (max-width: 991px) {
  .table--responsive--lg thead {
    display: none;
  }

  .table--responsive--lg tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--lg tbody tr:nth-child(odd) {
    background-color: #de980512;
  }

  .table--responsive--lg tbody tr {
    display: block;
  }

  .table--responsive--lg tbody tr:nth-child(even) {
    background-color: #39405a08;
  }

  .table--responsive--lg tbody tr td {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 35px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid #333;
  }

  .table--responsive--lg tbody tr td:last-child {
    border: none;
  }

  .table--responsive--lg tbody tr td::before {
    display: block;
  }
}

@media (max-width: 991px) {
  .table--responsive--lg tbody tr td {
    border: none;
  }
}

@media (max-width: 1199px) {
  .table--responsive--xl thead {
    display: none;
  }

  .table--responsive--xl tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--xl tbody tr:nth-child(odd) {
    background-color: #de980512;
  }

  .table--responsive--xl tbody tr {
    display: block;
  }

  .table--responsive--xl tbody tr:nth-child(even) {
    background-color: #39405a08;
  }

  .table--responsive--xl tbody tr td {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 35px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid #333;
  }

  .table--responsive--xl tbody tr td:last-child {
    border: none;
  }

  .table--responsive--xl tbody tr td::before {
    display: block;
  }
}

@media (max-width: 1199px) {
  .table--responsive--xl tbody tr td {
    border: 0;
  }
}

/* ============================ Table Css End ============================= */

.social-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.social-list__item {
  margin: 0 5px;
}

.social-list__link {
  width: 40px;
  height: 40px;
  display: flex !important;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  transition: 0.3s;
  cursor: pointer;
  color: #fff;
  background-color: #39405a;
}

@media (max-width: 575px) {
  .social-list__link {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
}

.social-list__link.active,
.social-list__link:hover {
  background-color: #de9805;
  ;
  color: #fff;
}

.input-group .input-group-text {
  padding: 8px 15px;
  line-height: 1;
}

.ticket-attachments-message {
  line-height: 1;
}

#confirmationModal .btn--base {
  padding: 6px 15px;
}

@media (max-width: 767px) {
  .cmn-btn {
    padding: 6px 15px !important;
    ;
  }
}

@media (max-width: 424px) {
  .cmn-btn {
    padding: 5px 8px !important;
  }
}

.custom--card {
  box-shadow: 0px 3px 10px 0px rgb(0 0 0 / 10%);
  border-radius: 5px;
  border: 0;
}

.form-check .form-check-input {
  box-shadow: none;
  background-color: transparent;
  box-shadow: none !important;
  border: 0;
  border: 1px solid var(--main);
  position: relative;
  border-radius: 3px;
  width: 16px !important;
  height: 16px !important;
  padding: 0;
}

.form-check .form-check-label {
  display: inline-block;
}

.form-check .form-check-input:checked {
  background-color: var(--main) !important;
  border-color: var(--main) !important;
  box-shadow: none;
}

.form-check .form-check-input[type=checkbox] {
  background-image: none;
}

.form-check .form-check-input:checked::before {
  position: absolute;
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: rgb(255, 255, 255);
  font-size: 10px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.form-check:last-child {
  margin-bottom: 0;
}

.form-check:last-child .form-check-label {
  margin-bottom: 0;
}

.form-check {
  margin-bottom: 0;
}

@media (max-width: 767px) {
  .dashboard-header .navbar-collapse .main-menu li a.cmn-btn {
    padding: 5px 16px !important;
  }
}

.usernameExist,
.mobileExist {
  display: block;
  position: absolute;
  top: 100%;
  left: 0;
}


.go-to-home {
  position: absolute;
  top: 45px;
  right: 45px;
}

@media (max-width: 991px) {
  .go-to-home {
    top: 5px;
    right: 5px;
  }
}




/* Header Dropdown Menu Responsive Start */
.header-bottom-area .navbar-collapse .main-menu li .sub-menu li a::before {
  display: none;
}

@media (max-width: 991px) {

  .header-bottom-area .navbar-collapse .main-menu {
    padding: 20px;
    background-color: #272d43;
    border-radius: 0 0 5px 5px;
  }

  .header-bottom-area .navbar-collapse .main-menu li a {
    color: #fff !important;
  }

  .header-bottom-area .menu_has_children .sub-menu li {
    width: 100%;
  }

  .header-bottom-area .navbar-collapse .main-menu .menu_has_children.show .sub-menu,
  .header-bottom-area .navbar-collapse .main-menu .menu_has_children.show .sub-menu {
    display: flex !important;
    flex-wrap: wrap;
  }


  .header-bottom-area .navbar-collapse .main-menu li a {
    padding: 12px 0;
    display: block;
  }

  .header-bottom-area .navbar-collapse .main-menu li .cmn-btn {
    padding: 8px 20px;
  }

  .header-bottom-area .navbar-collapse .main-menu li .sub-menu {
    transition: unset;
    background-color: transparent;
    padding: 0;
    visibility: visible;
    opacity: 1;

  }

  .header-bottom-area .navbar-collapse .main-menu li a {
    padding: 12px 0;
    display: block;
  }

  .header-bottom-area .navbar-collapse .main-menu li .cmn-btn {
    padding: 8px 20px;
  }

  .header-bottom-area .menu_has_children .sub-menu {
    position: static !important;
    width: 100% !important;
    box-shadow: none !important;
    display: none;
  }

  .header-bottom-area .navbar-collapse .main-menu li .sub-menu li a {
    color: #fff !important;
    padding-left: 0;
  }

  .header-bottom-area .navbar-collapse .main-menu li .sub-menu li {
    border-bottom: 0;
    margin-left: 20px;
  }

  .menu_has_children a::before {
    right: 10px;
  }

  .header-bottom-area .navbar-collapse .main-menu li .sub-menu li a {
    color: #fff !important;
  }
}

/* Header Dropdown Menu Responsive End */

.language-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 5px 12px;
  border-radius: 4px;
  width: 140px;
  background-color: rgb(255 255 255);
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  height: 38px;
}

.language_flag {
  flex-shrink: 0;
  display: flex;
}

.language_flag img {
  height: 20px;
  width: 20px;
  object-fit: cover;
  border-radius: 50%;
}

.language-wrapper.show .collapse-icon {
  transform: rotate(180deg)
}

.collapse-icon {
  font-size: 14px;
  display: flex;
  transition: all linear 0.2s;
}

.language_text_select {
  font-size: 14px;
  font-weight: 400;
}

.language-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.language_text {
  color: #000000;
}

.language-list {
  display: flex !important;
  align-items: center;
  gap: 6px;
  padding: 6px 12px !important;
  cursor: pointer;
}

.language .dropdown-menu {
  position: absolute;
  -webkit-transition: ease-in-out 0.1s;
  transition: ease-in-out 0.1s;
  opacity: 0;
  visibility: hidden;
  top: 100%;
  display: unset;
  background: #ffffff;
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
  min-width: 150px;
  padding: 7px 0 !important;
  border-radius: 8px;
  border: 1px solid rgb(255 255 255 / 10%);
}

.language .dropdown-menu.show {
  visibility: visible;
  opacity: 1;
}


.btn.disabled,
.btn:disabled {
  background: rgb(0 0 0 / 10%);
  color: #000;
}