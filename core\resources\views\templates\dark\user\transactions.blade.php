@extends($activeTemplate . 'layouts.master')
@section('content')
    <div class="row gy-3 justify-content-center">
        <div class="col-md-12  d-lg-none d-block">
            <div class="show-filter text-end">
                <button class="btn--base showFilterBtn" type="button"><i class="las la-filter"></i> @lang('Filter')</button>
            </div>
        </div>

        <div class="col-12">
            <div class="card custom--card responsive-filter-card">
                <div class="card-body">
                    <form action="">
                        <div class="d-flex flex-wrap gap-4">
                            <div class="flex-grow-1">
                                <label>@lang('Transaction Number')</label>
                                <input class="form-control form--control" name="search" type="text" value="{{ request()->search }}">
                            </div>
                            <div class="flex-grow-1">
                                <label>@lang('Type')</label>
                                <select class="custom--select form-control form--control" name="type">
                                    <option value="">@lang('All')</option>
                                    <option @selected(request()->type == '+') value="+">@lang('Plus')</option>
                                    <option @selected(request()->type == '-') value="-">@lang('Minus')</option>
                                </select>
                            </div>
                            <div class="flex-grow-1">
                                <label>@lang('Coin Code')</label>
                                <select class="custom--select form-control form--control" name="coin_code">
                                    <option value="">@lang('Any')</option>
                                    @foreach ($coins as $coin)
                                        <option @selected(request()->coin_code == $coin->currency) value="{{ $coin->currency }}">{{ __(strtoupper($coin->currency)) }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="flex-grow-1">
                                <label>@lang('Remark')</label>
                                <select class="custom--select form-control form--control" name="remark">
                                    <option value="">@lang('Any')</option>
                                    @foreach ($remarks as $remark)
                                        <option @selected(request()->remark == $remark->remark) value="{{ $remark->remark }}">{{ __(keyToTitle($remark->remark)) }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="flex-grow-1 align-self-end">
                                <button class="btn--base w-100 btn-sm"><i class="las la-filter"></i> @lang('Filter')</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="dashboard-table">
                @include($activeTemplate . 'partials.transaction_table', ['transactions' => $transactions])
                <div class="pagination-container mt-4">
                    {{ paginateLinks($transactions) }}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
<style>
    /* Pagination responsive styles */
    /* Hide unnecessary elements */
    .pagination-wrapper .d-flex.justify-content-between.flex-fill,
    .pagination-container .d-flex.justify-content-between.flex-fill {
        display: none !important;
    }

    /* Main container for pagination */
    .pagination-wrapper,
    .pagination-container {
        width: 100%;
        overflow-x: auto;
        padding: 5px 0;
        margin: 10px 0;
        scrollbar-width: thin;
    }

    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        display: block !important;
        width: 100%;
    }

    /* Center the pagination buttons */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-wrapper nav,
    .pagination-wrapper .pagination,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-container nav,
    .pagination-container .pagination,
    .pagination-container nav > ul {
        display: flex;
        justify-content: center !important;
        width: 100%;
    }

    /* Improved pagination styling for all devices */
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
        margin: 0;
        padding: 0;
        justify-content: center;
        max-width: 100%;
    }

    /* Handle large number of pagination items - general fallback */
    .pagination.pagination-sm {
        justify-content: center;
        padding: 5px 0;
    }

    /* Ensure consistent spacing between pagination items */
    .pagination .page-item {
        margin: 3px;
    }

    /* Style for pagination links */
    .pagination .page-item .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        padding: 0 10px;
        border-radius: 4px !important;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    /* Style for forward and back buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item.previous .page-link,
    .pagination .page-item.next .page-link {
        font-size: 14px;
        padding: 0 12px;
        min-width: 40px; /* Slightly wider for navigation buttons */
    }

    /* Ensure arrow icons are visible */
    .pagination .page-item .page-link span[aria-hidden="true"] {
        display: inline-block;
        line-height: 1;
    }

    /* Style for disabled pagination items */
    .pagination .page-item.disabled .page-link {
        background-color: hsl(0deg 0% 100% / 40%);
        opacity: 0.7;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 575px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 3px 0;
            margin: 8px 0;
        }

        .pagination {
            gap: 3px;
        }

        .pagination .page-item {
            margin: 2px;
        }

        .pagination .page-item .page-link {
            min-width: 32px;
            height: 32px;
            font-size: 13px;
        }

        /* Adjust forward/back buttons on small screens */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 36px;
            padding: 0 10px;
        }
    }

    /* Responsive adjustments for very small screens */
    @media (max-width: 375px) {
        .pagination .page-item .page-link {
            min-width: 30px;
            height: 30px;
            font-size: 12px;
            padding: 0 8px;
        }
    }

    /* Tablet-specific adjustments */
    @media (min-width: 768px) and (max-width: 1024px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 8px 0;
            margin: 12px 0;
        }

        .pagination {
            gap: 6px;
        }

        .pagination .page-item {
            margin: 3px;
        }

        .pagination .page-item .page-link {
            min-width: 38px;
            height: 38px;
            font-size: 15px;
        }

        /* Adjust forward/back buttons on tablets */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 42px;
            padding: 0 12px;
            font-size: 15px;
        }
    }

    /* Transaction table responsive styles */
    .dashboard-table {
        overflow-x: auto;
        width: 100%;
        max-width: 100%;
        -webkit-overflow-scrolling: touch;
        border-radius: 5px;
    }

    @media (max-width: 1024px) {
        .dashboard-table {
            margin: 0 -10px;
            padding: 0 10px;
            width: calc(100% + 20px);
        }
    }

    @media (max-width: 991px) {
        .dashboard-table {
            margin: 0 -8px;
            padding: 0 8px;
        }
    }

    @media (max-width: 767px) {
        .dashboard-table {
            margin: 0 -6px;
            padding: 0 6px;
        }
    }

    @media (max-width: 575px) {
        .dashboard-table {
            margin: 0 -5px;
            padding: 0 5px;
            width: calc(100% + 10px);
        }
    }

    @media (max-width: 375px) {
        .dashboard-table {
            margin: 0 -3px;
            padding: 0 3px;
        }
    }

    /* Specific iPad adjustments */
    @media (width: 1024px) and (height: 1366px),
           (width: 1024px) and (height: 600px),
           (width: 768px) and (height: 1024px),
           (width: 820px) and (height: 1180px),
           (width: 912px) and (height: 1368px),
           (width: 853px) and (height: 1280px) {
        .dashboard-table {
            margin: 0 -15px;
            padding: 0 15px;
            width: calc(100% + 30px);
            border-radius: 5px;
        }
    }

    /* General tablet adjustments for all orientations */
    @media only screen and (min-width: 768px) and (max-width: 1024px) {
        .dashboard-table {
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
    }
</style>
@endpush
