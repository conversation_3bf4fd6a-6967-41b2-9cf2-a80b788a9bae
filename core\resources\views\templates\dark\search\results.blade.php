@extends($activeTemplate . 'layouts.frontend')
@section('content')
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="search-results-header mb-4">
                <h2 class="text-white">Search Results for: "{{ $query }}"</h2>
                <p class="text-light">Found {{ $tokens->total() }} results</p>
                @if(strtolower($query) === 'newest')
                    <div class="alert alert-info mt-2">
                        <i class="las la-info-circle"></i> Showing tokens added to the site within the last 24 hours, sorted by newest first.
                    </div>
                @endif
            </div>

            <div class="search-results-container">
                @if($tokens->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-dark table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Coin</th>
                                    <th>Chain</th>
                                    <th>Price</th>
                                    <th>Age</th>
                                    <th>TXN</th>
                                    <th>Volume</th>
                                    <th>1h</th>
                                    <th>24h</th>
                                    <th>LP</th>
                                    <th>MCap</th>
                                    <th>Votes</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($tokens as $token)
                                    @php
                                        // Format price changes with color
                                        $priceChange24h = $token->price_change_24h;
                                        $priceChange5m = $token->price_change_5m;
                                        $priceChange1h = $token->price_change_1h;
                                        $priceChange6h = $token->price_change_6h;
                                        $change24hClass = floatval($priceChange24h) >= 0 ? 'text-success' : 'text-danger';
                                        $change5mClass = floatval($priceChange5m) >= 0 ? 'text-success' : 'text-danger';
                                        $change1hClass = floatval($priceChange1h) >= 0 ? 'text-success' : 'text-danger';
                                        $change6hClass = floatval($priceChange6h) >= 0 ? 'text-success' : 'text-danger';

                                        // Format chain ID for display
                                        $chainDisplay = formatBlockchainName($token->chain_id);
                                    @endphp
                                    <tr>
                                        <td>
                                            <span class="rank-number">{{ $token->global_rank ?? 'N/A' }}</span>
                                        </td>
                                        <td>
                                            <div class="position-relative">
                                                <div class="token-actions-wrapper mb-1">
                                                    <span class="token-action-icon promote-icon" data-bs-toggle="tooltip" data-bs-placement="top" title="Promote" data-token-address="{{ $token->token_address }}" data-chain-id="{{ $token->chain_id }}" data-token-symbol="{{ $token->token_symbol }}">⚡</span>
                                                    <span class="token-action-icon vote-icon" data-bs-toggle="tooltip" data-bs-placement="top" title="Vote" data-token-address="{{ $token->token_address }}" data-chain-id="{{ $token->chain_id }}" data-token-symbol="{{ $token->token_symbol }}">🔥</span>
                                                    <span class="token-action-icon watchlist-icon" data-bs-toggle="tooltip" data-bs-placement="top" title="Add to Watchlist" data-token-address="{{ $token->token_address }}" data-chain-id="{{ $token->chain_id }}" data-token-symbol="{{ $token->token_symbol }}">⭐</span>
                                                </div>
                                                <a href="{{ route('token.details', ['chainId' => $token->chain_id, 'tokenAddress' => $token->token_address]) }}" class="d-flex align-items-center text-decoration-none">
                                                    <div class="me-2">
                                                        @if($token->image_url)
                                                            <img src="@if(!filter_var($token->image_url, FILTER_VALIDATE_URL) && strpos($token->image_url, 'http') !== 0){{ asset('assets/images/coin_logos/'.$token->image_url) }}@else{{ $token->image_url }}@endif" alt="{{ $token->token_name }}" class="coin-icon" width="24" height="24">
                                                        @else
                                                            <div class="coin-icon-placeholder"></div>
                                                        @endif
                                                    </div>
                                                    <div class="token-info-container">
                                                        <div class="fw-bold text-white">{{ $token->token_symbol }}</div>
                                                        <div class="text-muted small">{{ Str::limit($token->token_name, 20) }}</div>
                                                    </div>
                                                </a>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge chain-badge">{{ $chainDisplay }}</span>
                                        </td>
                                        <td>
                                            @if($token->is_presale)
                                                <span class="presale-badge-list">Presale</span>
                                            @elseif($token->is_fair_launch)
                                                <span class="presale-badge-list">Fair Launch</span>
                                            @elseif($token->price_usd)
                                                ${{ $token->price_usd }}
                                            @else
                                                <span class="softcap">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($token->is_presale || $token->is_fair_launch)
                                                @php
                                                    // Get the corresponding SubmitCoin record for presale or fair launch tokens
                                                    $submitCoin = \App\Models\SubmitCoin::where('contract_address', $token->token_address)
                                                        ->orderBy('created_at', 'desc')
                                                        ->first();

                                                    if ($submitCoin && $submitCoin->presale_start_date) {
                                                        try {
                                                            $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', $submitCoin->presale_start_date);
                                                            $now = \Carbon\Carbon::now();
                                                            $daysUntil = (int)$now->diffInDays($startDate, false);

                                                            if ($daysUntil > 0) {
                                                                // Future date
                                                                $dayText = $daysUntil == 1 ? 'Day' : 'Days';
                                                                echo 'Starts in ' . $daysUntil . ' ' . $dayText;
                                                            } elseif ($daysUntil == 0) {
                                                                // Today
                                                                echo 'Started Today';
                                                            } else {
                                                                // Past date
                                                                $dayText = abs($daysUntil) == 1 ? 'Day' : 'Days';
                                                                echo 'Started ' . abs($daysUntil) . ' ' . $dayText . ' ago';
                                                            }
                                                        } catch (\Exception $e) {
                                                            // If date format is invalid, fall back to created_at
                                                            if($token->created_at) {
                                                                $createdAt = \Carbon\Carbon::parse($token->created_at);
                                                                $diff = $createdAt->diffInSeconds();

                                                                if ($diff < 60) {
                                                                    echo $diff . 's';
                                                                } elseif ($diff < 3600) {
                                                                    echo floor($diff / 60) . 'm';
                                                                } elseif ($diff < 86400) {
                                                                    echo floor($diff / 3600) . 'h';
                                                                } else {
                                                                    echo floor($diff / 86400) . 'd';
                                                                }
                                                            } else {
                                                                echo '<span class="softcap">-</span>';
                                                            }
                                                        }
                                                    } else {
                                                        // No presale date, fall back to created_at
                                                        if($token->created_at) {
                                                            $createdAt = \Carbon\Carbon::parse($token->created_at);
                                                            $diff = $createdAt->diffInSeconds();

                                                            if ($diff < 60) {
                                                                echo $diff . 's';
                                                            } elseif ($diff < 3600) {
                                                                echo floor($diff / 60) . 'm';
                                                            } elseif ($diff < 86400) {
                                                                echo floor($diff / 3600) . 'h';
                                                            } else {
                                                                echo floor($diff / 86400) . 'd';
                                                            }
                                                        } else {
                                                            echo '<span class="softcap">-</span>';
                                                        }
                                                    }
                                                @endphp
                                            @elseif($token->token_age)
                                                {{ $token->token_age }} days
                                            @else
                                                <span class="softcap">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($token->is_presale || $token->is_fair_launch)
                                                <span class="presale-label">Softcap</span><br>
                                                @php
                                                    $softcap = $token->softcap ? formatCapValue($token->softcap) : '-';
                                                @endphp
                                                <span class="presale-value-list">{{ $softcap }}</span>
                                            @elseif($token->txn_24h)
                                                {{ number_format($token->txn_24h) }}
                                            @else
                                                <span class="softcap">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($token->is_presale || $token->is_fair_launch)
                                                <span class="presale-label">Hardcap</span><br>
                                                @php
                                                    $hardcap = $token->hardcap ? formatCapValue($token->hardcap) : '-';
                                                @endphp
                                                <span class="presale-value-list">{{ $hardcap }}</span>
                                            @elseif($token->volume_24h)
                                                ${{ formatAmount($token->volume_24h) }}
                                            @else
                                                <span class="softcap">-</span>
                                            @endif
                                        </td>

                                        <td class="{{ $change1hClass }}">
                                            @if($token->is_presale || $token->is_fair_launch)
                                                <span class="presale-na-list">N/A</span>
                                            @elseif($token->price_change_1h)
                                                {{ $token->price_change_1h > 0 ? '+' : '' }}{{ $token->price_change_1h }}%
                                            @else
                                                <span class="softcap">-</span>
                                            @endif
                                        </td>

                                        <td class="{{ $change24hClass }}">
                                            @if($token->is_presale || $token->is_fair_launch)
                                                <span class="presale-na-list">N/A</span>
                                            @elseif($token->price_change_24h)
                                                {{ $token->price_change_24h > 0 ? '+' : '' }}{{ $token->price_change_24h }}%
                                            @else
                                                <span class="softcap">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($token->is_presale || $token->is_fair_launch)
                                                <span class="presale-na-list">N/A</span>
                                            @elseif($token->liquidity_usd)
                                                ${{ formatAmount($token->liquidity_usd) }}
                                            @else
                                                <span class="softcap">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($token->is_presale || $token->is_fair_launch)
                                                <span class="presale-na-list">N/A</span>
                                            @elseif($token->market_cap)
                                                ${{ formatAmount($token->market_cap) }}
                                            @else
                                                <span class="softcap">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="vote-count" data-chain-id="{{ $token->chain_id }}" data-token-address="{{ $token->token_address }}">
                                                <span class="fire-emoji-small">🔥</span> <span class="vote-count-value">{{ $token->vote_count ?? 0 }}</span>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="pagination-container mt-4">
                        <div class="search-pagination-wrapper">
                            {{ $tokens->appends(['query' => $query])->links() }}
                        </div>
                    </div>
                @else
                    <div class="no-results text-center py-5">
                        <i class="las la-search fa-3x mb-3 text-muted"></i>
                        <h4 class="text-white">No tokens found</h4>
                        <p class="text-light">Try searching with different keywords</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@auth
<!-- Promote Token Modal -->
<div class="modal custom--modal fade" id="promoteTokenModal" tabindex="-1" aria-labelledby="promoteTokenModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promoteTokenModalLabel">@lang('Promote Token')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('user.token.promote') }}" method="POST">
                    @csrf
                    <input type="hidden" name="chain_id" value="">
                    <input type="hidden" name="token_address" value="">
                    <div class="form-group">
                        <label>@lang('Token')</label>
                        <div class="input-group">
                            <span class="form-control token-name-display"></span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Current Promote Credits')</label>
                        <div class="input-group">
                            <span class="form-control">{{ auth()->user()->promote_credits }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Promotion Days')</label>
                        <div class="input-group">
                            <input type="number" name="days" class="form-control" min="1" max="100" value="1" required>
                            <span class="input-group-text">Days</span>
                        </div>
                        <small class="text-muted">Each day costs 1 promote credit. The token will be displayed in the Promoted Coins section for the specified number of days.</small>
                    </div>
                    <div class="form-group mt-3 text-end">
                        <button type="submit" class="btn btn--base w-100">@lang('Confirm Promotion')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Vote Token Modal -->
<div class="modal custom--modal fade" id="voteTokenModal" tabindex="-1" aria-labelledby="voteTokenModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="voteTokenModalLabel">@if(auth()->user()->trend_votes > 0)@lang('Use Trend Votes')@else Buy Trend Votes @endif</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="las la-info-circle"></i> You've already used your free daily vote for this token. You can use your trend votes to give it an extra boost to trend higher on the homepage.
                </div>
                <form action="{{ route('token.trend-vote') }}" method="POST">
                    @csrf
                    <input type="hidden" name="chain_id" value="">
                    <input type="hidden" name="token_address" value="">
                    <div class="form-group">
                        <label>@lang('Token')</label>
                        <div class="input-group">
                            <span class="form-control token-name-display"></span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Available Trend Votes')</label>
                        <div class="input-group">
                            <span class="form-control">{{ auth()->user()->trend_votes }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Number of Trend Votes to Use')</label>
                        <div class="input-group">
                            <input type="number" name="quantity" class="form-control" min="1" max="{{ auth()->user()->trend_votes }}" value="1" required>
                        </div>
                    </div>
                    <div class="form-group mt-3 text-end">
                        @if(auth()->user()->trend_votes > 0)
                            <button type="submit" class="btn btn--base w-100">@lang('Use Trend Votes')</button>
                        @else
                            <a href="{{ route('user.plans.buy.trend.votes') }}" class="btn btn--base w-100">Buy Trend Votes</a>
                        @endif
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endauth

@endsection

@push('style')
<style>
    /* Sitewide Notification Styles - Ensure consistency with other pages */
    .sitewide-notification {
        background-color: #BE8400;
        color: #fff;
        padding: 10px 0;
        text-align: center;
        font-weight: 500;
    }
    .sitewide-notification .notification-content {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
    }
    .sitewide-notification a {
        color: #fff;
        text-decoration: none;
    }
    .sitewide-notification.has-link a:hover {
        text-decoration: none;
    }
    .notification-btn {
        background-color: #1C2631;
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.3s;
    }
    .notification-btn:hover {
        background-color: #2A3642;
    }

    /* Responsive styles for sitewide notification */
    @media (min-width: 768px) and (max-width: 1024px) {
        .sitewide-notification .notification-content {
            gap: 10px;
        }
        .notification-btn {
            padding: 5px 15px;
            font-size: 13px;
            min-width: 110px;
            text-align: center;
        }
    }

    @media (max-width: 767px) {
        .sitewide-notification .notification-content {
            flex-direction: column;
            gap: 10px;
            padding: 5px 0;
        }
        .notification-btn {
            padding: 5px 15px;
            font-size: 13px;
            display: inline-block;
            margin-bottom: 5px;
            min-width: 120px;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .sitewide-notification {
            padding: 8px 0;
        }
        .notification-btn {
            padding: 4px 12px;
            font-size: 12px;
            width: auto;
            min-width: 100px;
        }
    }

    /* Specific tablet device styles */
    @media (width: 768px) and (height: 1024px),
           (width: 820px) and (height: 1180px),
           (width: 912px) and (height: 1368px),
           (width: 853px) and (height: 1280px),
           (width: 1024px) and (height: 1366px) {
        .notification-btn {
            padding: 6px 16px;
            font-size: 14px;
            min-width: 120px;
            text-align: center;
        }
    }

    /* Global Banner Styles */
    .global-banner-container {
        background-color: var(--section-bg);
        padding: 15px 0;
    }

    .search-results-container {
        background-color: var(--section-bg);
        padding: 20px;
        border-radius: 5px;
    }

    .rank-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #BE8400;
        color: white;
        min-width: 24px;
        height: 24px;
        padding: 0 8px; /* Increased horizontal padding */
        border-radius: 4px;
        font-weight: bold;
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
        overflow: visible; /* Ensure text doesn't get cut off */
        box-sizing: content-box; /* Ensure padding is added to the content width */
        max-width: none; /* Remove any max-width constraints */
        width: auto; /* Allow the width to adjust to content */
    }

    .coin-icon {
        border-radius: 50%;
        object-fit: cover;
    }

    .coin-icon-placeholder {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #333;
    }

    /* Token name and symbol alignment */
    .token-info-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .chain-badge {
        background-color: #BE8400;
        color: #ffffff;
        font-weight: 500;
    }

    .presale-badge-list {
        background-color: #BE8400;
        color: #ffffff;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.85rem;
        display: inline-block;
    }

    .presale-na-list {
        color: #ffffff !important;
        font-weight: bold !important;
        font-size: 0.9rem !important;
    }

    .presale-label {
        color: #BE8400 !important;
        font-weight: bold !important;
        font-size: 0.8rem !important;
    }

    .presale-value-list {
        color: #ffffff !important;
        font-weight: bold !important;
        font-size: 0.9rem !important;
    }

    .softcap {
        color: rgba(255, 255, 255, 0.5);
    }

    .vote-count {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
    }

    .fire-emoji-small {
        font-size: 14px;
        margin-right: 4px;
    }

    /* Token action icons styling */
    .token-actions-wrapper {
        display: flex;
        justify-content: flex-start;
        gap: 10px;
        margin-bottom: 5px;
    }

    .token-action-icon {
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-block;
    }

    .promote-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.7));
    }

    .vote-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 100, 0, 0.7));
    }

    .watchlist-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 0 2px rgba(255, 255, 0, 0.7));
    }

    .watchlist-icon.active {
        color: #FFD700;
    }

    /* Pagination responsive styles */
    /* Hide unnecessary elements */
    .pagination-wrapper .d-flex.justify-content-between.flex-fill,
    .pagination-container .d-flex.justify-content-between.flex-fill {
        display: none !important;
    }

    /* Main container for pagination */
    .pagination-wrapper,
    .pagination-container {
        width: 100%;
        overflow-x: auto;
        padding: 5px 0;
        margin: 10px 0;
        scrollbar-width: thin;
    }

    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        display: block !important;
        width: 100%;
    }

    /* Left align the "Showing X to Y of Z results" text */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:first-child {
        text-align: left !important;
        margin-bottom: 10px;
    }

    /* Center the pagination buttons */
    .pagination-wrapper .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-wrapper nav,
    .pagination-wrapper .pagination,
    .pagination-container .flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between > div:last-child,
    .pagination-container nav,
    .pagination-container .pagination,
    .pagination-container nav > ul {
        display: flex;
        justify-content: center !important;
        width: 100%;
    }

    /* Improved pagination styling for all devices */
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
        margin: 0;
        padding: 0;
        justify-content: center;
        max-width: 100%;
        --bs-pagination-color: #fff;
        --bs-pagination-bg: #222;
        --bs-pagination-border-color: #444;
        --bs-pagination-hover-color: #fff;
        --bs-pagination-hover-bg: #BE8400;
        --bs-pagination-hover-border-color: #BE8400;
        --bs-pagination-active-color: #fff;
        --bs-pagination-active-bg: #BE8400;
        --bs-pagination-active-border-color: #BE8400;
    }

    /* Handle large number of pagination items - general fallback */
    .pagination.pagination-sm {
        justify-content: center;
        padding: 5px 0;
    }

    /* Ensure consistent spacing between pagination items */
    .pagination .page-item {
        margin: 3px;
    }

    /* Style for pagination links */
    .pagination .page-item .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 56px; /* Increased from 36px to accommodate up to 7 digits */
        height: 36px;
        padding: 0 10px;
        border-radius: 4px !important;
        font-size: 14px;
        transition: all 0.3s ease;
        overflow: hidden; /* Prevent content from overflowing */
        text-overflow: ellipsis; /* Add ellipsis for any overflow */
        white-space: nowrap; /* Keep text on a single line */
    }

    /* Style for forward and back buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item.previous .page-link,
    .pagination .page-item.next .page-link {
        font-size: 14px;
        padding: 0 12px;
        min-width: 40px; /* Slightly wider for navigation buttons */
    }

    /* Ensure arrow icons are visible */
    .pagination .page-item .page-link span[aria-hidden="true"] {
        display: inline-block;
        line-height: 1;
    }

    /* Style for disabled pagination items */
    .pagination .page-item.disabled .page-link {
        background-color: hsl(0deg 0% 100% / 40%);
        opacity: 0.7;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 575px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 3px 0;
            margin: 8px 0;
        }

        .pagination {
            gap: 3px;
        }

        .pagination .page-item {
            margin: 2px;
        }

        .pagination .page-item .page-link {
            min-width: 48px; /* Increased from 32px to accommodate larger numbers */
            height: 32px;
            font-size: 13px;
        }

        /* Adjust forward/back buttons on small screens */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 36px;
            padding: 0 10px;
        }

        /* Token name and symbol alignment for mobile */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        .rank-number {
            min-width: 20px;
            height: 20px;
            padding: 0 6px; /* Increased horizontal padding */
            font-size: 10px;
            overflow: visible;
            box-sizing: content-box;
            max-width: none;
            width: auto;
        }
    }

    /* Responsive adjustments for very small screens */
    @media (max-width: 375px) {
        .pagination .page-item .page-link {
            min-width: 44px; /* Increased from 30px to accommodate larger numbers */
            height: 30px;
            font-size: 12px;
            padding: 0 6px; /* Slightly reduced padding to maintain overall width */
        }

        /* Token name and symbol alignment for extra small devices */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        .token-info-container .fw-bold {
            font-size: 12px;
        }

        .token-info-container .text-muted.small {
            font-size: 10px;
        }

        .rank-number {
            min-width: 18px;
            height: 18px;
            padding: 0 5px; /* Increased horizontal padding */
            font-size: 9px;
            overflow: visible;
            box-sizing: content-box;
            max-width: none;
            width: auto;
        }
    }

    /* Tablet-specific adjustments */
    @media (min-width: 768px) and (max-width: 1024px) {
        .pagination-wrapper,
        .pagination-container {
            padding: 8px 0;
            margin: 12px 0;
        }

        .pagination {
            gap: 6px;
        }

        .pagination .page-item {
            margin: 3px;
        }

        .pagination .page-item .page-link {
            min-width: 58px; /* Increased from 38px to accommodate larger numbers */
            height: 38px;
            font-size: 15px;
        }

        /* Adjust forward/back buttons on tablets */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link,
        .pagination .page-item.previous .page-link,
        .pagination .page-item.next .page-link {
            min-width: 42px;
            padding: 0 12px;
            font-size: 15px;
        }

        /* Token name and symbol alignment for tablets */
        .token-info-container {
            width: 100%;
            text-align: left;
        }

        .rank-number {
            min-width: 22px;
            height: 22px;
            padding: 0 7px; /* Increased horizontal padding */
            font-size: 11px;
            overflow: visible;
            box-sizing: content-box;
            max-width: none;
            width: auto;
        }
    }

    .table {
        color: var(--text-color);
        font-size: 14px;
    }

    .table thead th {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-weight: 500;
        font-size: 13px;
        white-space: nowrap;
    }

    /* Ensure the first header column (rank number) has enough width */
    .table thead th:first-child {
        min-width: 50px; /* Match the width of the first column in tbody */
    }

    .table tbody td {
        padding: 12px 8px;
        vertical-align: middle;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        white-space: nowrap;
    }

    /* Ensure the first column (rank number) has enough width */
    .table tbody td:first-child {
        min-width: 50px; /* Provide enough space for large rank numbers */
    }

    /* Custom notification styles */
    .custom-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 350px;
        transform: translateX(400px);
        transition: transform 0.3s ease-in-out;
    }

    .custom-notification.show {
        transform: translateX(0);
    }

    /* Custom notification popup styles - renamed to avoid conflict with sitewide notification */
    .custom-notification .notification-content {
        background-color: #222;
        border-left: 4px solid #BE8400;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        padding: 15px;
        position: relative;
    }

    /* Custom notification popup icon */
    .custom-notification .notification-icon {
        color: #BE8400;
        font-size: 24px;
        margin-right: 15px;
    }

    /* Custom notification popup text */
    .custom-notification .notification-text h5 {
        color: #fff;
        margin-bottom: 5px;
        font-size: 16px;
    }

    .custom-notification .notification-text p {
        color: #ccc;
        margin-bottom: 0;
        font-size: 14px;
    }

    /* Custom notification popup close button */
    .custom-notification .notification-close {
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 18px;
        line-height: 1;
        padding: 0;
        position: absolute;
        right: 10px;
        top: 10px;
    }

    .custom-notification .notification-close:hover {
        color: #fff;
    }

    /* Modal styling */
    .custom--modal .modal-content {
        background-color: #222;
        color: #fff;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .custom--modal .modal-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .custom--modal .modal-footer {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .custom--modal .btn-close {
        color: #fff;
        filter: invert(1) grayscale(100%) brightness(200%);
    }

    .custom--modal .form-control {
        background-color: #333;
        border-color: rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    .custom--modal .form-control:focus {
        background-color: #444;
        border-color: #BE8400;
        box-shadow: 0 0 0 0.25rem rgba(190, 132, 0, 0.25);
    }

    .btn--base {
        background-color: #BE8400;
        border-color: #BE8400;
        color: #fff;
    }

    .btn--base:hover {
        background-color: #9e6e00;
        border-color: #9e6e00;
        color: #fff;
    }

    .input-group-text {
        background-color: #333;
        border-color: rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    .alert-info {
        background-color: rgba(23, 162, 184, 0.1);
        border-color: rgba(23, 162, 184, 0.2);
        color: #17a2b8;
    }

    .alert-info i {
        margin-right: 5px;
    }
</style>
@endpush

@push('script')
<script>
    $(document).ready(function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // Fetch vote counts for all tokens using the batch API
        function fetchVoteCounts() {
            // Collect all tokens data
            var tokens = [];
            $('.vote-count').each(function() {
                var voteCountElement = $(this);
                tokens.push({
                    chain_id: voteCountElement.data('chain-id'),
                    token_address: voteCountElement.data('token-address'),
                    element: voteCountElement[0] // Store the DOM element for later reference
                });
            });

            // If no tokens, exit early
            if (tokens.length === 0) return;

            // Make a single batch request to get vote counts
            $.ajax({
                url: '/api/token/votes/batch',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    tokens: tokens.map(function(t) {
                        return {
                            chain_id: t.chain_id,
                            token_address: t.token_address
                        };
                    })
                },
                success: function(response) {
                    if (response.success && response.results) {
                        // Update vote counts in the UI
                        response.results.forEach(function(result, index) {
                            var element = $(tokens[index].element);
                            element.find('.vote-count-value').text(result.vote_count);
                        });
                    }
                }
            });
        }

        // Call the function when page loads
        fetchVoteCounts();

        // Handle promote icon click
        $('.promote-icon').on('click', function() {
            var tokenAddress = $(this).data('token-address');
            var chainId = $(this).data('chain-id');
            var tokenSymbol = $(this).data('token-symbol');

            @auth
                // Show promote modal if it exists
                if ($('#promoteTokenModal').length) {
                    $('#promoteTokenModal').modal('show');
                    $('#promoteTokenModal').find('input[name="chain_id"]').val(chainId);
                    $('#promoteTokenModal').find('input[name="token_address"]').val(tokenAddress);
                    $('#promoteTokenModal').find('.token-name-display').text(tokenSymbol);
                } else {
                    // Redirect to token details page
                    window.location.href = '/token/' + chainId + '/' + tokenAddress;
                }
            @else
                // Redirect to login page
                window.location.href = '{{ route("user.login") }}';
            @endauth
        });

        // Flag to prevent duplicate vote submissions
        var isVoteSubmitting = false;

        // Pre-check vote status for all tokens
        @auth
        function checkVoteStatus() {
            // Collect all tokens data
            var tokens = [];
            $('.vote-icon').each(function() {
                var icon = $(this);
                tokens.push({
                    chain_id: icon.data('chain-id'),
                    token_address: icon.data('token-address'),
                    element: icon[0] // Store the DOM element for later reference
                });
            });

            // If no tokens, exit early
            if (tokens.length === 0) return;

            // Make a single batch request instead of multiple individual requests
            $.ajax({
                url: '{{ route("token.vote.batch.check") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    tokens: tokens.map(function(t) {
                        return {
                            chain_id: t.chain_id,
                            token_address: t.token_address
                        };
                    })
                },
                success: function(response) {
                    if (response.success && response.results) {
                        // Store the results in a global object for quick lookup when clicking vote icons
                        window.voteStatusCache = window.voteStatusCache || {};

                        // Process each result
                        response.results.forEach(function(result, index) {
                            // Create a key for the cache
                            var cacheKey = result.chain_id + '_' + result.token_address;

                            // Store the result in the cache
                            window.voteStatusCache[cacheKey] = {
                                has_voted: result.has_voted,
                                has_negative_voted: result.has_negative_voted
                            };
                        });
                    }
                }
            });
        }

        // Call the function when page loads
        checkVoteStatus();
        @endauth

        // Handle vote icon click
        $('.vote-icon').on('click', function() {
            // Prevent duplicate submissions
            if (isVoteSubmitting) {
                return false;
            }

            var voteIcon = $(this);
            var tokenAddress = voteIcon.data('token-address');
            var chainId = voteIcon.data('chain-id');
            var tokenSymbol = voteIcon.data('token-symbol');

            @auth
                // Set flag to prevent duplicate submissions
                isVoteSubmitting = true;

                // Add visual feedback
                voteIcon.css('opacity', '0.5');

                // Check if we have cached vote status
                var cacheKey = chainId + '_' + tokenAddress;
                if (window.voteStatusCache && window.voteStatusCache[cacheKey]) {
                    var cachedStatus = window.voteStatusCache[cacheKey];

                    if (cachedStatus.has_voted) {
                        // User has already used their daily vote, show trend vote modal if it exists
                        if ($('#voteTokenModal').length) {
                            $('#voteTokenModal').modal('show');
                            $('#voteTokenModal').find('input[name="chain_id"]').val(chainId);
                            $('#voteTokenModal').find('input[name="token_address"]').val(tokenAddress);
                            $('#voteTokenModal').find('.token-name-display').text(tokenSymbol);
                        } else {
                            // Redirect to token details page
                            window.location.href = '/token/' + chainId + '/' + tokenAddress;
                        }

                        // Reset submission flag and visual feedback
                        isVoteSubmitting = false;
                        voteIcon.css('opacity', '1');
                        return;
                    }

                    // User hasn't voted today, submit regular vote
                    var form = $('<form>', {
                        'method': 'POST',
                        'action': '{{ route("token.vote") }}'
                    }).append($('<input>', {
                        'type': 'hidden',
                        'name': '_token',
                        'value': '{{ csrf_token() }}'
                    })).append($('<input>', {
                        'type': 'hidden',
                        'name': 'chain_id',
                        'value': chainId
                    })).append($('<input>', {
                        'type': 'hidden',
                        'name': 'token_address',
                        'value': tokenAddress
                    }));

                    $('body').append(form);

                    // Use setTimeout to ensure we don't submit the form multiple times
                    setTimeout(function() {
                        form.submit();
                    }, 100);
                    return;
                }

                // If we don't have cached status, fall back to individual check
                $.ajax({
                    url: '{{ route("check.token.vote") }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        chain_id: chainId,
                        token_address: tokenAddress
                    },
                    success: function(response) {
                        if (response.has_voted) {
                            // User has already used their daily vote, show trend vote modal if it exists
                            if ($('#voteTokenModal').length) {
                                $('#voteTokenModal').modal('show');
                                $('#voteTokenModal').find('input[name="chain_id"]').val(chainId);
                                $('#voteTokenModal').find('input[name="token_address"]').val(tokenAddress);
                                $('#voteTokenModal').find('.token-name-display').text(tokenSymbol);
                            } else {
                                // Redirect to token details page
                                window.location.href = '/token/' + chainId + '/' + tokenAddress;
                            }

                            // Reset submission flag and visual feedback
                            isVoteSubmitting = false;
                            voteIcon.css('opacity', '1');
                        } else {
                            // User hasn't voted today, submit regular vote
                            var form = $('<form>', {
                                'method': 'POST',
                                'action': '{{ route("token.vote") }}'
                            }).append($('<input>', {
                                'type': 'hidden',
                                'name': '_token',
                                'value': '{{ csrf_token() }}'
                            })).append($('<input>', {
                                'type': 'hidden',
                                'name': 'chain_id',
                                'value': chainId
                            })).append($('<input>', {
                                'type': 'hidden',
                                'name': 'token_address',
                                'value': tokenAddress
                            }));

                            $('body').append(form);

                            // Use setTimeout to ensure we don't submit the form multiple times
                            setTimeout(function() {
                                form.submit();
                            }, 100);
                        }
                    },
                    error: function() {
                        // Redirect to token details page on error
                        window.location.href = '/token/' + chainId + '/' + tokenAddress;

                        // Reset submission flag and visual feedback
                        isVoteSubmitting = false;
                        voteIcon.css('opacity', '1');
                    }
                });
            @else
                // Redirect to login page
                window.location.href = '{{ route("user.login") }}';
            @endauth
        });

        // Define a variable to track if watchlist notification has been shown
        if (typeof watchlistNotificationShown === 'undefined') {
            var watchlistNotificationShown = false;
        }

        // Check watchlist status for each token and update UI
        @auth
        function checkWatchlistStatus() {
            // Collect all tokens data
            var tokens = [];
            $('.watchlist-icon').each(function() {
                var icon = $(this);
                tokens.push({
                    chain_id: icon.data('chain-id'),
                    token_address: icon.data('token-address'),
                    element: icon[0] // Store the DOM element for later reference
                });
            });

            // If no tokens, exit early
            if (tokens.length === 0) return;

            // Make a single batch request instead of multiple individual requests
            $.ajax({
                url: '{{ route("token.watchlist.batch.check") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    tokens: tokens.map(function(t) {
                        return {
                            chain_id: t.chain_id,
                            token_address: t.token_address
                        };
                    })
                },
                success: function(response) {
                    if (response.success && response.results) {
                        // Process each result
                        response.results.forEach(function(result, index) {
                            if (result.is_watchlisted) {
                                var $icon = $(tokens[index].element);
                                $icon.addClass('active');
                                $icon.attr('title', 'Remove from Watchlist');
                                if ($icon.tooltip) {
                                    $icon.tooltip('dispose');
                                    $icon.tooltip();
                                }
                            }
                        });
                    }
                }
            });
        }

        // Call the function when page loads
        checkWatchlistStatus();
        @endauth

        // Handle watchlist icon click
        $('.watchlist-icon').on('click', function() {
            var $icon = $(this);
            var tokenAddress = $icon.data('token-address');
            var chainId = $icon.data('chain-id');
            var tokenSymbol = $icon.data('token-symbol');

            @auth
                // Toggle watchlist status
                $.ajax({
                    url: '{{ route("token.watchlist.toggle") }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        chain_id: chainId,
                        token_address: tokenAddress
                    },
                    success: function(response) {
                        if (response.success) {
                            if (response.is_watchlisted) {
                                $icon.addClass('active');
                                $icon.attr('title', 'Remove from Watchlist');
                                if ($icon.tooltip) {
                                    $icon.tooltip('dispose');
                                    $icon.tooltip();
                                }

                                // Check if token was already in watchlist
                                if (response.already_exists) {
                                    if (!watchlistNotificationShown) {
                                        notify('info', 'Token already in watchlist');
                                        watchlistNotificationShown = true;
                                        // Reset the flag after 2 seconds to allow future notifications
                                        setTimeout(function() {
                                            watchlistNotificationShown = false;
                                        }, 2000);
                                    }
                                } else {
                                    // Token was just added
                                    if (!watchlistNotificationShown) {
                                        notify('success', 'Token added to watchlist');
                                        watchlistNotificationShown = true;
                                        // Reset the flag after 2 seconds to allow future notifications
                                        setTimeout(function() {
                                            watchlistNotificationShown = false;
                                        }, 2000);
                                    }
                                }
                            } else {
                                $icon.removeClass('active');
                                $icon.attr('title', 'Add to Watchlist');
                                if ($icon.tooltip) {
                                    $icon.tooltip('dispose');
                                    $icon.tooltip();
                                }
                                if (!watchlistNotificationShown) {
                                    notify('success', 'Token removed from watchlist');
                                    watchlistNotificationShown = true;
                                    // Reset the flag after 2 seconds to allow future notifications
                                    setTimeout(function() {
                                        watchlistNotificationShown = false;
                                    }, 2000);
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Watchlist error:', xhr.responseText);
                        // Don't show error notification as the token might still be added
                        // notify('error', 'Something went wrong');
                    }
                });
            @else
                // Redirect to login page
                window.location.href = '{{ route("user.login") }}';
            @endauth
        });

        // Handle search pagination scrolling
        $('.search-pagination-wrapper .page-link').on('click', function(e) {
            // Store the target URL
            var targetUrl = $(this).attr('href');
            
            // Skip if this is a disabled link or current page
            if ($(this).parent().hasClass('disabled') || $(this).parent().hasClass('active')) {
                return;
            }
            
            // Prevent default navigation
            e.preventDefault();
            
            // Show loading indicator
            $('body').append('<div id="pagination-loading" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 8px; z-index: 9999; font-size: 16px;"><i class="fas fa-spinner fa-spin"></i> Loading search results...</div>');
            
            // Navigate to the new page
            window.location.href = targetUrl;
        });

        // Auto-scroll to search results if we're on page 2 or higher
        @if(request()->query('page') && request()->query('page') > 1)
        $(window).on('load', function() {
            // Show loading indicator during scroll
            $('body').append('<div id="scroll-loading" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 15px 25px; border-radius: 8px; z-index: 9999; font-size: 14px;"><i class="fas fa-arrow-down fa-bounce"></i> Scrolling to results...</div>');
            
            setTimeout(function() {
                $('html, body').animate({
                    scrollTop: $('.search-results-container').offset().top - 20
                }, 400, function() {
                    // Remove loading indicator after scroll completes
                    $('#scroll-loading').fadeOut(200, function() {
                        $(this).remove();
                    });
                });
            }, 50);
        });
        @endif
    });
</script>
@endpush
