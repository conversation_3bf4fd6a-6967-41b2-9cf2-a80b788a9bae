<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('general_settings', function (Blueprint $table) {
            $table->integer('dexscreener_tokens_count')->default(20)->after('homepage_trending_articles_count');
            $table->integer('dexscreener_refresh_interval')->default(15)->after('dexscreener_tokens_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('general_settings', function (Blueprint $table) {
            $table->dropColumn('dexscreener_tokens_count');
            $table->dropColumn('dexscreener_refresh_interval');
        });
    }
}; 