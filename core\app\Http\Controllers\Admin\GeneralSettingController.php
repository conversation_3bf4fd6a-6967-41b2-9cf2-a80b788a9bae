<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\Frontend;
use App\Rules\FileTypeValidate;
use Illuminate\Http\Request;

class GeneralSettingController extends Controller
{
    public function systemSetting(){
        $general = gs();
        $pageTitle = 'System Configuration';

        // Create comprehensive settings data structure for the view
        $settings = [
            'general' => (object)[
                'icon' => 'las la-cog',
                'title' => 'General Setting',
                'route_name' => 'admin.setting.general',
                'params' => null,
                'subtitle' => 'Basic settings for your system',
                'keyword' => ['general', 'site name', 'timezone', 'currency']
            ],
            'configuration' => (object)[
                'icon' => 'las la-wrench',
                'title' => 'System Configuration',
                'route_name' => 'admin.setting.system.configuration',
                'params' => null,
                'subtitle' => 'Configure core system elements',
                'keyword' => ['configuration', 'maintenance', 'registration', 'ssl']
            ],
            'logo_favicon' => (object)[
                'icon' => 'las la-image',
                'title' => 'Logo & Favicon',
                'route_name' => 'admin.setting.logo.icon',
                'params' => null,
                'subtitle' => 'Manage your site logo and favicon',
                'keyword' => ['logo', 'favicon', 'image']
            ],
            'trending_articles' => (object)[
                'icon' => 'las la-fire',
                'title' => 'Trending Articles',
                'route_name' => 'admin.setting.trending-articles',
                'params' => null,
                'subtitle' => 'Configure trending articles display',
                'keyword' => ['article', 'blog', 'trending']
            ],
            'maintenance' => (object)[
                'icon' => 'las la-tools',
                'title' => 'Maintenance Mode',
                'route_name' => 'admin.maintenance.mode',
                'params' => null,
                'subtitle' => 'Set your site to maintenance mode',
                'keyword' => ['maintenance', 'offline', 'downtime']
            ],
            'notifications' => (object)[
                'icon' => 'las la-bell',
                'title' => 'Notification Settings',
                'route_name' => 'admin.setting.notification.templates',
                'params' => null,
                'subtitle' => 'Configure notification templates and settings',
                'keyword' => ['notification', 'email', 'sms', 'templates']
            ],
            'email_setting' => (object)[
                'icon' => 'las la-envelope',
                'title' => 'Email Settings',
                'route_name' => 'admin.setting.notification.email',
                'params' => null,
                'subtitle' => 'Configure email sending settings',
                'keyword' => ['email', 'smtp', 'mail']
            ],
            'sms_setting' => (object)[
                'icon' => 'las la-sms',
                'title' => 'SMS Settings',
                'route_name' => 'admin.setting.notification.sms',
                'params' => null,
                'subtitle' => 'Configure SMS gateway settings',
                'keyword' => ['sms', 'text', 'message']
            ],
            'push_notification' => (object)[
                'icon' => 'lab la-chrome',
                'title' => 'Push Notification',
                'route_name' => 'admin.setting.notification.push',
                'params' => null,
                'subtitle' => 'Configure push notification settings',
                'keyword' => ['push', 'notification', 'alert']
            ],
            'payment_gateways' => (object)[
                'icon' => 'las la-credit-card',
                'title' => 'Payment Gateways',
                'route_name' => 'admin.gateway.automatic.index',
                'params' => null,
                'subtitle' => 'Configure automatic or manual payment gateways to accept payment from users.',
                'keyword' => ['payment', 'gateway', 'transaction', 'stripe', 'paypal']
            ],
            'extensions' => (object)[
                'icon' => 'las la-puzzle-piece',
                'title' => 'Extensions',
                'route_name' => 'admin.extensions.index',
                'params' => null,
                'subtitle' => 'Manage system extensions and plugins',
                'keyword' => ['extensions', 'plugins', 'addons']
            ],
            'cookie' => (object)[
                'icon' => 'las la-cookie-bite',
                'title' => 'GDPR Cookie',
                'route_name' => 'admin.setting.cookie',
                'params' => null,
                'subtitle' => 'Configure GDPR cookie consent',
                'keyword' => ['cookie', 'gdpr', 'consent', 'privacy']
            ],
            'social_login' => (object)[
                'icon' => 'las la-share-alt',
                'title' => 'Social Login',
                'route_name' => 'admin.setting.socialite.credentials',
                'params' => null,
                'subtitle' => 'Configure social login credentials',
                'keyword' => ['social', 'login', 'oauth', 'facebook', 'google']
            ],
            'custom_css' => (object)[
                'icon' => 'lab la-css3',
                'title' => 'Custom CSS',
                'route_name' => 'admin.setting.custom.css',
                'params' => null,
                'subtitle' => 'Add custom CSS to your site',
                'keyword' => ['css', 'style', 'custom']
            ],
            'system_info' => (object)[
                'icon' => 'las la-server',
                'title' => 'System Information',
                'route_name' => 'admin.system.info',
                'params' => null,
                'subtitle' => 'View system information and status',
                'keyword' => ['system', 'info', 'status', 'version']
            ],
            'seo' => (object)[
                'icon' => 'las la-globe',
                'title' => 'SEO Manager',
                'route_name' => 'admin.seo',
                'params' => null,
                'subtitle' => 'Manage SEO settings',
                'keyword' => ['seo', 'meta', 'search']
            ],
            'kyc' => (object)[
                'icon' => 'las la-user-check',
                'title' => 'KYC Setting',
                'route_name' => 'admin.kyc.setting',
                'params' => null,
                'subtitle' => 'Configure KYC verification requirements',
                'keyword' => ['kyc', 'verification', 'identity']
            ],
            'crypto' => (object)[
                'icon' => 'lab la-bitcoin',
                'title' => 'Crypto Settings',
                'route_name' => 'admin.crypto.index',
                'params' => null,
                'subtitle' => 'Configure cryptocurrency settings',
                'keyword' => ['crypto', 'bitcoin', 'currency', 'digital']
            ],
            'sitemap' => (object)[
                'icon' => 'las la-sitemap',
                'title' => 'Sitemap',
                'route_name' => 'admin.setting.sitemap',
                'params' => null,
                'subtitle' => 'Configure site sitemap',
                'keyword' => ['sitemap', 'xml', 'seo']
            ],
            'robots' => (object)[
                'icon' => 'las la-robot',
                'title' => 'Robots.txt',
                'route_name' => 'admin.setting.robot',
                'params' => null,
                'subtitle' => 'Configure robots.txt file',
                'keyword' => ['robots', 'crawl', 'seo']
            ],
            'frontend_sections' => (object)[
                'icon' => 'las la-palette',
                'title' => 'Frontend Sections',
                'route_name' => 'admin.frontend.sections',
                'params' => null,
                'subtitle' => 'Manage frontend sections and content',
                'keyword' => ['frontend', 'sections', 'content', 'homepage']
            ],
            'pages' => (object)[
                'icon' => 'las la-file-alt',
                'title' => 'Manage Pages',
                'route_name' => 'admin.frontend.manage.pages',
                'params' => null,
                'subtitle' => 'Manage custom pages',
                'keyword' => ['pages', 'custom', 'content']
            ],
            'referral' => (object)[
                'icon' => 'las la-user-friends',
                'title' => 'Referral System',
                'route_name' => 'admin.setting.referral',
                'params' => null,
                'subtitle' => 'Configure referral system settings',
                'keyword' => ['referral', 'affiliate', 'commission']
            ],
            // 'cron' => (object)[
            //     'icon' => 'las la-clock',
            //     'title' => 'Cron Jobs',
            //     'route_name' => 'admin.cron.index',
            //     'params' => null,
            //     'subtitle' => 'Configure automated system tasks',
            //     'keyword' => ['cron', 'schedule', 'automated', 'jobs']
            // ]
        ];

        return view('admin.setting.system', compact('pageTitle', 'general', 'settings'));
    }

    public function general()
    {
        $pageTitle = 'General Setting';
        $timezones = timezone_identifiers_list();
        $currentTimezone = array_search(config('app.timezone'),$timezones);
        return view('admin.setting.general', compact('pageTitle','timezones','currentTimezone'));
    }

    public function generalUpdate(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:40',
            'cur_text' => 'required|string|max:40',
            'cur_sym' => 'required|string|max:40',
            'preloader_title' => 'nullable|string|max:10',
            'base_color' => 'nullable|regex:/^[a-f0-9]{6}$/i',
            'secondary_color' => 'nullable|regex:/^[a-f0-9]{6}$/i',
            'timezone' => 'required|integer',
            'currency_format'=>'required|in:1,2,3',
            'paginate_number'=>'required|integer'
        ]);

        $timezones = timezone_identifiers_list();
        $timezone = @$timezones[$request->timezone] ?? 'UTC';

        $general = gs();
        $general->site_name = $request->site_name;
        $general->cur_text = $request->cur_text;
        $general->cur_sym = $request->cur_sym;
        $general->paginate_number = $request->paginate_number;
        $general->base_color = str_replace('#','',$request->base_color);
        $general->secondary_color = str_replace('#','',$request->secondary_color);
        $general->preloader_title = $request->preloader_title;
        $general->currency_format = $request->currency_format;
        $general->save();

        $timezoneFile = config_path('timezone.php');
        $content = '<?php $timezone = "'.$timezone.'" ?>';
        file_put_contents($timezoneFile, $content);
        $notify[] = ['success', 'General setting updated successfully'];
        return back()->withNotify($notify);
    }

    public function systemConfiguration(){
        $pageTitle = 'System Configuration';
        return view('admin.setting.configuration', compact('pageTitle'));
    }


    public function systemConfigurationSubmit(Request $request)
    {
        $general = gs();
        $general->kv = $request->kv ? Status::ENABLE : Status::DISABLE;
        $general->ev = $request->ev ? Status::ENABLE : Status::DISABLE;
        $general->en = $request->en ? Status::ENABLE : Status::DISABLE;
        $general->sv = $request->sv ? Status::ENABLE : Status::DISABLE;
        $general->sn = $request->sn ? Status::ENABLE : Status::DISABLE;
        $general->pn = $request->pn ? Status::ENABLE : Status::DISABLE;
        $general->force_ssl = $request->force_ssl ? Status::ENABLE : Status::DISABLE;
        $general->secure_password = $request->secure_password ? Status::ENABLE : Status::DISABLE;
        $general->registration = $request->registration ? Status::ENABLE : Status::DISABLE;
        $general->agree = $request->agree ? Status::ENABLE : Status::DISABLE;
        $general->multi_language = $request->multi_language ? Status::ENABLE : Status::DISABLE;
        $general->referral_system = $request->referral_system ? Status::ENABLE : Status::DISABLE;
        $general->save();

        // Log referral system status
        \Illuminate\Support\Facades\Log::info('Referral system status updated', [
            'status' => $general->referral_system ? 'enabled' : 'disabled'
        ]);

        $notify[] = ['success', 'System configuration updated successfully'];
        return back()->withNotify($notify);
    }


    public function logoIcon()
    {
        $pageTitle = 'Logo & Favicon';
        return view('admin.setting.logo_icon', compact('pageTitle'));
    }

    public function logoIconUpdate(Request $request)
    {
        $request->validate([
            'logo' => ['image',new FileTypeValidate(['jpg','jpeg','png'])],
            'favicon' => ['image',new FileTypeValidate(['png'])],
        ]);
        $path = getFilePath('logo_icon');
        if ($request->hasFile('logo')) {
            try {
                fileUploader($request->logo,$path,filename:'logo.png');
            } catch (\Exception $exp) {
                $notify[] = ['error', 'Couldn\'t upload the logo'];
                return back()->withNotify($notify);
            }
        }

        if ($request->hasFile('favicon')) {
            try {
                fileUploader($request->favicon,$path,filename:'favicon.png');
            } catch (\Exception $exp) {
                $notify[] = ['error', 'Couldn\'t upload the favicon'];
                return back()->withNotify($notify);
            }
        }
        $notify[] = ['success', 'Logo & favicon updated successfully'];
        return back()->withNotify($notify);
    }

    public function customCss(){
        $pageTitle = 'Custom CSS';
        $file = activeTemplate(true).'css/custom.css';
        $fileContent = @file_get_contents($file);
        return view('admin.setting.custom_css',compact('pageTitle','fileContent'));
    }

    public function sitemap(){
        $pageTitle = 'Sitemap XML';
        $file = 'sitemap.xml';
        $fileContent = @file_get_contents($file);
        return view('admin.setting.sitemap',compact('pageTitle','fileContent'));
    }

    public function sitemapSubmit(Request $request){
        $file = 'sitemap.xml';
        if (!file_exists($file)) {
            fopen($file, "w");
        }
        file_put_contents($file,$request->sitemap);
        $notify[] = ['success','Sitemap updated successfully'];
        return back()->withNotify($notify);
    }



    public function robot(){
        $pageTitle = 'Robots TXT';
        $file = 'robots.xml';
        $fileContent = @file_get_contents($file);
        return view('admin.setting.robots',compact('pageTitle','fileContent'));
    }

    public function robotSubmit(Request $request){
        $file = 'robots.xml';
        if (!file_exists($file)) {
            fopen($file, "w");
        }
        file_put_contents($file,$request->robots);
        $notify[] = ['success','Robots txt updated successfully'];
        return back()->withNotify($notify);
    }


    public function customCssSubmit(Request $request){
        $file = activeTemplate(true).'css/custom.css';
        if (!file_exists($file)) {
            fopen($file, "w");
        }
        file_put_contents($file,$request->css);
        $notify[] = ['success','CSS updated successfully'];
        return back()->withNotify($notify);
    }

    public function maintenanceMode()
    {
        $pageTitle = 'Maintenance Mode';
        $maintenance = Frontend::where('data_keys','maintenance.data')->firstOrFail();
        return view('admin.setting.maintenance',compact('pageTitle','maintenance'));
    }

    public function maintenanceModeSubmit(Request $request)
    {
        $request->validate([
            'description'=>'required',
            'image'=>['nullable',new FileTypeValidate(['jpg','jpeg','png'])]
        ]);
        $general = gs();
        $general->maintenance_mode = $request->status ? Status::ENABLE : Status::DISABLE;
        $general->save();

        $maintenance = Frontend::where('data_keys','maintenance.data')->firstOrFail();
        $image = @$maintenance->data_values->image;
        if ($request->hasFile('image')) {
            try {
                $old = $image;
                $image = fileUploader($request->image, getFilePath('maintenance'), getFileSize('maintenance'), $old);
            } catch (\Exception $exp) {
                $notify[] = ['error', 'Couldn\'t upload your image'];
                return back()->withNotify($notify);
            }
        }

        $maintenance->data_values = [
            'description' => $request->description,
            'image'=>$image
        ];
        $maintenance->save();

        $notify[] = ['success','Maintenance mode updated successfully'];
        return back()->withNotify($notify);
    }

    public function cookie(){
        $pageTitle = 'GDPR Cookie';
        $cookie = Frontend::where('data_keys','cookie.data')->firstOrFail();
        return view('admin.setting.cookie',compact('pageTitle','cookie'));
    }

    public function cookieSubmit(Request $request){
        $request->validate([
            'short_desc'=>'required|string|max:255',
            'description'=>'required',
        ]);
        $cookie = Frontend::where('data_keys','cookie.data')->firstOrFail();
        $cookie->data_values = [
            'short_desc' => $request->short_desc,
            'description' => $request->description,
            'status' => $request->status ? Status::ENABLE : Status::DISABLE,
        ];
        $cookie->save();
        $notify[] = ['success','Cookie policy updated successfully'];
        return back()->withNotify($notify);
    }


    public function socialiteCredentials()
    {
        $pageTitle = 'Social Login Credentials';
        return view('admin.setting.social_credential', compact('pageTitle'));
    }

    public function updateSocialiteCredentialStatus($key)
    {
        $general = gs();
        $credentials = $general->socialite_credentials;
        try {
            $credentials->$key->status = $credentials->$key->status == Status::ENABLE ? Status::DISABLE : Status::ENABLE;
        } catch (\Throwable $th) {
            abort(404);
        }

        $general->socialite_credentials = $credentials;
        $general->save();

        $notify[] = ['success', 'Status changed successfully'];
        return back()->withNotify($notify);
    }

    public function updateSocialiteCredential(Request $request, $key)
    {
        $general = gs();
        $credentials = $general->socialite_credentials;
        try {
            @$credentials->$key->client_id = $request->client_id;
            @$credentials->$key->client_secret = $request->client_secret;
        } catch (\Throwable $th) {
            abort(404);
        }
        $general->socialite_credentials = $credentials;
        $general->save();

        $notify[] = ['success', ucfirst($key) . ' credential updated successfully'];
        return back()->withNotify($notify);
    }

    public function trendingArticles(Request $request){
        $general = gs();
        $pageTitle = 'Trending Articles Setting';
        $search = $request->search;

        // Get all articles
        $allArticles = \App\Models\Frontend::activeTemplate()
                      ->where('data_keys', 'blog.element')
                      ->get();

        // Compute votes for each article
        foreach ($allArticles as $article) {
            // Get regular user votes
            $article->regular_votes = \App\Models\ArticleVote::getVoteCount($article->id);

            // Get admin trend votes
            $article->admin_trend_votes = 0;
            if (isset($article->data_values->trend_votes) && is_numeric($article->data_values->trend_votes)) {
                $article->admin_trend_votes = (int)$article->data_values->trend_votes;
            }

            // Calculate total votes
            $article->vote_count = $article->regular_votes + $article->admin_trend_votes;
        }

        // Apply search filter if provided
        if ($search) {
            $allArticles = $allArticles->filter(function($article) use ($search) {
                // Search in title and content
                $title = $article->data_values->title ?? '';
                $description = $article->data_values->description ?? '';

                return stripos($title, $search) !== false ||
                       stripos($description, $search) !== false;
            });
        }

        // Sort by total votes (highest first)
        $sortedArticles = $allArticles->sortByDesc('vote_count');

        // Add global rank to each article based on vote count
        $rank = 1;
        $prevVotes = null;
        $prevRank = 1;

        foreach ($sortedArticles as $article) {
            if ($prevVotes !== null && $article->vote_count < $prevVotes) {
                // Only increment rank when vote count decreases
                $prevRank = $rank;
            } else if ($prevVotes !== null && $article->vote_count == $prevVotes) {
                // Keep same rank for equal vote counts
                $rank = $prevRank;
            }

            $article->global_rank = $rank;
            $prevVotes = $article->vote_count;
            $rank++;
        }

        // Paginate the collection manually
        $page = request()->input('page', 1);
        $perPage = 10;
        $offset = ($page - 1) * $perPage;

        // Get the paginated items
        $items = $sortedArticles->slice($offset, $perPage)->all();

        // Create a new paginator instance
        $articles = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $sortedArticles->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return view('admin.setting.trending_articles',compact('pageTitle','general','articles','search'));
    }

    public function trendingArticlesUpdate(Request $request){
        // If it's a general settings update
        if ($request->has('homepage_trending_articles_count')) {
            $request->validate([
                'homepage_trending_articles_count' => 'required|integer|min:1|max:500',
            ]);

            $general = gs();
            $general->homepage_trending_articles_count = $request->homepage_trending_articles_count;
            $general->save();

            $notify[] = ['success','Trending articles setting has been updated'];
            return back()->withNotify($notify);
        }

        // If it's a specific article vote update
        if ($request->has('article_id')) {
            $article = \App\Models\Frontend::where('id', $request->article_id)
                       ->where('data_keys', 'blog.element')
                       ->first();

            if (!$article) {
                $notify[] = ['error', 'Article not found'];
                return back()->withNotify($notify);
            }

            // Update only the trend votes, no trending flag
            $dataValues = $article->data_values;

            // Update trend votes if provided
            if ($request->has('trend_votes')) {
                $trendVotes = max(0, intval($request->trend_votes));
                $dataValues->trend_votes = $trendVotes;
            }

            // Ensure trend_votes is set properly if not provided
            if (!isset($dataValues->trend_votes) && !$request->has('trend_votes')) {
                $dataValues->trend_votes = 0;
            }

            $article->data_values = $dataValues;
            $article->save();

            $notify[] = ['success', 'Article votes updated successfully to ' . ($dataValues->trend_votes ?? 0) . ' votes'];
            return back()->withNotify($notify);
        }

        $notify[] = ['error', 'Invalid request'];
        return back()->withNotify($notify);
    }


}
