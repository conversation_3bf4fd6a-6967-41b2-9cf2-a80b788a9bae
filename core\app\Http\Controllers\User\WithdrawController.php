<?php

namespace App\Http\Controllers\User;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\AdminNotification;
use App\Models\Transaction;
use App\Models\Withdrawal;
use App\Models\ReferralLog;
use Illuminate\Http\Request;

class WithdrawController extends Controller
{
    public function withdrawMoney()
    {
        $pageTitle = 'Withdraw Funds';
        $user = auth()->user();
        $general = gs();

        // Calculate total referral earnings (including any previous withdrawals)
        $totalReferralEarnings = ReferralLog::where('referee_id', $user->id)->sum('amount');

        // Check if user meets minimum withdrawal requirement
        $canWithdraw = $totalReferralEarnings >= $general->referral_min_withdrawal;

        return view('Template::user.withdraw.methods', compact('pageTitle', 'totalReferralEarnings', 'canWithdraw', 'general'));
    }

    public function withdrawStore(Request $request)
    {
        $user = auth()->user();
        $general = gs();

        // Get the minimum withdrawal amount set by admin
        $minWithdrawal = $general->referral_min_withdrawal;

        $request->validate([
            'amount' => 'required|numeric|gt:0|min:' . $minWithdrawal,
            'wallet_address' => 'required|string',
        ]);

        $amount = $request->amount;

        // Calculate total referral earnings (including any previous withdrawals)
        $totalReferralEarnings = ReferralLog::where('referee_id', $user->id)->sum('amount');

        // Check if user meets minimum withdrawal requirement
        if ($totalReferralEarnings < $general->referral_min_withdrawal) {
            $notify[] = ['error', 'You need at least ' . showAmount($general->referral_min_withdrawal) . ' ' . $general->cur_text . ' to withdraw referral earnings'];
            return back()->withNotify($notify);
        }

        // Check if amount is less than minimum withdrawal amount
        if ($amount < $minWithdrawal) {
            $notify[] = ['error', 'Minimum withdrawal amount is ' . showAmount($minWithdrawal) . ' ' . $general->cur_text];
            return back()->withNotify($notify);
        }

        // Check if requested amount is available
        if ($amount > $totalReferralEarnings) {
            $notify[] = ['error', 'You cannot withdraw more than your referral earnings'];
            return back()->withNotify($notify);
        }

        $trx = getTrx();

        // Create withdrawal request
        $withdraw = new Withdrawal();
        $withdraw->user_id = $user->id;
        $withdraw->amount = $amount;
        $withdraw->currency = $general->cur_text;
        $withdraw->trx = $trx;
        $withdraw->final_amount = $amount;
        $withdraw->after_charge = $amount;
        $withdraw->status = Status::PAYMENT_PENDING;
        $withdraw->withdraw_information = json_encode([
            'wallet_address' => $request->wallet_address,
            'withdrawal_type' => 'referral_earnings'
        ]);
        $withdraw->save();

        // Create admin notification
        $adminNotification = new AdminNotification();
        $adminNotification->user_id = $user->id;
        $adminNotification->title = 'New referral earnings withdrawal request from ' . $user->username;
        $adminNotification->click_url = urlPath('admin.withdraw.data.details', $withdraw->id);
        $adminNotification->save();

        // Create transaction record
        $transaction = new Transaction();
        $transaction->user_id = $user->id;
        $transaction->amount = $amount;
        $transaction->post_balance = $user->balance;
        $transaction->charge = 0;
        $transaction->trx_type = '-';
        $transaction->details = 'Withdrawal of referral earnings';
        $transaction->trx = $trx;
        $transaction->currency = $general->cur_text;
        $transaction->remark = 'withdraw';
        $transaction->save();

        // Send notification to user
        notify($user, 'WITHDRAW_REQUEST', [
            'wallet' => $request->wallet_address,
            'post_balance' => showAmount($user->balance),
            'amount' => showAmount($amount),
            'coin_code' => $general->cur_text,
            'trx' => $trx
        ]);

        $notify[] = ['success', 'Withdrawal request submitted successfully'];
        return redirect()->route('user.withdraw.preview', $withdraw->id)->withNotify($notify);
    }

    public function withdrawPreview($id)
    {
        $withdraw = Withdrawal::where('id', $id)
            ->where('user_id', auth()->id())
            ->where('status', '!=', Status::PAYMENT_INITIATE)
            ->with('user')
            ->firstOrFail();

        $pageTitle = 'Withdrawal Preview';
        $withdrawInfo = json_decode($withdraw->withdraw_information);

        return view('Template::user.withdraw.preview', compact('pageTitle', 'withdraw', 'withdrawInfo'));
    }

    public function withdrawLog()
    {
        $pageTitle = "My Withdrawals";
        $withdraws = Withdrawal::where('user_id', auth()->id())->orderBy('id', 'desc')->paginate(5);
        return view('Template::user.withdraw.log', compact('pageTitle', 'withdraws'));
    }
}
