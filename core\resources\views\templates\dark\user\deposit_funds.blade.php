@extends($activeTemplate . 'layouts.master')
@section('content')
    <div class="card custom--card">
        <div class="card-header">
            <h5 class="card-title">@lang('Deposit Funds')</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('user.deposit.insert') }}" method="post" class="deposit-form">
                @csrf
                <input type="hidden" name="currency">
                <input type="hidden" name="gateway">
                <div class="gateway-card">
                    <div class="row justify-content-center gy-sm-4 gy-3 deposit-row">
                        <div class="col-lg-6 deposit-left-col">
                            <div class="deposit-info mb-4">
                                <div class="deposit-info__title">
                                    <p class="text mb-0">@lang('Enter Amount')<span class="text-danger">*</span></p>
                                </div>
                                <div class="deposit-info__input">
                                    <div class="input-group amount-input-group">
                                        <input type="number" step="any" name="amount" class="form-control form--control amount" required>
                                        <span class="input-group-text">{{ gs('cur_text') }}</span>
                                    </div>
                                    <div class="form-text amount-helper-text">@lang('Enter deposit amount')<span class="text-danger">*</span></div>
                                </div>
                            </div>

                            <div class="payment-system-list gateway-option-list">
                                @foreach ($gatewayCurrency as $data)
                                    <label class="payment-item gateway-item" data-id="{{ $data->id }}">
                                        <div class="payment-item__info">
                                            <span class="payment-item__check"></span>
                                            <span class="payment-item__name">{{ __($data->name) }}</span>
                                        </div>
                                        <div class="payment-item__thumb">
                                            <img class="payment-item__thumb-img" src="{{ getImage(getFilePath('gateway') . '/' . $data->method->image) }}" alt="{{ $data->name }}">
                                        </div>
                                        <input type="radio" name="gateway_select" class="gateway-radio d-none"
                                            data-gateway='@json($data)'
                                            data-id="{{ $data->id }}"
                                            data-name="{{ $data->name }}"
                                            data-currency="{{ $data->currency }}"
                                            data-method_code="{{ $data->method_code }}"
                                            data-min_amount="{{ showAmount($data->min_amount) }}"
                                            data-max_amount="{{ showAmount($data->max_amount) }}"
                                            data-base_symbol="{{ gs('cur_sym') }}"
                                            data-fix_charge="{{ showAmount($data->fixed_charge) }}"
                                            data-percent_charge="{{ showAmount($data->percent_charge) }}"
                                            data-rate="{{ showAmount($data->rate) }}">
                                    </label>
                                @endforeach
                            </div>
                        </div>
                        <div class="col-lg-6 deposit-right-col">
                            <div class="payment-system-list p-3">
                                <div class="deposit-info">
                                    <div class="deposit-info__title">
                                        <p class="text has-icon"> @lang('Limit')
                                            <span></span>
                                        </p>
                                    </div>
                                    <div class="deposit-info__input">
                                        <p class="text"><span class="gateway-limit">@lang('0.00')</span>
                                        </p>
                                    </div>
                                </div>
                                <div class="deposit-info" style="border-bottom: none;">
                                    <div class="deposit-info__title">
                                        <p class="text has-icon">@lang('Processing Charge')
                                            <span data-bs-toggle="tooltip" title="@lang('Processing charge for payment gateways')" class="proccessing-fee-info"><i class="las la-info-circle"></i> </span>
                                        </p>
                                    </div>
                                    <div class="deposit-info__input">
                                        <p class="text"><span class="processing-fee">@lang('0.00')</span> {{ __(gs('cur_text')) }}</p>
                                    </div>
                                </div>

                                <div class="deposit-info total-amount pt-3">
                                    <div class="deposit-info__title">
                                        <p class="text">@lang('Total')</p>
                                    </div>
                                    <div class="deposit-info__input">
                                        <p class="text"><span class="final-amount">@lang('0.00')</span>
                                            {{ __(gs('cur_text')) }}</p>
                                    </div>
                                </div>

                                <div class="deposit-info gateway-conversion d-none total-amount pt-2">
                                    <div class="deposit-info__title">
                                        <p class="text">@lang('Conversion')
                                        </p>
                                    </div>
                                    <div class="deposit-info__input">
                                        <p class="text"></p>
                                    </div>
                                </div>
                                <div class="deposit-info conversion-currency d-none total-amount pt-2">
                                    <div class="deposit-info__title">
                                        <p class="text">
                                            @lang('In') <span class="gateway-currency"></span>
                                        </p>
                                    </div>
                                    <div class="deposit-info__input">
                                        <p class="text">
                                            <span class="in-currency"></span>
                                        </p>
                                    </div>
                                </div>
                                <div class="d-none crypto-message mb-3">
                                    @lang('Conversion with') <span class="gateway-currency"></span> @lang('and final value will shown on the next step.')
                                </div>
                                <button type="submit" class="btn btn--base w-100" disabled>
                                    @lang('Proceed to Payment')
                                </button>
                                <div class="gateway-images-card mb-3">
                                    <div class="gateway-images-container">
                                        <img src="{{ asset('assets/images/gateway/1-BTC.png') }}" alt="BTC" class="gateway-img">
                                        <img src="{{ asset('assets/images/gateway/2-BNB.png') }}" alt="BNB" class="gateway-img">
                                        <img src="{{ asset('assets/images/gateway/3-ETH.png') }}" alt="ETH" class="gateway-img">
                                        <img src="{{ asset('assets/images/gateway/4-SOL.png') }}" alt="SOL" class="gateway-img">
                                        <img src="{{ asset('assets/images/gateway/5-USDT.png') }}" alt="USDT" class="gateway-img">
                                        <img src="{{ asset('assets/images/gateway/6-TRX.png') }}" alt="TRX" class="gateway-img">
                                    </div>
                                    <div class="info-text">
                                        <p class="text">@lang('Secure payments with world-class payment options.')</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('script')
<script>
    (function ($) {
        "use strict";

        let gateway = null;
        let amount = 0;

        // Ensure button is disabled on page load
        $('.deposit-form button[type=submit]').attr('disabled', true);

        // Handle gateway selection
        $('.gateway-item').on('click', function() {
            let item = $(this);
            let radio = item.find('.gateway-radio');

            // Select the radio button
            radio.prop('checked', true);

            // Get gateway data
            gateway = radio.data('gateway');

            // Highlight the selected gateway
            $('.payment-item').removeClass('active');
            item.addClass('active');

            // Set the gateway input values
            $('input[name=gateway]').val(gateway.method_code);
            $('input[name=currency]').val(gateway.currency);

            // Update gateway details
            // Format min and max amount with 2 decimal places and thousands separator
            const formattedMinAmount = parseFloat(gateway.min_amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            const formattedMaxAmount = parseFloat(gateway.max_amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            $('.gateway-limit').text(`$${formattedMinAmount} - $${formattedMaxAmount} {{ gs('cur_text') }}`);

            let processingFeeInfo = `${gateway.percent_charge}% with ${gateway.fixed_charge} {{ __(gs('cur_text')) }} charge for payment gateway processing fees`;
            $(".proccessing-fee-info").attr("data-bs-original-title", processingFeeInfo);

            // Calculate and update display
            calculateGatewayCharge();
        });

        $('.amount').on('input', function() {
            amount = parseFloat($(this).val());
            if (isNaN(amount) || amount <= 0) {
                $('.deposit-form button[type=submit]').attr('disabled', true);
            } else if (gateway) {
                calculateGatewayCharge();
            }
        });

        function calculateGatewayCharge() {
            amount = parseFloat($('.amount').val());
            if (isNaN(amount) || amount <= 0) {
                amount = 0;
            }

            // Check if amount is valid
            if (amount <= 0) {
                $('.deposit-form button[type=submit]').attr('disabled', true);
                return;
            }

            let percentCharge = 0;
            let fixedCharge = 0;
            let totalPercentCharge = 0;

            if (amount) {
                percentCharge = parseFloat(gateway.percent_charge);
                fixedCharge = parseFloat(gateway.fixed_charge);
                totalPercentCharge = parseFloat(amount / 100 * percentCharge);
            }

            let totalCharge = parseFloat(totalPercentCharge + fixedCharge);
            let totalAmount = parseFloat((amount || 0) + totalPercentCharge + fixedCharge);

            // Update the display
            $('.final-amount').text('$' + totalAmount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ","));
            $('.processing-fee').text('$' + totalCharge.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ","));
            $('.gateway-currency').text(gateway.currency);

            // Enable/disable submit button based on amount limits
            if (amount <= 0 || amount < gateway.min_amount || amount > gateway.max_amount) {
                $('.deposit-form button[type=submit]').attr('disabled', true);
            } else {
                $('.deposit-form button[type=submit]').removeAttr('disabled');
            }

            // Handle currency conversion display
            if (gateway.currency != "{{ gs('cur_text') }}" && gateway.method.crypto != 1) {
                $('.deposit-form').addClass('adjust-height');
                $('.gateway-conversion, .conversion-currency').removeClass('d-none');
                $('.gateway-conversion').find('.deposit-info__input .text').html(
                    `$1 {{ __(gs('cur_text')) }} = <span class="rate">${parseFloat(gateway.rate).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}</span> <span class="method_currency">${gateway.currency}</span>`
                );
                const convertedAmount = parseFloat(totalAmount * gateway.rate);
                const formattedConvertedAmount = convertedAmount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                $('.in-currency').text('$' + formattedConvertedAmount);
            } else {
                $('.gateway-conversion, .conversion-currency').addClass('d-none');
                $('.deposit-form').removeClass('adjust-height');
            }

            if (gateway.method.crypto == 1) {
                $('.crypto-message').removeClass('d-none');
            } else {
                $('.crypto-message').addClass('d-none');
            }
        }

        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    })(jQuery);
</script>
@endpush

@push('style')
<style>
    /* Gateway images styles */
    .gateway-images-card {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 10px;
        margin-top: 15px;
    }
    .gateway-images-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }
    .gateway-img {
        max-width: 40px;
        height: auto;
        margin: 0 5px;
        object-fit: contain;
    }
    .gateway-images-card .info-text {
        padding-top: 8px !important;
        margin-bottom: 0;
        text-align: center;
    }
    .gateway-images-card .info-text p {
        margin-bottom: 0;
        font-size: 12px;
    }

    /* Payment system list styles */
    .payment-system-list {
        --thumb-width: 130px;
        --thumb-height: 60px;
        --radio-size: 12px;
        --border-color: rgba(255, 255, 255, 0.1);
        --hover-border-color: #BE8400;
        background-color: transparent;
        border-radius: 5px;
        height: 100%;
    }

    /* Payment system list with no scrollbar */
    .payment-system-list {
        padding-block: 4px;
        overflow: visible !important;
        max-height: none !important;
        height: auto !important;
    }

    /* Ensure no scrollbar appears */
    .payment-system-list::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
    }

    .payment-system-list {
        -ms-overflow-style: none !important;  /* IE and Edge */
        scrollbar-width: none !important;  /* Firefox */
    }

    /* Payment item styles */
    .payment-item {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        padding: 10px 18px;
        transition: all 0.3s;
        border-left: 3px solid transparent;
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
    }

    .payment-item:first-child {
        border-radius: 5px 5px 0 0;
    }

    .payment-item.active {
        border-left: 3px solid var(--hover-border-color);
    }

    .payment-item__info {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        max-width: calc(100% - var(--thumb-width));
    }

    .payment-item__check {
        width: var(--radio-size);
        height: var(--radio-size);
        border-radius: 50%;
        border: 3px solid transparent;
        display: inline-block;
    }

    .payment-item:has(.gateway-radio:checked) .payment-item__check {
        border: 3px solid #BE8400;
    }

    .payment-item.active .payment-item__check {
        border: 3px solid #BE8400;
        background-color: #BE8400;
    }

    .payment-item__name {
        padding-left: 10px;
        width: calc(100% - var(--radio-size));
        transition: all 0.3s;
        font-weight: 500;
    }

    .payment-item__thumb {
        width: var(--thumb-width);
        height: var(--thumb-height);
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .payment-item__thumb-img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    /* Deposit info styles */
    .deposit-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid var(--border-color);
    }

    .deposit-info__title {
        font-weight: 500;
    }

    .deposit-info__title .text {
        margin-bottom: 0;
    }

    .deposit-info__title .text.has-icon {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .deposit-info__input .text {
        margin-bottom: 0;
    }

    .total-amount {
        border-top: 1px solid var(--border-color);
        font-weight: 700;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    /* Gateway card styles */
    .gateway-card {
        background-color: transparent;
    }

    /* Info text styles */
    .info-text {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
    }

    /* Amount input styles */
    .amount-input-group {
        max-width: 200px;
        margin-left: auto;
    }

    .amount-helper-text {
        white-space: nowrap;
        text-align: center;
    }

    /* Responsive styles for all devices */
    @media (max-width: 1199px) {
        .gateway-card .row {
            margin-left: -10px;
            margin-right: -10px;
        }
        .gateway-card .row > [class*="col-"] {
            padding-left: 10px;
            padding-right: 10px;
        }
    }

    /* Additional iPad Pro fix */
    @media only screen and (min-width: 1000px) and (max-width: 1030px) {
        .deposit-row {
            display: flex;
            flex-direction: column;
        }
        .deposit-left-col,
        .deposit-right-col {
            width: 100%;
            flex: 0 0 100%;
            max-width: 100%;
        }
        .payment-system-list {
            margin-bottom: 15px;
        }
    }

    @media (max-width: 991px) {
        .deposit-info {
            padding: 8px 0;
        }
        .payment-item {
            padding: 8px 15px;
        }
        .payment-item__thumb {
            --thumb-width: 110px;
            --thumb-height: 50px;
        }
        .gateway-images-container {
            gap: 8px;
        }
        .gateway-img {
            margin: 3px;
        }
    }

    /* Tablet specific styles */
    @media (min-width: 768px) and (max-width: 991px) {
        .deposit-info__title,
        .deposit-info__input {
            max-width: 100%;
            width: 100%;
            text-align: left;
        }
        .deposit-info__input {
            margin-top: 5px;
            text-align: left;
        }
        .deposit-info {
            flex-direction: column;
            align-items: flex-start;
        }
        .input-group, .amount-input-group {
            max-width: 100% !important;
            margin-left: 0;
        }
        .amount-helper-text {
            text-align: left;
        }
        .gateway-card .row > [class*="col-"] {
            margin-bottom: 20px;
        }
    }

    /* Specific iPad and tablet adjustments */
    @media (width: 768px) and (height: 1024px),
           (device-width: 768px) and (device-height: 1024px),
           (width: 820px) and (height: 1180px),
           (device-width: 820px) and (device-height: 1180px),
           (width: 912px) and (height: 1368px),
           (device-width: 912px) and (device-height: 1368px),
           (width: 853px) and (height: 1280px),
           (device-width: 853px) and (device-height: 1280px),
           (width: 1024px) and (height: 1366px),
           (device-width: 1024px) and (device-height: 1366px),
           (width: 1024px) and (height: 600px),
           (device-width: 1024px) and (device-height: 600px) {
        .card.custom--card {
            margin-bottom: 20px;
        }
        .card-body {
            padding: 20px !important;
        }
        .gateway-card {
            padding: 10px;
        }
    }

    /* iPad Pro and 1024x600 specific fixes */
    @media only screen and (width: 1024px),
           only screen and (device-width: 1024px),
           only screen and (width: 1024px) and (height: 1366px),
           only screen and (width: 1024px) and (height: 600px),
           only screen and (min-width: 1024px) and (max-width: 1024px),
           only screen and (min-device-width: 1024px) and (max-device-width: 1024px),
           only screen and (width: 1024px) and (aspect-ratio: 3/4),
           only screen and (width: 1024px) and (aspect-ratio: 4/3),
           only screen and (width: 1024px) and (aspect-ratio: 16/9),
           only screen and (width: 1024px) and (aspect-ratio: 9/16) {
        .deposit-info__title,
        .deposit-info__input {
            max-width: 100%;
            width: 100%;
            text-align: left;
        }
        .deposit-info__input {
            margin-top: 5px;
            text-align: left;
        }
        .deposit-info {
            flex-direction: column;
            align-items: flex-start;
        }
        .input-group, .amount-input-group {
            max-width: 100% !important;
            margin-left: 0;
        }
        .amount-helper-text {
            text-align: left;
        }
        .deposit-row {
            display: flex;
            flex-direction: column;
        }
        .deposit-left-col,
        .deposit-right-col {
            width: 100%;
            flex: 0 0 100%;
            max-width: 100%;
        }
        .payment-system-list {
            margin-bottom: 15px;
        }
        .gateway-card {
            padding: 5px;
        }
        .card-body {
            padding: 15px !important;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .deposit-info__title,
        .deposit-info__input {
            max-width: 100%;
            width: 100%;
            text-align: left;
        }
        .deposit-info__input {
            margin-top: 5px;
        }
        .deposit-info {
            flex-direction: column;
            align-items: flex-start;
        }
        .input-group, .amount-input-group {
            max-width: 100% !important;
            margin-left: 0;
        }
        .amount-helper-text {
            white-space: normal;
            text-align: left;
        }
        .payment-item__thumb {
            --thumb-width: 90px;
            --thumb-height: 45px;
        }
        .payment-item__name {
            font-size: 14px;
        }
        .gateway-images-card {
            padding: 8px;
        }
        .gateway-img {
            max-width: 30px;
            margin: 2px;
        }
    }

    /* Small mobile styles */
    @media (max-width: 575px) {
        .card-body {
            padding: 15px !important;
        }
        .gateway-card {
            padding: 5px;
        }
        .payment-item {
            padding: 8px 10px;
        }
        .payment-item__thumb {
            --thumb-width: 80px;
            --thumb-height: 40px;
        }
        .payment-item__name {
            font-size: 13px;
        }
        /* Ensure all form text is properly aligned */
        .form-text, .amount-helper-text {
            white-space: normal !important;
            text-align: left !important;
            font-size: 12px;
        }
    }

    /* Extra small mobile styles */
    @media (max-width: 375px) {
        .payment-item__thumb {
            --thumb-width: 70px;
            --thumb-height: 35px;
        }
        .gateway-img {
            max-width: 25px;
            margin: 2px;
        }
    }
</style>
@endpush
