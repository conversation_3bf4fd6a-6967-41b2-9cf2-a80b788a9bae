<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ccl_token_data', function (Blueprint $table) {
            $table->id();
            $table->string('token_name')->default('CCL Token');
            $table->string('token_symbol')->default('CCL');
            $table->string('total_supply')->default('100,000,000 CCL');
            $table->string('blockchain')->default('Ethereum (ERC-20)');
            $table->text('about_content')->nullable();
            $table->json('utility_items')->nullable();
            $table->json('roadmap_items')->nullable();
            $table->json('distribution_items')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ccl_token_data');
    }
};
