<?php

namespace App\Models;

use App\Constants\Status;
use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'features' => 'array',
        'price' => 'decimal:2',
    ];

    public function scopeActive($query)
    {
        return $query->where('status', Status::ENABLE);
    }

    public function purchases()
    {
        return $this->hasMany(PlanPurchase::class);
    }

    public function users()
    {
        return $this->hasManyThrough(User::class, PlanPurchase::class, 'plan_id', 'id', 'id', 'user_id');
    }
}