<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DexscreenerToken;
use App\Models\TokenVote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TokenVoteApiController extends Controller
{
    /**
     * Get vote counts for multiple tokens at once (batch operation)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchGetVoteCounts(Request $request)
    {
        try {
            $request->validate([
                'tokens' => 'required|array',
                'tokens.*.chain_id' => 'required|string',
                'tokens.*.token_address' => 'required|string',
            ]);

            $tokens = $request->tokens;
            $results = [];

            // Get all tokens in a single query to avoid N+1 problem
            $tokenAddresses = collect($tokens)->pluck('token_address')->toArray();
            $chainIds = collect($tokens)->pluck('chain_id')->toArray();
            
            // Create a map of chain_id + token_address to token for faster lookup
            $tokenMap = [];
            $dexTokens = DexscreenerToken::whereIn('token_address', $tokenAddresses)
                ->whereIn('chain_id', $chainIds)
                ->get();
                
            foreach ($dexTokens as $token) {
                $key = $token->chain_id . '_' . $token->token_address;
                $tokenMap[$key] = $token;
            }

            // Process each token in the request
            foreach ($tokens as $token) {
                $chainId = $token['chain_id'];
                $tokenAddress = $token['token_address'];
                $key = $chainId . '_' . $tokenAddress;
                
                // Get the token from our map
                $dexToken = $tokenMap[$key] ?? null;
                
                // Calculate vote count
                $voteCount = 0;
                if ($dexToken) {
                    // Get regular user votes
                    $userVotes = TokenVote::getVoteCountByAddress($chainId, $tokenAddress);
                    
                    // Get admin trend votes
                    $adminTrendVotes = 0;
                    if (isset($dexToken->trend_votes) && is_numeric($dexToken->trend_votes)) {
                        $adminTrendVotes = (int)$dexToken->trend_votes;
                    }
                    
                    // Total votes
                    $voteCount = $userVotes + $adminTrendVotes;
                }
                
                $results[] = [
                    'chain_id' => $chainId,
                    'token_address' => $tokenAddress,
                    'vote_count' => $voteCount
                ];
            }

            return response()->json([
                'success' => true,
                'results' => $results
            ]);
        } catch (\Exception $e) {
            Log::error('Error in batchGetVoteCounts method: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'An error occurred while fetching vote counts'
            ], 500);
        }
    }
}
