<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TokenPromotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'chain_id',
        'token_address',
        'days',
        'start_date',
        'end_date',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created the promotion
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the token being promoted
     */
    public function token()
    {
        return $this->belongsTo(DexscreenerToken::class, 'token_address', 'token_address')
            ->where('chain_id', $this->chain_id);
    }

    /**
     * Scope a query to only include active promotions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where('end_date', '>', now());
    }
}
