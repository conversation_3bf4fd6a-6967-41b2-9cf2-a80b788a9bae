<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ad_impressions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_banner_id');
            $table->date('impression_date');
            $table->integer('count')->default(0);
            $table->timestamps();

            // Add foreign key reference
            $table->foreign('user_banner_id')->references('id')->on('user_banners')->onDelete('cascade');

            // Add unique constraint to prevent duplicate entries for the same banner on the same day
            $table->unique(['user_banner_id', 'impression_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_impressions');
    }
};
