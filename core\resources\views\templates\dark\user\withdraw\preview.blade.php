@extends($activeTemplate . 'layouts.master')

@section('content')
    <div class="card custom--card">
        <h5 class="card-header">
            {{ __($pageTitle) }}
        </h5>
        <div class="card-body">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10 col-sm-12">
                    <ul class="list-group withdraw-preview-list">
                        <li class="list-group-item rounded-0 d-flex justify-content-between">
                            <span class="font-weight-bold">@lang('Requested Amount')</span>
                            <span>{{ showAmount($withdraw->amount) }} {{ strtoupper($withdraw->currency) }}</span>
                        </li>
                        <li class="list-group-item rounded-0 d-flex justify-content-between">
                            <span class="font-weight-bold">@lang('Transaction Id')</span>
                            <span class="trx-id">{{ $withdraw->trx }}</span>
                        </li>
                        <li class="list-group-item rounded-0 d-flex justify-content-between">
                            <span class="font-weight-bold">@lang('Wallet Address')</span>
                            <span class="wallet-address">{{ $withdrawInfo->wallet_address }}</span>
                        </li>
                        <li class="list-group-item rounded-0 d-flex justify-content-between">
                            <span class="font-weight-bold">@lang('Status')</span>
                            <span>
                                @php
                                    echo $withdraw->statusBadge;
                                @endphp
                            </span>
                        </li>
                    </ul>

                    <div class="mt-4">
                        <a href="{{ route('user.withdraw.history') }}" class="btn btn--base w-100">
                            @lang('View My Withdrawal History')
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
<style>
    /* Responsive styles for withdraw preview card */
    .custom--card {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 5px;
        width: 100%;
    }

    .custom--card .card-header {
        background-color: #BE8400;
        color: #ffffff;
        border-radius: 5px 5px 0 0;
        padding: 20px;
    }

    .custom--card .card-body {
        padding: 25px;
    }

    .withdraw-preview-list .list-group-item {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        word-break: break-word;
    }

    .withdraw-preview-list .list-group-item .wallet-address,
    .withdraw-preview-list .list-group-item .trx-id {
        max-width: 60%;
        text-align: right;
        word-break: break-all;
    }

    /* Tablet styles */
    @media (min-width: 768px) and (max-width: 1024px) {
        .custom--card .card-header {
            padding: 18px;
        }

        .custom--card .card-body {
            padding: 20px;
        }

        .withdraw-preview-list .list-group-item {
            padding: 12px;
            font-size: 14px;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .custom--card .card-header {
            padding: 15px;
        }

        .custom--card .card-body {
            padding: 15px;
        }

        .card-title {
            font-size: 16px;
        }

        .withdraw-preview-list .list-group-item {
            padding: 12px 10px;
            font-size: 13px;
        }

        .withdraw-preview-list .list-group-item .wallet-address,
        .withdraw-preview-list .list-group-item .trx-id {
            max-width: 55%;
        }

        .btn--base {
            padding: 8px 15px;
            font-size: 14px;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .custom--card .card-header {
            padding: 12px;
        }

        .custom--card .card-body {
            padding: 12px;
        }

        .card-title {
            font-size: 15px;
        }

        .withdraw-preview-list .list-group-item {
            padding: 10px 8px;
            font-size: 12px;
            flex-direction: column;
            align-items: flex-start;
        }

        .withdraw-preview-list .list-group-item span:last-child,
        .withdraw-preview-list .list-group-item .wallet-address,
        .withdraw-preview-list .list-group-item .trx-id {
            max-width: 100%;
            text-align: left;
            margin-top: 5px;
        }
    }
</style>
@endpush
